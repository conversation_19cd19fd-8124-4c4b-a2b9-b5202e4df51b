<script lang="ts">
  import {
    defineComponent,
    ref,
    onMounted,
    onUnmounted,
    watch,
    nextTick,
  } from 'vue';
  import * as THREE from 'three';
  import { Card } from '@arco-design/web-vue';
  import { IconCommon } from '@arco-design/web-vue/es/icon';
  import cardTitleBg from '@/assets/images/dataScreen/卡片组件/标题渐变背景.png';

  interface PieDataItem {
    value: number;
    name: string;
  }

  /**
   * 配置常量
   */
  const CONFIG = {
    data: [], // 数据从 props 传入
    ring: {
      innerRadius: 0.75,
      outerRadius: 1.6,
      height: 0.2,
    },
    animation: {
      cycleDuration: 12,
      segmentActiveDuration: 3,
      lerpFactor: 0.1,
    },
    camera: {
      frustumSize: 4.5,
    },
  };

  /**
   * 文字精灵管理器类
   */
  class TextSpriteManager {
    cache = new Map<string, THREE.Sprite>();

    create(text: string, fontSize: number, color: string): THREE.Sprite {
      const key = `${text}_${fontSize}_${color}`;
      if (this.cache.has(key)) {
        return this.cache.get(key)!.clone();
      }

      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d')!;

      // 获取设备像素比以提高高DPI显示器上的清晰度
      const dpr = window.devicePixelRatio || 1;
      canvas.width = 1024 * dpr;
      canvas.height = 512 * dpr;

      context.clearRect(0, 0, canvas.width, canvas.height);
      // 根据设备像素比调整字体大小
      context.font = `bold ${fontSize * dpr}px Microsoft YaHei, Arial`;
      context.textAlign = 'center';
      context.textBaseline = 'middle';
      context.shadowColor = 'rgba(0, 0, 0, 0.8)';
      context.shadowBlur = 8;
      context.shadowOffsetX = 2;
      context.shadowOffsetY = 2;
      context.fillStyle = color;
      context.fillText(text, canvas.width / 2, canvas.height / 2);

      const texture = new THREE.Texture(canvas);
      texture.needsUpdate = true;
      // 优化纹理设置以提高清晰度
      texture.minFilter = THREE.LinearFilter;
      texture.magFilter = THREE.LinearFilter;
      texture.generateMipmaps = false;

      const spriteMaterial = new THREE.SpriteMaterial({
        map: texture,
        transparent: true,
        depthTest: true,
        depthWrite: false,
      });

      const sprite = new THREE.Sprite(spriteMaterial);
      const aspectRatio = canvas.width / canvas.height;
      sprite.scale.set(aspectRatio * 0.8, 0.8, 1);

      this.cache.set(key, sprite);
      return sprite.clone();
    }
  }

  /**
   * 主3D图表类
   */
  class Chart3D {
    textSpriteManager = new TextSpriteManager();
    scene = new THREE.Scene();
    renderer!: THREE.WebGLRenderer;
    camera!: THREE.OrthographicCamera;
    segments: THREE.Mesh[] = [];
    groups: Record<string, THREE.Group | THREE.Object3D> = {};
    animationData = {
      time: 0,
      currentActiveIndex: -1,
      hoveredSegment: null as THREE.Mesh | null,
      isMouseInteracting: false,
    };
    raycaster = new THREE.Raycaster();
    mouse = new THREE.Vector2();
    animationFrameId: number | null = null;
    containerRef: HTMLElement;
    electronData: Array<{
      mesh: THREE.Mesh;
      velocity: THREE.Vector3;
      originalOpacity: number;
    }> = [];
    scanLine!: THREE.Line;
    glowRing!: THREE.Mesh;
    percentageSprite!: THREE.Sprite;
    percentSymbolSprite!: THREE.Sprite;
    labelSprite!: THREE.Sprite;

    constructor(containerRef: HTMLElement) {
      this.containerRef = containerRef;
    }

    /**
     * 初始化3D图表
     */
    init() {
      this.setupRenderer();
      this.setupCamera();
      this.setupLights();
      this.createChart();
      this.createDecorations();
      this.setupInteraction();
      this.animate();
    }

    /**
     * 设置渲染器
     */
    setupRenderer() {
      this.renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
        powerPreference: 'high-performance',
      });

      this.renderer.setSize(
        this.containerRef.clientWidth,
        this.containerRef.clientHeight
      );
      this.renderer.setPixelRatio(window.devicePixelRatio);
      this.renderer.setClearColor(0x000000, 0);
      this.renderer.shadowMap.enabled = true;
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
      this.renderer.sortObjects = true;
      this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
      this.renderer.toneMappingExposure = 0.9;

      this.containerRef.appendChild(this.renderer.domElement);
    }

    /**
     * 设置相机
     */
    setupCamera() {
      const aspect =
        this.containerRef.clientWidth / this.containerRef.clientHeight;
      const frustumSize = CONFIG.camera.frustumSize;
      this.camera = new THREE.OrthographicCamera(
        (frustumSize * aspect) / -2,
        (frustumSize * aspect) / 2,
        frustumSize / 2,
        frustumSize / -2,
        0.1,
        1000
      );
      this.camera.position.set(-2.4, -4.8, 4.2);
      this.camera.lookAt(0, 0, 0);
    }

    /**
     * 设置光照
     */
    setupLights() {
      // 环境光 - 与 AlarmComprehensiveChart 一致
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
      this.scene.add(ambientLight);

      // 主光源
      const mainLight = new THREE.DirectionalLight(0xffffff, 0.4);
      mainLight.position.set(5, 8, 8);
      mainLight.castShadow = true;
      mainLight.shadow.mapSize.width = 2048;
      mainLight.shadow.mapSize.height = 2048;
      this.scene.add(mainLight);

      // 填充光
      const fillLight = new THREE.DirectionalLight(0x4dd0e1, 0.4);
      fillLight.position.set(-8, 5, 5);
      this.scene.add(fillLight);

      // 边缘光
      const rimLight = new THREE.DirectionalLight(0xffffff, 0.6);
      rimLight.position.set(5, -5, 10);
      this.scene.add(rimLight);

      // 前方主要光源 - 照亮圆环侧面
      const frontLight = new THREE.DirectionalLight(0xffffff, 1.0);
      frontLight.position.set(0, 0, 10);
      this.scene.add(frontLight);
    }

    /**
     * 创建主图表
     */
    createChart() {
      this.groups.main = new THREE.Group();
      this.groups.text = new THREE.Group();

      // 初始化时不创建扇形，等待数据更新
      // this.createSegments();
      this.createCenterText();

      this.scene.add(this.groups.main);
      this.scene.add(this.groups.text);
    }

    /**
     * 创建环状扇形
     */
    createSegments() {
      console.log('createSegments - CONFIG.data:', CONFIG.data);
      if (CONFIG.data.length === 0) return;

      let currentAngle = -Math.PI / 2;

      CONFIG.data.forEach((item, index) => {
        const angleSpan = (item.percentage / 100) * Math.PI * 2;
        const segment = this.createSegment(
          item,
          index,
          currentAngle,
          angleSpan
        );

        this.groups.main.add(segment);
        this.segments.push(segment);

        currentAngle += angleSpan;
      });
    }

    /**
     * 创建单个扇形段
     */
    createSegment(
      data: any,
      index: number,
      startAngle: number,
      angleSpan: number
    ) {
      const shape = new THREE.Shape();
      const { innerRadius, outerRadius, height } = CONFIG.ring;

      shape.moveTo(
        Math.cos(startAngle) * outerRadius,
        Math.sin(startAngle) * outerRadius
      );
      shape.absarc(
        0,
        0,
        outerRadius,
        startAngle,
        startAngle + angleSpan,
        false
      );
      shape.lineTo(
        Math.cos(startAngle + angleSpan) * innerRadius,
        Math.sin(startAngle + angleSpan) * innerRadius
      );
      shape.absarc(0, 0, innerRadius, startAngle + angleSpan, startAngle, true);
      shape.closePath();

      const geometry = new THREE.ExtrudeGeometry(shape, {
        depth: height,
        bevelEnabled: false,
        steps: 1,
      });

      const material = new THREE.MeshPhysicalMaterial({
        color: data.color,
        transparent: true,
        opacity: 0.85,
        roughness: 0.1,
        metalness: 0.0,
        clearcoat: 1.0,
        clearcoatRoughness: 0.1,
        transmission: 0.3,
        thickness: 0.5,
        ior: 1.5,
        side: THREE.DoubleSide,
        polygonOffset: true,
        polygonOffsetFactor: index * 0.01,
        polygonOffsetUnits: 0.1,
      });

      const segment = new THREE.Mesh(geometry, material);
      segment.position.z = -height / 2 - index * 0.0001;
      segment.castShadow = true;
      segment.receiveShadow = true;

      segment.userData = {
        originalZ: segment.position.z,
        isHovered: false,
        data: data,
        originalColor: data.color,
        targetScale: 1,
        currentScale: 1,
        index: index,
      };

      return segment;
    }

    /**
     * 创建中心文字
     */
    createCenterText() {
      // 默认显示空内容，等待数据加载
      this.percentageSprite = this.textSpriteManager.create(
        '0',
        120,
        '#49B1E0'
      );
      this.percentageSprite.position.set(-0.3, -0.25, 0.5);
      // 增大数字显示尺寸
      this.percentageSprite.scale.set(3.6, 1.5, 1);
      this.groups.text.add(this.percentageSprite);

      // 创建百分号sprite，字体大小为数字的一半
      this.percentSymbolSprite = this.textSpriteManager.create(
        '%',
        80,
        '#49B1E0'
      );
      this.percentSymbolSprite.position.set(0.2, -0.12, 0.5);
      // 百分号缩放比例也相应调整
      this.percentSymbolSprite.scale.set(2.4, 1.0, 1);
      this.groups.text.add(this.percentSymbolSprite);

      this.labelSprite = this.textSpriteManager.create('加载中', 72, '#ffffff');
      this.labelSprite.position.set(-0.3, -0.75, 0.5);
      // 根据canvas尺寸调整sprite缩放
      this.labelSprite.scale.set(2.8, 1.2, 1);
      this.groups.text.add(this.labelSprite);
    }

    /**
     * 更新中心文字
     */
    updateCenterText(percentage: number, label: string, color: string) {
      this.groups.text.remove(this.percentageSprite);
      this.groups.text.remove(this.percentSymbolSprite);
      this.groups.text.remove(this.labelSprite);

      this.percentageSprite = this.textSpriteManager.create(
        percentage.toString(),
        120,
        color
      );
      this.percentageSprite.position.set(-0.2, -0.25, 0.5);
      // 增大数字显示尺寸
      this.percentageSprite.scale.set(3.6, 1.5, 1);
      this.groups.text.add(this.percentageSprite);

      // 创建百分号sprite，字体大小为数字的一半
      this.percentSymbolSprite = this.textSpriteManager.create('%', 80, color);
      this.percentSymbolSprite.position.set(0.2, -0.12, 0.5);
      // 百分号缩放比例也相应调整
      this.percentSymbolSprite.scale.set(2.4, 1.0, 1);
      this.groups.text.add(this.percentSymbolSprite);

      this.labelSprite = this.textSpriteManager.create(label, 72, '#ffffff');
      this.labelSprite.position.set(-0.2, -0.75, 0.5);
      // 根据canvas尺寸调整sprite缩放
      this.labelSprite.scale.set(2.8, 1.2, 1);
      this.groups.text.add(this.labelSprite);
    }

    /**
     * 创建所有装饰效果
     */
    createDecorations() {
      this.createTechRing();
      this.createScanLine();
      this.createGrid();
      this.createGlowRing();
      this.createInnerDots();
      this.createParticles();
      this.createElectronCloud();
      // this.createCornerDecorations(); // 暂时隐藏角落装饰
      this.createHUD();
      this.createDynamicRings();
    }

    /**
     * 创建科技环
     */
    createTechRing() {
      this.groups.techRing = new THREE.Group();

      const ringGeometry = new THREE.RingGeometry(2.0, 2.05, 64);
      const ringMaterial = new THREE.MeshBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.3,
        side: THREE.DoubleSide,
      });
      const ring = new THREE.Mesh(ringGeometry, ringMaterial);
      this.groups.techRing.add(ring);

      // 创建刻度
      const tickGeometry = new THREE.BoxGeometry(0.015, 0.06, 0.02);
      const mainTickGeometry = new THREE.BoxGeometry(0.015, 0.12, 0.02);

      for (let i = 0; i < 60; i++) {
        const angle = (i / 60) * Math.PI * 2;
        const isMainTick = i % 5 === 0;

        const material = new THREE.MeshBasicMaterial({
          color: isMainTick ? 0x00ffff : 0x4169e1,
          transparent: true,
          opacity: isMainTick ? 0.8 : 0.4,
        });

        const tick = new THREE.Mesh(
          isMainTick ? mainTickGeometry : tickGeometry,
          material
        );

        const radius = 2.1;
        tick.position.set(
          Math.cos(angle) * radius,
          Math.sin(angle) * radius,
          0
        );
        tick.rotation.z = angle;

        this.groups.techRing.add(tick);
      }

      // 数字标记
      for (let i = 0; i < 12; i++) {
        const angle = (i / 12) * Math.PI * 2 - Math.PI / 2;
        const numberSprite = this.textSpriteManager.create(
          (i * 30).toString(),
          36,
          '#00FFFF'
        );
        numberSprite.position.set(
          Math.cos(angle) * 2.25,
          Math.sin(angle) * 2.25,
          0.1
        );
        numberSprite.scale.set(0.5, 0.25, 1);
        this.groups.techRing.add(numberSprite);
      }

      this.scene.add(this.groups.techRing);
    }

    /**
     * 创建扫描线
     */
    createScanLine() {
      const geometry = new THREE.BufferGeometry();
      const vertices = new Float32Array([0, 0, 0, 2.1, 0, 0]);
      geometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3));

      const material = new THREE.LineBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.6,
        linewidth: 2,
      });

      this.scanLine = new THREE.Line(geometry, material);
      this.scene.add(this.scanLine);
    }

    /**
     * 创建网格
     */
    createGrid() {
      this.groups.grid = new THREE.Group();

      const ringMaterial = new THREE.MeshBasicMaterial({
        color: 0x1e3a5f,
        transparent: true,
        opacity: 0.2,
        side: THREE.DoubleSide,
      });

      for (let r = 0.5; r <= 2.4; r += 0.25) {
        const geometry = new THREE.RingGeometry(r - 0.005, r + 0.005, 64);
        const ring = new THREE.Mesh(geometry, ringMaterial);
        ring.position.z = -0.1;
        this.groups.grid.add(ring);
      }

      const lineMaterial = new THREE.LineBasicMaterial({
        color: 0x1e3a5f,
        transparent: true,
        opacity: 0.2,
      });

      for (let i = 0; i < 12; i++) {
        const angle = (i / 12) * Math.PI * 2;
        const geometry = new THREE.BufferGeometry();
        const vertices = new Float32Array([
          Math.cos(angle) * 0.5,
          Math.sin(angle) * 0.5,
          -0.1,
          Math.cos(angle) * 2.4,
          Math.sin(angle) * 2.4,
          -0.1,
        ]);
        geometry.setAttribute(
          'position',
          new THREE.BufferAttribute(vertices, 3)
        );

        const line = new THREE.Line(geometry, lineMaterial);
        this.groups.grid.add(line);
      }

      this.scene.add(this.groups.grid);
    }

    /**
     * 创建发光环
     */
    createGlowRing() {
      const geometry = new THREE.RingGeometry(1.85, 1.9, 64);
      const material = new THREE.MeshBasicMaterial({
        color: 0x49b1e0,
        transparent: true,
        opacity: 0.4,
        side: THREE.DoubleSide,
      });
      this.glowRing = new THREE.Mesh(geometry, material);
      this.glowRing.position.z = 0.05;
      this.scene.add(this.glowRing);
    }

    /**
     * 创建内圈圆点
     */
    createInnerDots() {
      this.groups.innerDots = new THREE.Group();
      const sphereGeometry = new THREE.SphereGeometry(0.02, 8, 6);

      for (let i = 0; i < 24; i++) {
        const angle = (i / 24) * Math.PI * 2;

        const material = new THREE.MeshPhongMaterial({
          color: 0xc0c0c0,
          opacity: 0.8,
          transparent: true,
          emissive: 0x222222,
          emissiveIntensity: 0.1,
          shininess: 100,
        });

        const sphere = new THREE.Mesh(sphereGeometry, material);
        sphere.position.set(Math.cos(angle) * 0.6, Math.sin(angle) * 0.6, 0.1);
        sphere.userData = { index: i };

        this.groups.innerDots.add(sphere);
      }

      this.scene.add(this.groups.innerDots);
    }

    /**
     * 创建粒子系统
     */
    createParticles() {
      this.groups.particles = new THREE.Group();
      const particleGeometry = new THREE.SphereGeometry(0.015, 6, 4);

      for (let i = 0; i < 80; i++) {
        const material = new THREE.MeshBasicMaterial({
          color: Math.random() > 0.5 ? 0x76ffff : 0x49b1e0,
          opacity: Math.random() * 0.4 + 0.6,
          transparent: true,
        });

        const particle = new THREE.Mesh(particleGeometry, material);
        const radius = 2.1 + Math.random() * 0.5;
        const angle = Math.random() * Math.PI * 2;

        particle.position.set(
          Math.cos(angle) * radius,
          Math.sin(angle) * radius,
          (Math.random() - 0.5) * 0.8
        );

        particle.userData = {
          speed: 0.005 + Math.random() * 0.01,
          radius: radius,
          angle: angle,
          originalOpacity: material.opacity,
        };

        this.groups.particles.add(particle);
      }

      this.scene.add(this.groups.particles);
    }

    /**
     * 创建电子云
     */
    createElectronCloud() {
      this.groups.electrons = new THREE.Group();
      this.electronData = [];
      const electronGeometry = new THREE.SphereGeometry(0.008, 4, 3);

      for (let i = 0; i < 80; i++) {
        const material = new THREE.MeshBasicMaterial({
          color:
            Math.random() > 0.7
              ? 0x00ffff
              : Math.random() > 0.4
              ? 0x4169e1
              : 0xffffff,
          opacity: Math.random() * 0.6 + 0.3,
          transparent: true,
        });

        const electron = new THREE.Mesh(electronGeometry, material);
        const distance = 2.4 + Math.random() * 1.0;
        const phi = Math.random() * Math.PI * 2;
        const theta = Math.random() * Math.PI;

        electron.position.set(
          distance * Math.sin(theta) * Math.cos(phi),
          distance * Math.sin(theta) * Math.sin(phi),
          distance * Math.cos(theta) * 0.4
        );

        this.electronData.push({
          mesh: electron,
          velocity: new THREE.Vector3(
            (Math.random() - 0.5) * 0.008,
            (Math.random() - 0.5) * 0.008,
            (Math.random() - 0.5) * 0.005
          ),
          originalOpacity: material.opacity,
        });

        this.groups.electrons.add(electron);
      }

      this.scene.add(this.groups.electrons);
    }

    /**
     * 创建角落装饰
     */
    createCornerDecorations() {
      this.groups.corners = new THREE.Group();
      const positions = [
        { x: 2.4, y: 2.4 },
        { x: -2.4, y: 2.4 },
        { x: -2.4, y: -2.4 },
        { x: 2.4, y: -2.4 },
      ];

      const lineMaterial = new THREE.LineBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.6,
      });

      const dotGeometry = new THREE.SphereGeometry(0.03, 8, 6);
      const dotMaterial = new THREE.MeshBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.8,
      });

      positions.forEach((pos, index) => {
        const lineLength = 0.3;
        const geometry = new THREE.BufferGeometry();
        const vertices = new Float32Array([
          pos.x,
          pos.y - lineLength,
          0.1,
          pos.x,
          pos.y,
          0.1,
          pos.x - lineLength * Math.sign(pos.x),
          pos.y,
          0.1,
        ]);
        geometry.setAttribute(
          'position',
          new THREE.BufferAttribute(vertices, 3)
        );

        const line = new THREE.Line(geometry, lineMaterial);
        this.groups.corners.add(line);

        const dot = new THREE.Mesh(dotGeometry, dotMaterial.clone());
        dot.position.set(pos.x, pos.y, 0.1);
        dot.userData = { index };
        this.groups.corners.add(dot);
      });

      this.scene.add(this.groups.corners);
    }

    /**
     * 创建HUD界面
     */
    createHUD() {
      this.groups.hud = new THREE.Group();
      const statusIndicators = ['ONLINE', 'ACTIVE', 'SCANNING', 'CONNECTED'];

      statusIndicators.forEach((status, index) => {
        const angle = (index / 4) * Math.PI * 2 + Math.PI / 8;
        let radius = 2.4;

        if (index === 0 || index === 2) {
          radius = 2.2;
        }

        const statusSprite = this.textSpriteManager.create(
          status,
          24,
          '#00FFFF'
        );
        statusSprite.position.set(
          Math.cos(angle) * radius,
          Math.sin(angle) * radius,
          0.1
        );
        statusSprite.scale.set(0.6, 0.2, 1);
        statusSprite.material.opacity = 0.6;
        statusSprite.userData = {
          baseOpacity: 0.6,
          phase: (index * Math.PI) / 2,
        };
        this.groups.hud.add(statusSprite);

        const indicatorGeometry = new THREE.CircleGeometry(0.02, 8);
        const indicatorMaterial = new THREE.MeshBasicMaterial({
          color: 0x00ff00,
          transparent: true,
          opacity: 0.8,
        });
        const indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
        indicator.position.set(
          Math.cos(angle) * (radius - 0.15),
          Math.sin(angle) * (radius - 0.15),
          0.11
        );
        indicator.userData = { phase: (index * Math.PI) / 2 };
        this.groups.hud.add(indicator);
      });

      this.scene.add(this.groups.hud);
    }

    /**
     * 创建动态光环
     */
    createDynamicRings() {
      this.groups.dynamicRings = new THREE.Group();

      for (let i = 0; i < 3; i++) {
        const radius = 2.15 + i * 0.12;
        const geometry = new THREE.RingGeometry(
          radius - 0.01,
          radius + 0.01,
          64,
          1,
          0,
          Math.PI * 0.5
        );
        const material = new THREE.MeshBasicMaterial({
          color: i === 0 ? 0x00ffff : i === 1 ? 0x49b1e0 : 0x76ffff,
          transparent: true,
          opacity: 0.3 - i * 0.1,
          side: THREE.DoubleSide,
        });
        const arc = new THREE.Mesh(geometry, material);
        arc.userData = {
          speed: 0.01 + i * 0.005,
          direction: i % 2 === 0 ? 1 : -1,
        };
        this.groups.dynamicRings.add(arc);
      }

      this.scene.add(this.groups.dynamicRings);
    }

    /**
     * 设置交互
     */
    setupInteraction() {
      const handleMouseMove = (event: MouseEvent) => {
        const rect = this.containerRef.getBoundingClientRect();
        this.mouse.x =
          ((event.clientX - rect.left) / this.containerRef.clientWidth) * 2 - 1;
        this.mouse.y =
          -((event.clientY - rect.top) / this.containerRef.clientHeight) * 2 +
          1;

        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.segments);

        if (intersects.length > 0) {
          const segment = intersects[0].object as THREE.Mesh;

          if (!this.animationData.isMouseInteracting) {
            this.animationData.isMouseInteracting = true;
            this.resetSegments();
          }

          if (this.animationData.hoveredSegment !== segment) {
            if (this.animationData.hoveredSegment) {
              (
                this.animationData.hoveredSegment
                  .material as THREE.MeshPhysicalMaterial
              ).color.setHex(
                this.animationData.hoveredSegment.userData.originalColor
              );
              this.animationData.hoveredSegment.userData.targetScale = 1;
            }

            this.animationData.hoveredSegment = segment;
            (segment.material as THREE.MeshPhysicalMaterial).color
              .setHex(segment.userData.originalColor)
              .multiplyScalar(1.3);
            segment.userData.targetScale = 1.15;

            // 使用扇形实际的颜色
            const segmentColor = `#${segment.userData.originalColor
              .toString(16)
              .padStart(6, '0')
              .toUpperCase()}`;
            this.updateCenterText(
              Math.round(segment.userData.data.percentage),
              segment.userData.data.label,
              segmentColor
            );
          }
        } else {
          if (this.animationData.isMouseInteracting) {
            this.animationData.isMouseInteracting = false;
            this.animationData.hoveredSegment = null;
            this.resetSegments();
            this.animationData.currentActiveIndex = -1;
          }
        }
      };

      const handleResize = () => {
        const aspect =
          this.containerRef.clientWidth / this.containerRef.clientHeight;
        const frustumSize = CONFIG.camera.frustumSize;

        this.camera.left = (frustumSize * aspect) / -2;
        this.camera.right = (frustumSize * aspect) / 2;
        this.camera.top = frustumSize / 2;
        this.camera.bottom = frustumSize / -2;
        this.camera.updateProjectionMatrix();

        this.renderer.setSize(
          this.containerRef.clientWidth,
          this.containerRef.clientHeight
        );
        this.renderer.setPixelRatio(window.devicePixelRatio);
      };

      this.containerRef.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('resize', handleResize);
    }

    /**
     * 重置所有扇形段
     */
    resetSegments() {
      this.segments.forEach((seg) => {
        (seg.material as THREE.MeshPhysicalMaterial).color.setHex(
          seg.userData.originalColor
        );
        seg.userData.targetScale = 1;
      });
    }

    /**
     * 动画循环
     */
    animate() {
      this.animationFrameId = requestAnimationFrame(() => this.animate());

      this.animationData.time += 0.016;

      this.updateTextAnimation();
      this.updateSegmentAnimation();
      this.updateDecorationAnimation();
      this.updateParticleAnimation();
      this.updateAutoLoop();

      this.renderer.render(this.scene, this.camera);
    }

    /**
     * 更新文字动画
     */
    updateTextAnimation() {
      const time = this.animationData.time;
      this.groups.text.position.z = 0.05 + Math.sin(time * 2) * 0.02;
    }

    /**
     * 更新扇形动画
     */
    updateSegmentAnimation() {
      this.segments.forEach((segment) => {
        const userData = segment.userData;
        userData.currentScale +=
          (userData.targetScale - userData.currentScale) *
          CONFIG.animation.lerpFactor;
        segment.scale.x = userData.currentScale;
        segment.scale.y = userData.currentScale;
      });
    }

    /**
     * 更新装饰动画
     */
    updateDecorationAnimation() {
      const time = this.animationData.time;

      // 科技环旋转
      (this.groups.techRing as THREE.Group).rotation.z -= 0.003;

      // 扫描线
      this.scanLine.rotation.z += 0.01;
      (this.scanLine.material as THREE.LineBasicMaterial).opacity =
        0.3 + Math.sin(time * 4) * 0.3;

      // 发光环脉冲
      this.glowRing.scale.x = 1 + Math.sin(time * 3) * 0.02;
      this.glowRing.scale.y = 1 + Math.sin(time * 3) * 0.02;
      (this.glowRing.material as THREE.MeshBasicMaterial).opacity =
        0.3 + Math.sin(time * 2) * 0.1;

      // 内圈圆点
      (this.groups.innerDots as THREE.Group).rotation.z += 0.02;
      (this.groups.innerDots as THREE.Group).children.forEach(
        (sphere, index) => {
          const pulse = Math.sin(time * 3 + index * 0.5) * 0.3 + 1;
          sphere.scale.setScalar(pulse);
          (
            (sphere as THREE.Mesh).material as THREE.MeshPhongMaterial
          ).emissiveIntensity = 0.2 + Math.sin(time * 2 + index) * 0.2;
        }
      );

      // 角落装饰 - 已暂时隐藏
      /*
      (this.groups.corners as THREE.Group).children.forEach((child, index) => {
        if ((child as THREE.Mesh).isMesh) {
          ((child as THREE.Mesh).material as THREE.MeshBasicMaterial).opacity =
            0.5 + Math.sin(time * 3 + index) * 0.3;
          const scale = 1 + Math.sin(time * 4 + index * 0.5) * 0.2;
          child.scale.setScalar(scale);
        }
      });
      */

      // 动态光环
      (this.groups.dynamicRings as THREE.Group).children.forEach((ring) => {
        ring.rotation.z += ring.userData.speed * ring.userData.direction;
        ((ring as THREE.Mesh).material as THREE.MeshBasicMaterial).opacity =
          0.2 + Math.sin(time * 2 + ring.rotation.z) * 0.1;
      });

      // HUD动画
      (this.groups.hud as THREE.Group).children.forEach((child) => {
        if (child.userData.phase !== undefined) {
          if ((child as THREE.Sprite).isSprite) {
            (child as THREE.Sprite).material.opacity =
              child.userData.baseOpacity +
              Math.sin(time * 3 + child.userData.phase) * 0.2;
          } else if ((child as THREE.Mesh).isMesh) {
            const pulse = Math.sin(time * 4 + child.userData.phase);
            child.scale.setScalar(1 + pulse * 0.3);
            (
              (child as THREE.Mesh).material as THREE.MeshBasicMaterial
            ).opacity = 0.5 + pulse * 0.3;
            (
              (child as THREE.Mesh).material as THREE.MeshBasicMaterial
            ).color.setRGB(0, pulse > 0 ? 1 : 0.5, 0);
          }
        }
      });
    }

    /**
     * 更新粒子动画
     */
    updateParticleAnimation() {
      const time = this.animationData.time;

      // 粒子动画
      (this.groups.particles as THREE.Group).children.forEach((particle) => {
        particle.userData.angle += particle.userData.speed;
        particle.position.x =
          Math.cos(particle.userData.angle) * particle.userData.radius;
        particle.position.y =
          Math.sin(particle.userData.angle) * particle.userData.radius;

        const flicker =
          Math.sin(time * 4 + particle.userData.angle * 10) * 0.3 + 0.7;
        ((particle as THREE.Mesh).material as THREE.MeshBasicMaterial).opacity =
          particle.userData.originalOpacity * flicker;
      });

      // 电子云动画
      this.electronData.forEach((data, i) => {
        const electron = data.mesh;
        const pos = electron.position;

        pos.add(data.velocity);

        const distance = pos.length();
        if (distance > 3.7 || distance < 2.2) {
          data.velocity.multiplyScalar(-1);
        }

        const sparkle = Math.sin(time * 6 + i * 0.5) * 0.4 + 0.6;
        (electron.material as THREE.MeshBasicMaterial).opacity =
          data.originalOpacity * sparkle;
      });
    }

    /**
     * 更新自动循环
     */
    updateAutoLoop() {
      if (
        !this.animationData.isMouseInteracting &&
        this.segments.length > 0 &&
        CONFIG.data.length > 0
      ) {
        const cycleProgress =
          (this.animationData.time % CONFIG.animation.cycleDuration) /
          CONFIG.animation.cycleDuration;
        const progressIndex = Math.floor(cycleProgress * CONFIG.data.length);
        const newActiveIndex =
          (CONFIG.data.length - 1 - progressIndex) % CONFIG.data.length;

        if (newActiveIndex !== this.animationData.currentActiveIndex) {
          if (
            this.animationData.currentActiveIndex >= 0 &&
            this.animationData.currentActiveIndex < this.segments.length
          ) {
            const prevSegment =
              this.segments[this.animationData.currentActiveIndex];
            if (prevSegment && prevSegment.material) {
              (prevSegment.material as THREE.MeshPhysicalMaterial).color.setHex(
                prevSegment.userData.originalColor
              );
              prevSegment.userData.targetScale = 1;
            }
          }

          if (newActiveIndex >= 0 && newActiveIndex < this.segments.length) {
            const segment = this.segments[newActiveIndex];
            if (segment && segment.material) {
              (segment.material as THREE.MeshPhysicalMaterial).color
                .setHex(segment.userData.originalColor)
                .multiplyScalar(1.3);
              segment.userData.targetScale = 1.15;

              // 使用扇形实际的颜色
              const segmentColor = `#${segment.userData.originalColor
                .toString(16)
                .padStart(6, '0')
                .toUpperCase()}`;
              this.updateCenterText(
                Math.round(segment.userData.data.percentage),
                segment.userData.data.label,
                segmentColor
              );
            }
          }

          this.animationData.currentActiveIndex = newActiveIndex;
        }
      }
    }

    /**
     * 更新数据
     */
    updateData(newData: PieDataItem[]) {
      console.log('Chart3D.updateData 接收到数据:', newData);
      // 更新CONFIG数据
      const totalValue = newData.reduce((sum, item) => sum + item.value, 0);
      const colorMap: Record<string, number> = {
        低风险: 0x90ee90,
        中风险: 0x00bfff,
        高风险: 0xffd700,
        铁磁报警: 0x00ffff,
        氧浓度报警: 0xffd700,
        温度报警: 0x00bfff,
        湿度报警: 0x90ee90,
      };

      CONFIG.data = newData.map((item) => ({
        label: item.name,
        percentage:
          totalValue === 0 ? 0 : Math.round((item.value / totalValue) * 100),
        color: colorMap[item.name] || 0x76ffff,
      }));

      // 清除现有扇形
      this.segments.forEach((segment) => {
        this.groups.main.remove(segment);
      });
      this.segments = [];

      // 重新创建扇形
      console.log('准备创建扇形，CONFIG.data:', CONFIG.data);
      this.createSegments();
    }

    /**
     * 销毁
     */
    dispose() {
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
      }

      this.scene.clear();
      this.renderer.dispose();

      if (this.containerRef && this.renderer.domElement.parentNode) {
        this.containerRef.removeChild(this.renderer.domElement);
      }
    }
  }

  export default defineComponent({
    name: 'AlarmAnalysisChart',
    components: {
      ACard: Card,
      IconCommon,
    },
    props: {
      /**
       * 饼图数据
       */
      alarmPieData: {
        type: Array as () => PieDataItem[],
        required: true,
        default: () => [],
      },
      /**
       * 屏幕适配函数
       */
      adaptSize: {
        type: Function,
        required: true,
      },
      /**
       * 当前视图类型
       */
      currentView: {
        type: String,
        default: 'MR',
      },
    },
    setup(props) {
      let chart3D: Chart3D | null = null;
      const containerRef = ref<HTMLElement | null>(null);
      const legendData = ref<Array<{ label: string; color: number; value: number }>>([]);

      /**
       * 初始化3D图表
       */
      const init3DChart = async () => {
        const dataToUse = props.alarmPieData;
        console.log('初始化3D图表，使用数据:', dataToUse);
        if (!containerRef.value || dataToUse.length === 0) return;

        await nextTick();

        chart3D = new Chart3D(containerRef.value);
        chart3D.init();
        chart3D.updateData(dataToUse);
      };

      // 监听数据变化并更新3D图表
      watch(
        () => props.alarmPieData,
        (newValue) => {
          const dataToUse = newValue;
          {
            // 更新图例数据
            const colorMap: Record<string, number> = {
              低风险: 0x90ee90,
              中风险: 0x00bfff,
              高风险: 0xffd700,
              铁磁报警: 0x00ffff,
              氧浓度报警: 0xffd700,
              温度报警: 0x00bfff,
              湿度报警: 0x90ee90,
            };

            legendData.value = dataToUse.map((item) => ({
              label: item.name,
              color: colorMap[item.name] || 0x76ffff,
              value: item.value,
            }));

            if (chart3D) {
              chart3D.updateData(dataToUse);
            } else {
              init3DChart();
            }
          }
        },
        { deep: true, immediate: false }
      );

      onMounted(() => {
        init3DChart();
      });

      onUnmounted(() => {
        if (chart3D) {
          chart3D.dispose();
          chart3D = null;
        }
      });

      return {
        cardTitleBg,
        containerRef,
        legendData,
      };
    },
  });
</script>

<template>
  <a-card
    class="data-card alarm-analysis-card"
    :bordered="false"
    :header-style="{
      border: 'none',
      padding: 0,
      height: '40px',
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      backgroundImage: `url(${cardTitleBg})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      borderRadius: '0 0 0 0',
    }"
    :body-style="{
      padding: 0,
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      borderRadius: '0 0 10px 10px',
      minHeight: currentView === 'MR' ? '334px' : '700px',
    }"
  >
    <template #title>
      <div class="title-content">
        <icon-common class="card-icon" />
        <span class="title-text">铁磁报警分析</span>
      </div>
    </template>
    <div class="card-content">
      <div class="chart-container">
        <div ref="containerRef" class="three-chart-container"></div>
        <!-- 图例 -->
        <div v-if="legendData.length > 0" class="chart-legend">
          <div v-for="item in legendData" :key="item.label" class="legend-item">
            <div
              class="legend-color"
              :style="{
                backgroundColor: `#${item.color.toString(16).padStart(6, '0')}`,
              }"
            ></div>
            <span class="legend-text">{{ item.label }}</span>
            <span class="legend-value">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<style scoped lang="less">
  .alarm-analysis-card {
    background: transparent;
    border: none;
    overflow: hidden;

    :deep(.arco-card-header) {
      border: none;
      background: transparent;
    }

    :deep(.arco-card-body) {
      position: relative;
      background: rgba(13, 45, 76, 0.15);
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 201, 242, 0.1);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(0, 201, 242, 0.05) 0%,
          rgba(0, 201, 242, 0.02) 50%,
          rgba(0, 201, 242, 0) 100%
        );
        border-radius: 8px;
        pointer-events: none;
      }
    }

    .title-content {
      padding: 0 10px;
      height: 100%;
      display: flex;
      align-items: center;
      position: relative;

      .card-icon {
        margin-right: 10px;
        font-size: 18px;
        color: #fff;
      }

      .title-text {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
      }
    }

    .card-content {
      padding: 20px;
      padding-bottom: 50px;

      .chart-container {
        position: relative;
        width: 100%;
        height: 304px;

        .three-chart-container {
          width: 100%;
          height: 100%;
          background: transparent;
          border-radius: 8px;
          overflow: hidden;

          :deep(canvas) {
            border-radius: 8px;
          }
        }

        // 图例样式
        .chart-legend {
          position: absolute;
          left: 50%;
          bottom: -30px;
          transform: translateX(-50%);
          display: flex;
          flex-direction: row;
          gap: 24px;
          align-items: center;
          justify-content: center;
          white-space: nowrap;

          .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #ffffff;

            .legend-color {
              flex-shrink: 0;
              width: 14px;
              height: 14px;
              border-radius: 2px;
            }

            .legend-text {
              font-size: 0.85rem;
            }

            .legend-value {
              font-weight: bold;
              color: #4dd0e1;
              font-size: 0.85rem;
              margin-left: 4px;
            }
          }
        }
      }
    }
  }
</style>
