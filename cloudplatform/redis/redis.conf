# CloudPlatform Redis 开发环境配置文件
# 基于 Redis 7.2 LTS 版本优化

# ============================================================================
# 网络配置
# ============================================================================

# 绑定地址 - 允许所有接口访问（开发环境）
bind 0.0.0.0

# 端口配置
port 6379

# 超时设置（秒）- 0表示禁用超时
timeout 300

# TCP keepalive 设置
tcp-keepalive 300

# ============================================================================
# 安全配置
# ============================================================================

# 密码认证（开发环境使用简单密码）
requirepass xiaoye71

# 禁用危险命令（生产环境建议）
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command DEBUG ""

# ============================================================================
# 内存管理
# ============================================================================

# 最大内存限制（512MB，适合开发环境）
maxmemory 512mb

# 内存淘汰策略 - LRU算法淘汰最近最少使用的键
maxmemory-policy allkeys-lru

# ============================================================================
# 持久化配置
# ============================================================================

# RDB 快照配置
# 900秒内至少1个键变化时保存
save 900 1
# 300秒内至少10个键变化时保存
save 300 10
# 60秒内至少10000个键变化时保存
save 60 10000

# RDB 文件名
dbfilename dump.rdb

# 工作目录
dir /data

# RDB 文件压缩
rdbcompression yes

# RDB 文件校验
rdbchecksum yes

# AOF 持久化配置
appendonly yes
appendfilename "appendonly.aof"

# AOF 同步策略 - 每秒同步一次（平衡性能和安全性）
appendfsync everysec

# AOF 重写配置
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# ============================================================================
# 日志配置
# ============================================================================

# 日志级别：debug, verbose, notice, warning
loglevel notice

# 日志文件（空表示输出到标准输出）
logfile ""

# ============================================================================
# 客户端配置
# ============================================================================

# 最大客户端连接数
maxclients 10000

# ============================================================================
# 性能优化
# ============================================================================

# 数据库数量
databases 16

# 哈希表配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# 列表配置
list-max-ziplist-size -2
list-compress-depth 0

# 集合配置
set-max-intset-entries 512

# 有序集合配置
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog 配置
hll-sparse-max-bytes 3000

# ============================================================================
# 慢查询日志
# ============================================================================

# 慢查询阈值（微秒）- 10毫秒
slowlog-log-slower-than 10000

# 慢查询日志最大长度
slowlog-max-len 128

# ============================================================================
# 事件通知
# ============================================================================

# 键空间通知配置（开发环境可以启用）
notify-keyspace-events "Ex"

# ============================================================================
# 高级配置
# ============================================================================

# 禁用保护模式（开发环境）
protected-mode no

# TCP backlog 设置
tcp-backlog 511

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 停止写入阈值
stop-writes-on-bgsave-error yes

# Lua 脚本超时时间（毫秒）
lua-time-limit 5000
