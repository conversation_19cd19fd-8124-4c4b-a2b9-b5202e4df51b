# CloudPlatform ZLMediaKit 流媒体服务器配置文件
# 适用于生产环境部署，支持多种流媒体协议

[api]
# API 调试模式（生产环境建议设为0）
apiDebug=0
# API 前缀路径
api_prefix=/api
# 默认截图文件
defaultSnap=./www/logo.png
# 下载根目录
downloadRoot=./www
# 启用 HTTP API
enable=1
# 启用跨域支持
enable_cors=1
# HTTP API 端口
port=80
# API 访问密钥（生产环境请修改）
secret=xiaoye71
# 截图存储根目录
snapRoot=./www/snap/

[cluster]
# 集群源 URL（如果使用集群模式）
origin_url=
# 重试次数
retry_count=3
# 超时时间（秒）
timeout_sec=15

[ffmpeg]
# FFmpeg 可执行文件路径
bin=/usr/bin/ffmpeg
# FFmpeg 命令模板
cmd=%s -re -i %s -c:a aac -strict -2 -ar 44100 -ab 48k -c:v libx264 -f flv %s
# FFmpeg 日志级别
log=./ffmpeg/ffmpeg.log

[general]
# 是否启用虚拟主机
enableVhost=0
# 流量统计事件流间隔（秒）
flowThreshold=1024
# 最大等待时间（毫秒）
maxStreamWaitMS=15000
# 修改配置文件后是否重启
modifyStamp=1665024000
# 发布者超时时间（秒）
publishToHls=1
# 发布者超时时间（秒）
publishToMP4=0
# 重置配置文件
resetConfig=0
# 流无人观看时的超时时间（秒）
streamNoneReaderDelayMS=20000

[hls]
# HLS 切片时长（秒）
segDur=2
# HLS 播放列表保留的切片数量
segNum=3
# HLS 切片保留数量
segRetain=5
# HLS 文件路径
filePath=./www
# 是否广播 HLS
broadcastRecordTs=0
# 删除延迟（秒）
deleteDelaySec=10

[hook]
# 事件回调地址
admin_params=secret=CloudPlatform2024SecretKey
# 启用事件回调
enable=1
# 回调超时时间（秒）
timeoutSec=10
# 流量统计事件
on_flow_report=
# 播放事件
on_play=
# 发布事件
on_publish=
# 录制完成事件
on_record_mp4=
# 录制开始事件
on_record_ts=
# RTP 服务器超时事件
on_rtsp_realm=
# 服务器启动事件
on_server_started=
# 流变化事件
on_stream_changed=
# 流无人观看事件
on_stream_none_reader=
# 流不存在事件
on_stream_not_found=

[http]
# HTTP 服务器字符集
charSet=utf-8
# 连接超时时间（秒）
connTimeoutSec=10
# 目录列表
dirMenu=1
# 启用 HTTP 服务器
enable=1
# 保持连接时间（秒）
keepAliveSecond=30
# 最大连接数
maxCount=100
# 监听端口
port=80
# 根目录
rootPath=./www
# 发送缓冲区大小
sendBufSize=65536
# SSL 证书文件
sslCertFile=./ssl.crt
# SSL 私钥文件
sslKeyFile=./ssl.key
# SSL 端口
sslPort=443

[multicast]
# 组播地址
addrMax=239.255.255.255
# 组播地址最小值
addrMin=239.0.0.0
# 组播端口最大值
portMax=60000
# 组播端口最小值
portMin=50000
# 组播 TTL
udpTTL=64

[record]
# 录制应用名称
appName=record
# 录制文件路径
filePath=./www/record
# 录制文件后缀
fileSecond=3600
# 快速启动
fastStart=0
# 录制 HLS
recordHLS=1
# 录制 MP4
recordMP4=0

[rtmp]
# RTMP 握手超时时间（秒）
handshakeSecond=15
# RTMP 保持连接时间（秒）
keepAliveSecond=15
# 修改时间戳
modifyStamp=0
# RTMP 端口
port=1935
# SSL 端口
sslPort=19350

[rtp]
# 音频 MTUSIZE
audioMtuSize=1400
# 清除过期的 RTP 包时间间隔（秒）
clearCount=10
# 循环池大小
cycleMS=46
# 最大 RTP 包时间（毫秒）
maxRtpCount=50
# 视频 MTUSIZE
videoMtuSize=1400

[rtsp]
# 认证基本信息
authBasic=0
# 直接代理模式
directProxy=1
# 握手超时时间（秒）
handshakeSecond=15
# 保持连接时间（秒）
keepAliveSecond=15
# RTSP 端口
port=554
# SSL 端口
sslPort=322

[shell]
# 最大等待时间（秒）
maxReqSize=4096
# Shell 端口
port=9000

[srt]
# SRT 延迟时间（毫秒）
latencyMul=4
# 包丢失阈值
pktDropThreshold=5
# SRT 端口
port=9000
# 超时时间（秒）
timeoutSec=5

[webrtc]
# 候选地址
candidateIP=
# 最小端口
minPort=50000
# 最大端口
maxPort=60000
# 首选编解码器
preferredCodecA=PCMA,PCMU,opus,mpeg4-generic
preferredCodecV=H264,H265,AV1,VP9,VP8
# WebRTC 端口
port=8000
# RTP 端口
rtpPort=8000
# 超时时间（秒）
timeoutSec=15
