# CloudPlatform 一体化部署 Docker Compose 配置
# 包含完整的服务栈：前端、后端、缓存、流媒体服务
# 实现"一键部署"的完整解决方案

# 定义自定义网络
networks:
  cloudplatform:
    driver: bridge
    name: cloudplatform
    # 使用自动IP分配，服务通过别名进行通信
  nginx_network:
    external: true
    name: nginx_network

# 定义数据卷（完整服务栈）
volumes:
  # 应用日志持久化
  app-logs:
    driver: local
    name: cloudplatform-app-logs
  # 前端日志持久化
  web-logs:
    driver: local
    name: cloudplatform-web-logs
  # Redis 数据持久化
  redis-data:
    driver: local
    name: cloudplatform-redis-data
  # ZLMediaKit 媒体文件存储
  zlmediakit-media:
    driver: local
    name: cloudplatform-zlmediakit-media
  # ZLMediaKit 录制文件存储
  zlmediakit-record:
    driver: local
    name: cloudplatform-zlmediakit-record
  # ZLMediaKit 日志存储
  zlmediakit-logs:
    driver: local
    name: cloudplatform-zlmediakit-logs

# 注意：MySQL 使用外部数据库服务器（**************）

services:
  # ========================================================================
  # 数据库服务（外部数据库配置）
  # ========================================================================

  # ========================================================================
  # 缓存服务（已分离到独立配置文件）
  # ========================================================================

  # ========================================================================
  # 后端服务
  # ========================================================================

  # CloudPlatform Spring Boot 后端服务
  cloudplatform-srv:
    image: cloudmagnet/cloudplatform-srv:1.0.4
    container_name: cloudplatform-srv
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
    environment:
      # Spring 配置文件
      SPRING_PROFILES_ACTIVE: docker

      # 数据库连接配置（外部数据库服务器）
      SPRING_DATASOURCE_URL: *******************************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: xiaoye71

      # Redis 连接配置
      SPRING_DATA_REDIS_HOST: redis
      SPRING_DATA_REDIS_PORT: 6379
      SPRING_DATA_REDIS_PASSWORD: cloudplatform123

      # 容器感知的 JVM 配置（G1GC + 堆内存限制）
      JAVA_OPTS: >-
        -Duser.timezone=Asia/Shanghai
        -Xms1024m
        -Xmx2048m
        -XX:+UseG1GC
        -XX:MaxGCPauseMillis=200
        -XX:G1HeapRegionSize=16m
        -XX:+UseContainerSupport
        -XX:MaxRAMPercentage=75.0
        -XX:InitialRAMPercentage=50.0
        -XX:+ExitOnOutOfMemoryError
        -XX:+HeapDumpOnOutOfMemoryError
        -XX:HeapDumpPath=/app/heapdumps/
        -Xlog:gc:/app/logs/gc.log:time,tags

      # 应用配置
      SERVER_PORT: 8081
      TZ: Asia/Shanghai

      # 日志配置
      LOGGING_LEVEL_ROOT: INFO
      LOGGING_LEVEL_LAB_CLOUDMAGNET: DEBUG
      LOGGING_FILE_NAME: /app/logs/cloudplatform-srv.log

      # Spring Boot Actuator 健康检查配置
      MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: health,info,metrics,prometheus
      MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: when_authorized
      MANAGEMENT_SERVER_PORT: 8081

      # 天气 API 配置
      WEATHER_API_APPCODE: 61dd02402f3141169a02d743f746fc2c
      WEATHER_API_AREACODE: 320104
    volumes:
      # 日志文件持久化
      - app-logs:/app/logs
      # 配置文件挂载
      - ./app/config:/app/config:ro
      # 堆转储文件存储
      - app-logs:/app/heapdumps
    # 端口仅内部暴露，通过nginx反向代理访问
    expose:
      - "8081"
    networks:
      cloudplatform:
        aliases:
          - backend
          - api
          - cloudplatform-srv
      nginx_network:
        aliases:
          - cloudplatform-srv
    # 资源限制（生产环境配置）
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
    # 安全配置
    security_opt:
      - no-new-privileges:true

  # ========================================================================
  # 前端服务
  # ========================================================================

  # CloudPlatform Vue.js 前端服务
  cloudplatform-web:
    image: cloudmagnet/cloudplatform-web:1.0.6
    container_name: cloudplatform-web
    restart: unless-stopped
    depends_on:
      cloudplatform-srv:
        condition: service_started
    environment:
      # Node.js 环境配置
      NODE_ENV: production
      TZ: Asia/Shanghai

      # 前端应用配置 - 适配nginx反向代理
      VITE_API_BASE_URL: https://cloudplatform.cloudmagnet.lab/api
      VITE_APP_TITLE: "CloudPlatform Dashboard"
    volumes:
      # 前端日志持久化
      - web-logs:/app/logs
    # 端口仅内部暴露，通过nginx反向代理访问
    expose:
      - "3000"
    networks:
      cloudplatform:
        aliases:
          - frontend
          - web
          - cloudplatform-web
      nginx_network:
        aliases:
          - cloudplatform-web
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"
    # 安全配置
    security_opt:
      - no-new-privileges:true

  # ========================================================================
  # 缓存服务
  # ========================================================================

  # Redis 7.2 LTS 缓存数据库
  redis:
    image: redis:7.2-alpine
    container_name: cloudplatform-redis
    restart: unless-stopped
    # Redis 仅内部访问，不对外暴露端口
    expose:
      - "6379"
    volumes:
      # 数据持久化：将 Redis 数据目录挂载到本地卷
      - redis-data:/data
      # Redis 配置文件挂载（只读模式）
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    environment:
      # 时区设置 - 与 CloudPlatform 其他服务保持一致
      - TZ=Asia/Shanghai
      # Redis 密码认证
      - REDIS_PASSWORD=xiaoye71
    command: >
      redis-server /usr/local/etc/redis/redis.conf
    # 健康检查配置
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "cloudplatform123", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    # 网络配置 - 使用自动IP分配
    networks:
      cloudplatform:
        aliases:
          - redis
          - cache
          - cloudplatform-redis
      nginx_network:
        aliases:
          - redis
    # 资源限制（适合生产环境）
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # 安全配置
    security_opt:
      - no-new-privileges:true

  # ========================================================================
  # 流媒体服务
  # ========================================================================

  # ZLMediaKit 流媒体服务器
  zlmediakit:
    # 使用最新的稳定版本 - 基于2024年最新的稳定构建
    # 注意：ZLMediaKit主要维护master分支，这是推荐的生产版本
    image: zlmediakit/zlmediakit:master
    container_name: cloudplatform-zlmediakit
    restart: unless-stopped
    # 端口配置 - 适配nginx反向代理环境
    ports:
      # 流媒体协议端口需要直接暴露（nginx无法代理UDP协议）
      - "1935:1935"   # RTMP 推流端口
      - "554:554"     # RTSP 流媒体端口
      - "8000:8000"   # HTTP-FLV 直播端口
      - "8080:8080"   # WebRTC 端口
      - "10000-10100:10000-10100/udp"  # RTP 端口范围
      - "9000:9000/udp"  # SRT 端口
    # HTTP API 通过nginx代理，仅内部暴露
    expose:
      - "80"          # HTTP API 端口
    # 数据卷挂载
    volumes:
      # 配置文件挂载（只读模式确保安全性）
      - ./zlmediakit/config:/opt/media/conf:ro
      # 媒体文件存储（直播录制、点播文件等）
      - zlmediakit-media:/opt/media/www
      # 录制文件专用存储
      - zlmediakit-record:/opt/media/www/record
      # 日志文件存储（Docker数据卷）
      - zlmediakit-logs:/opt/media/log
      # 日志文件本地目录映射（便于查看和调试）
      - ./logs/zlmediakit:/opt/media/log/local
      # HLS 切片文件存储
      - zlmediakit-media:/opt/media/www/hls
    # 环境变量配置
    environment:
      # 时区设置 - 与 CloudPlatform 其他服务保持一致
      - TZ=Asia/Shanghai
      # 服务器配置
      - ZLM_API_SECRET=xiaoye71
      - ZLM_HTTP_PORT=80
      - ZLM_RTMP_PORT=1935
      - ZLM_RTSP_PORT=554
      - ZLM_WEBRTC_PORT=8080
      # 性能优化配置
      - ZLM_THREAD_NUM=4
      - ZLM_MAX_CONNECTIONS=1000
      # 录制配置
      - ZLM_RECORD_PATH=/opt/media/www/record
      - ZLM_HLS_PATH=/opt/media/www/hls
    # 健康检查配置
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/index/api/getServerConfig?secret=CloudPlatform2024SecretKey"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # 资源限制（生产环境配置）
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'
    # 网络配置
    networks:
      cloudplatform:
        aliases:
          - media
          - streaming
          - zlmediakit
          - cloudplatform-zlmediakit
      nginx_network:
        aliases:
          - zlmediakit
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
    # 安全配置
    security_opt:
      - no-new-privileges:true
    # 用户配置（非 root 用户运行）
    user: "1000:1000"

# ============================================================================
# 使用说明 - 一体化部署
# ============================================================================

# 一键启动所有服务（推荐）：
# docker-compose up -d

# 启动特定服务：
# docker-compose up -d redis                # 仅启动 Redis 缓存
# docker-compose up -d cloudplatform-srv    # 仅启动后端服务
# docker-compose up -d cloudplatform-web    # 仅启动前端服务
# docker-compose up -d zlmediakit           # 仅启动流媒体服务

# 查看所有服务状态：
# docker-compose ps

# 查看特定服务日志：
# docker-compose logs -f cloudplatform-srv  # 后端服务日志
# docker-compose logs -f cloudplatform-web  # 前端服务日志
# docker-compose logs -f redis              # Redis 服务日志
# docker-compose logs -f zlmediakit         # 流媒体服务日志

# 查看 ZLMediaKit 详细日志文件：
# tail -f logs/zlmediakit/*.log             # 查看本地映射的日志文件
# ls -la logs/zlmediakit/                   # 列出所有日志文件

# 停止所有服务：
# docker-compose down

# 停止并删除数据卷（谨慎使用）：
# docker-compose down -v

# 重新构建并启动：
# docker-compose up -d --build

# 扩展特定服务实例：
# docker-compose up -d --scale cloudplatform-srv=2

# 重启特定服务：
# docker-compose restart cloudplatform-srv

# ============================================================================
# 服务访问地址 - nginx反向代理部署
# ============================================================================

# 通过域名访问（nginx反向代理）：
# 前端 Web 界面：https://cloudplatform.cloudmagnet.lab
# 后端 API 接口：https://cloudplatform.cloudmagnet.lab/api
# 后端健康检查：https://cloudplatform.cloudmagnet.lab/api/actuator/health
# ZLMediaKit 管理：https://cloudplatform.cloudmagnet.lab/media

# 直接访问流媒体协议端口：
# - RTMP 推流：rtmp://cloudplatform.cloudmagnet.lab:1935/live/stream_name
# - RTSP 播放：rtsp://cloudplatform.cloudmagnet.lab:554/live/stream_name
# - HTTP-FLV：http://cloudplatform.cloudmagnet.lab:8000/live/stream_name.flv
# - WebRTC：http://cloudplatform.cloudmagnet.lab:8080/index/webrtc/play.html
# - HLS 播放：https://cloudplatform.cloudmagnet.lab/media/hls/stream_name/index.m3u8

# 内部服务访问（仅容器间）：
# - Redis 缓存：redis:6379
# - MySQL 数据库：**************:3306/cloudmagnet

# nginx 反向代理配置参考（使用服务名/别名）：
# 前端：proxy_pass http://cloudplatform-web:3000;
# 后端：proxy_pass http://cloudplatform-srv:8081;
# 流媒体：proxy_pass http://zlmediakit:80;

# ============================================================================
# 生产环境部署注意事项 - 一体化部署
# ============================================================================

# 1. 安全配置：
#    - 修改所有默认密码（Redis、ZLMediaKit API 密钥）
#    - 配置 JVM 安全参数
#    - 启用 HTTPS（通过 nginx 代理）
#    - 定期更新所有服务镜像版本
#    - 配置防火墙规则开放必要端口

# 2. 性能优化：
#    - 根据服务器配置调整各服务资源限制
#    - 配置日志轮转策略防止磁盘空间不足
#    - 监控所有服务性能指标
#    - 设置合适的健康检查间隔
#    - 优化 JVM 堆内存和 GC 参数

# 3. 数据备份：
#    - 定期备份 Redis 数据
#    - 备份应用日志文件
#    - 备份所有服务配置文件
#    - 备份 ZLMediaKit 录制文件
#    - 备份前端静态资源

# 4. 监控告警：
#    - 配置所有服务状态监控
#    - 设置内存和 CPU 使用告警
#    - 监控服务日志错误信息
#    - 配置服务自动重启策略
#    - 监控网络连通性和端口可用性

# 5. 服务依赖管理：
#    - 确保外部 MySQL 数据库可用性
#    - 配置服务间依赖关系和启动顺序
#    - 设置服务故障转移策略
#    - 实施健康检查和自动恢复

# 6. 一体化部署优势：
#    - 单一命令启动所有服务
#    - 统一的网络和数据卷管理
#    - 简化的服务发现和通信
#    - 完整的功能栈部署
#    - 便于版本控制和回滚
