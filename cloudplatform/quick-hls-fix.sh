#!/bin/bash

# CloudPlatform HLS 快速修复脚本
# 针对当前 404 错误的快速修复

set -e

echo "=== CloudPlatform HLS 快速修复 ==="
echo "修复时间: $(date)"
echo ""

# 服务器配置
SERVER="cloudplatform.cloudmagnet.lab"
USER="root"

echo "🎯 针对问题: 前端视频播放 404 错误"
echo "📋 修复策略:"
echo "1. 修复 ZLMediaKit HLS 路径配置"
echo "2. 调整 Nginx 代理规则"
echo "3. 重启相关服务"
echo "4. 测试验证"
echo ""

# 1. 直接在服务器上修复 ZLMediaKit 配置
echo "🔧 修复 ZLMediaKit 配置..."
ssh $USER@$SERVER "
cd /root/cloudplatform

# 备份当前配置
cp zlmediakit/config/config.ini zlmediakit/config/config.ini.backup-\$(date +%H%M%S)

# 修复 HLS 配置
cat > zlmediakit/config/config.ini << 'EOF'
[api]
apiDebug=0
enable=1
port=80
secret=xiaoye71

[general]
enableVhost=0
publishToHls=1

[hls]
enable=1
segDur=2
segNum=3
segRetain=5
filePath=./www/hls
save_file=1

[http]
enable=1
port=80
rootPath=./www

[rtsp]
port=554
directProxy=1

[rtmp]
port=1935
EOF

echo '✅ ZLMediaKit 配置已修复'
"

# 2. 修复 Nginx 配置
echo "🔧 修复 Nginx 配置..."
ssh $USER@$SERVER "
# 备份当前 Nginx 配置
cp /root/nginx-cloudplatform.conf /root/nginx-cloudplatform.conf.backup-\$(date +%H%M%S)

# 修复 HLS 路径代理
sed -i 's|rewrite ^/hls/(.*)$ /\$1 break;|# 直接代理，不重写路径|' /root/nginx-cloudplatform.conf

echo '✅ Nginx 配置已修复'
"

# 3. 重启服务
echo "🔄 重启服务..."
ssh $USER@$SERVER "
cd /root/cloudplatform

echo '停止服务...'
docker-compose stop zlmediakit nginx

echo '创建 HLS 目录...'
mkdir -p zlmediakit/media/hls
chmod 755 zlmediakit/media/hls

echo '启动 ZLMediaKit...'
docker-compose up -d zlmediakit
sleep 10

echo '启动 Nginx...'
docker-compose up -d nginx
sleep 5

echo '检查服务状态:'
docker-compose ps | grep -E '(zlmediakit|nginx)'
"

# 4. 测试验证
echo "🧪 测试验证..."
sleep 5

# 测试 ZLMediaKit API
echo "测试 ZLMediaKit API..."
API_TEST=$(curl -s -X POST "https://$SERVER/zlm/index/api/getServerConfig" \
    -H "Content-Type: application/json" \
    -d '{"secret":"xiaoye71"}' | jq -r '.code' 2>/dev/null || echo "error")

if [ "$API_TEST" = "0" ]; then
    echo "✅ ZLMediaKit API 正常"
else
    echo "❌ ZLMediaKit API 异常"
fi

# 创建测试流
echo "创建测试 RTSP 流..."
TEST_RTSP="rtsp://admin:yunci123@**************/Streaming/Channels/101"
TEST_STREAM_ID=$(echo -n "$TEST_RTSP" | base64 | tr '+/' '-_' | tr -d '=')

PROXY_RESULT=$(curl -s -X POST "https://$SERVER/zlm/index/api/addStreamProxy" \
    -H "Content-Type: application/json" \
    -d "{
        \"secret\":\"xiaoye71\",
        \"vhost\":\"__defaultVhost__\",
        \"app\":\"CloudPlatform\",
        \"stream\":\"$TEST_STREAM_ID\",
        \"url\":\"$TEST_RTSP\"
    }")

echo "代理创建结果: $PROXY_RESULT"

# 测试 HLS 访问
echo "测试 HLS 访问..."
sleep 3

HLS_URLS=(
    "https://$SERVER/hls/CloudPlatform/$TEST_STREAM_ID/hls.m3u8"
    "https://$SERVER/CloudPlatform/$TEST_STREAM_ID/hls.m3u8"
    "https://$SERVER/zlm/CloudPlatform/$TEST_STREAM_ID/hls.m3u8"
)

for url in "${HLS_URLS[@]}"; do
    echo "测试: $url"
    status=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    if [ "$status" = "200" ]; then
        echo "✅ 可访问 (状态码: $status)"
        echo "🎉 找到可用的 HLS 路径: $url"
        break
    else
        echo "❌ 不可访问 (状态码: $status)"
    fi
done

# 显示服务器日志
echo ""
echo "📋 最近的服务器日志:"
ssh $USER@$SERVER "
cd /root/cloudplatform
echo '=== ZLMediaKit 日志 ==='
docker logs --tail 10 cloudplatform-zlmediakit

echo ''
echo '=== Nginx 错误日志 ==='
docker exec cloudplatform-nginx tail -5 /var/log/nginx/cloudplatform.error.log 2>/dev/null || echo '无法读取 Nginx 日志'
"

echo ""
echo "🎯 快速修复完成！"
echo ""
echo "📝 验证步骤:"
echo "1. 访问 https://$SERVER 打开前端"
echo "2. 进入监控页面测试视频播放"
echo "3. 检查浏览器控制台是否还有错误"
echo ""
echo "🔍 如果问题仍然存在:"
echo "1. 检查摄像头是否在线: ping **************"
echo "2. 测试 RTSP 连接: telnet ************** 554"
echo "3. 查看详细日志: ssh $USER@$SERVER 'cd /root/cloudplatform && docker-compose logs -f zlmediakit'"
echo ""
echo "修复完成时间: $(date)"
