#!/bin/bash

# ZLMediaKit 权限修复脚本
# 解决 "mkdir /opt/media/bin/log/ failed: permission denied" 问题

set -e

echo "🔧 开始修复 ZLMediaKit 权限问题..."

# 检查容器是否运行
if ! docker ps | grep -q "cloudplatform-zlmediakit"; then
    echo "❌ ZLMediaKit 容器未运行，请先启动容器"
    echo "   运行命令: docker-compose up -d zlmediakit"
    exit 1
fi

echo "📁 在容器内创建必要的目录并设置权限..."

# 进入容器并修复权限
docker exec cloudplatform-zlmediakit sh -c "
    # 创建 bin/log 目录
    mkdir -p /opt/media/bin/log
    
    # 设置正确的所有者和权限
    chown -R 1000:1000 /opt/media/bin
    chmod -R 755 /opt/media/bin
    
    # 验证目录创建成功
    ls -la /opt/media/bin/
    
    echo '✅ 权限修复完成'
"

echo "🔄 重启 ZLMediaKit 容器以应用更改..."
docker-compose restart zlmediakit

echo "⏳ 等待容器启动..."
sleep 5

# 检查容器状态
if docker ps | grep -q "cloudplatform-zlmediakit"; then
    echo "✅ ZLMediaKit 容器已成功重启"
    echo "📋 查看容器日志以确认问题已解决:"
    echo "   docker-compose logs -f zlmediakit"
else
    echo "❌ 容器重启失败，请检查配置"
    exit 1
fi

echo "🎉 权限修复完成！"
