#!/bin/bash

# 服务器端 ZLMediaKit 修复脚本
# 解决容器重启循环和权限问题
# 适用于生产服务器环境
# 作者: AI Assistant
# 日期: 2025-06-20

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 容器名称
CONTAINER_NAME="cloudplatform-zlmediakit"
COMPOSE_FILE="docker-compose.yml"

# 检查运行环境
check_environment() {
    log_info "检查运行环境..."
    
    # 检查是否在正确的目录
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "未找到 docker-compose.yml 文件"
        log_info "请确保在正确的目录运行此脚本"
        exit 1
    fi
    
    # 检查是否有 docker-compose 命令
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose 命令未找到"
        exit 1
    fi
    
    # 检查是否有 root 权限
    if [[ $EUID -ne 0 ]]; then
        log_warning "建议以 root 权限运行此脚本"
    fi
    
    log_success "环境检查通过"
}

# 分析容器状态
analyze_container_status() {
    log_info "分析容器状态..."
    
    if docker ps -a --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        local status=$(docker inspect ${CONTAINER_NAME} --format='{{.State.Status}}')
        local restart_count=$(docker inspect ${CONTAINER_NAME} --format='{{.RestartCount}}')
        local exit_code=$(docker inspect ${CONTAINER_NAME} --format='{{.State.ExitCode}}')
        
        log_info "容器状态: $status"
        log_info "重启次数: $restart_count"
        log_info "退出码: $exit_code"
        
        if [[ "$status" == "restarting" ]]; then
            log_error "容器处于重启循环状态"
            return 1
        elif [[ "$restart_count" -gt 100 ]]; then
            log_warning "容器重启次数过多 ($restart_count)"
            return 1
        elif [[ "$exit_code" == "139" ]]; then
            log_error "容器因段错误退出 (可能是权限或配置问题)"
            return 1
        fi
    else
        log_warning "容器不存在"
        return 1
    fi
    
    return 0
}

# 备份配置文件
backup_config() {
    log_info "备份配置文件..."
    
    local backup_file="${COMPOSE_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$COMPOSE_FILE" "$backup_file"
    
    log_success "配置文件已备份到: $backup_file"
}

# 修复配置文件
fix_config() {
    log_info "修复 docker-compose.yml 配置..."
    
    # 检查是否需要修复
    if grep -q "zlmediakit-logs:/opt/media/bin/log" "$COMPOSE_FILE"; then
        log_success "配置文件已经是正确的"
        return 0
    fi
    
    # 执行修复
    if grep -q "zlmediakit-logs:/opt/media/log" "$COMPOSE_FILE"; then
        sed -i 's|zlmediakit-logs:/opt/media/log|zlmediakit-logs:/opt/media/bin/log|g' "$COMPOSE_FILE"
        
        # 验证修复结果
        if grep -q "zlmediakit-logs:/opt/media/bin/log" "$COMPOSE_FILE"; then
            log_success "配置文件修复成功"
            return 0
        else
            log_error "配置文件修复失败"
            return 1
        fi
    else
        log_warning "未找到需要修复的配置项"
        return 1
    fi
}

# 停止并清理容器
cleanup_container() {
    log_info "停止并清理问题容器..."
    
    # 强制停止容器
    if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        log_info "停止运行中的容器..."
        docker-compose stop zlmediakit
        sleep 5
    fi
    
    # 删除容器
    if docker ps -a --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        log_info "删除问题容器..."
        docker-compose rm -f zlmediakit
    fi
    
    log_success "容器清理完成"
}

# 重新创建容器
recreate_container() {
    log_info "重新创建 ZLMediaKit 容器..."
    
    # 拉取最新镜像
    log_info "拉取最新镜像..."
    docker-compose pull zlmediakit
    
    # 创建并启动容器
    log_info "创建并启动容器..."
    docker-compose up -d zlmediakit
    
    # 等待容器启动
    log_info "等待容器启动..."
    sleep 15
    
    # 检查容器状态
    if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        log_success "容器启动成功"
        return 0
    else
        log_error "容器启动失败"
        return 1
    fi
}

# 验证修复效果
verify_fix() {
    log_info "验证修复效果..."
    
    # 等待服务完全启动
    sleep 10
    
    # 检查容器状态
    local status=$(docker inspect ${CONTAINER_NAME} --format='{{.State.Status}}' 2>/dev/null || echo "not_found")
    
    if [[ "$status" == "running" ]]; then
        log_success "容器运行正常"
        
        # 检查日志中是否还有权限错误
        log_info "检查权限错误..."
        if docker logs ${CONTAINER_NAME} --since=30s 2>&1 | grep -q "permission denied"; then
            log_warning "仍然存在权限问题"
            return 1
        else
            log_success "权限问题已解决"
        fi
        
        # 检查健康状态
        sleep 30  # 等待健康检查
        local health=$(docker inspect ${CONTAINER_NAME} --format='{{.State.Health.Status}}' 2>/dev/null || echo "none")
        if [[ "$health" == "healthy" ]]; then
            log_success "容器健康检查通过"
        else
            log_warning "容器健康检查未通过: $health"
        fi
        
        return 0
    else
        log_error "容器状态异常: $status"
        return 1
    fi
}

# 显示容器日志
show_logs() {
    log_info "显示容器日志..."
    echo "========================================"
    docker logs ${CONTAINER_NAME} --tail=30
    echo "========================================"
}

# 主函数
main() {
    echo "========================================"
    echo "ZLMediaKit 服务器端修复工具"
    echo "开始时间: $(date)"
    echo "========================================"
    
    # 检查环境
    check_environment
    
    # 分析容器状态
    if ! analyze_container_status; then
        log_warning "检测到容器问题，开始修复..."
        
        # 备份配置
        backup_config
        
        # 修复配置文件
        if fix_config; then
            # 清理问题容器
            cleanup_container
            
            # 重新创建容器
            if recreate_container; then
                # 验证修复效果
                if verify_fix; then
                    log_success "ZLMediaKit 修复完成"
                    show_logs
                else
                    log_error "修复验证失败"
                    show_logs
                    exit 1
                fi
            else
                log_error "容器重新创建失败"
                exit 1
            fi
        else
            log_error "配置文件修复失败"
            exit 1
        fi
    else
        log_success "容器状态正常，无需修复"
        show_logs
    fi
    
    echo ""
    echo "========================================"
    log_success "修复工具执行完成"
    echo "结束时间: $(date)"
    echo "========================================"
}

# 执行主函数
main "$@"
