#!/bin/bash

# 修复视频流媒体问题的部署脚本
# 解决 Mixed Content、CORS 和 API 路径问题

set -e

echo "🔧 开始修复视频流媒体问题..."

# 1. 检查当前环境
echo "📋 检查当前环境..."
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装"
    exit 1
fi

# 2. 备份当前配置
echo "💾 备份当前配置..."
timestamp=$(date +%Y%m%d_%H%M%S)

# 备份 nginx 配置
if [ -f "/etc/nginx/sites-available/cloudplatform" ]; then
    sudo cp /etc/nginx/sites-available/cloudplatform /etc/nginx/sites-available/cloudplatform.backup.$timestamp
    echo "✅ Nginx 配置已备份"
fi

# 3. 更新 nginx 配置
echo "🔄 更新 nginx 配置..."
if [ -f "../deploy/nginx/nginx-cloudplatform.conf" ]; then
    sudo cp ../deploy/nginx/nginx-cloudplatform.conf /etc/nginx/sites-available/cloudplatform
    echo "✅ Nginx 配置已更新"
    
    # 测试 nginx 配置
    if sudo nginx -t; then
        echo "✅ Nginx 配置测试通过"
        sudo systemctl reload nginx
        echo "✅ Nginx 已重新加载"
    else
        echo "❌ Nginx 配置测试失败，恢复备份"
        sudo cp /etc/nginx/sites-available/cloudplatform.backup.$timestamp /etc/nginx/sites-available/cloudplatform
        exit 1
    fi
else
    echo "❌ ../deploy/nginx/nginx-cloudplatform.conf 文件不存在"
    exit 1
fi

# 4. 重新构建前端
echo "🏗️ 重新构建前端..."
cd ../CloudPlatform-web

# 检查是否有 package.json
if [ ! -f "package.json" ]; then
    echo "❌ package.json 不存在"
    exit 1
fi

# 安装依赖并构建
echo "📦 安装依赖..."
if command -v pnpm &> /dev/null; then
    pnpm install
    echo "🔨 构建前端..."
    pnpm build
elif command -v npm &> /dev/null; then
    npm install
    echo "🔨 构建前端..."
    npm run build
else
    echo "❌ 未找到 npm 或 pnpm"
    exit 1
fi

echo "✅ 前端构建完成"

# 5. 重新构建并部署前端容器
echo "🐳 重新构建前端容器..."
cd ..

# 构建新的前端镜像
docker build -t cloudmagnet/cloudplatform-web:1.0.7 CloudPlatform-web/

# 6. 更新 docker-compose.yml 中的前端版本
echo "📝 更新 docker-compose.yml..."
cd deploy/docker
sed -i 's/cloudmagnet\/cloudplatform-web:1.0.6/cloudmagnet\/cloudplatform-web:1.0.7/' docker-compose.yml

# 7. 重新部署服务
echo "🚀 重新部署服务..."
docker-compose down cloudplatform-web
docker-compose up -d cloudplatform-web

# 8. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 9. 检查服务状态
echo "📊 检查服务状态..."
if docker ps | grep -q "cloudplatform-web"; then
    echo "✅ 前端服务运行正常"
else
    echo "❌ 前端服务启动失败"
    docker-compose logs cloudplatform-web
    exit 1
fi

if docker ps | grep -q "cloudplatform-zlmediakit"; then
    echo "✅ ZLMediaKit 服务运行正常"
else
    echo "❌ ZLMediaKit 服务未运行"
    exit 1
fi

# 10. 测试 API 连接
echo "🧪 测试 API 连接..."
if curl -s -f "https://cloudplatform.cloudmagnet.lab/zlm/index/api/getServerConfig?secret=xiaoye71" > /dev/null; then
    echo "✅ ZLMediaKit API 连接正常"
else
    echo "⚠️ ZLMediaKit API 连接可能有问题，请检查"
fi

echo "🎉 视频流媒体问题修复完成！"
echo ""
echo "📋 修复内容："
echo "  ✅ 修复了 Mixed Content 问题（HTTPS/HTTP 混合内容）"
echo "  ✅ 修复了 CORS 跨域问题"
echo "  ✅ 修复了 API 路径配置问题"
echo "  ✅ 修复了 API 密钥不匹配问题"
echo "  ✅ 更新了 nginx 反向代理配置"
echo ""
echo "🌐 现在可以通过以下地址访问："
echo "  前端：https://cloudplatform.cloudmagnet.lab"
echo "  ZLMediaKit API：https://cloudplatform.cloudmagnet.lab/zlm/index/api/"
echo "  HLS 流：https://cloudplatform.cloudmagnet.lab/hls/"
