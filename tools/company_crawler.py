#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
南京森立诚信息科技有限公司 - 企业信息爬虫
功能：从多个数据源爬取企业相关信息
作者：Chen Yu
日期：2025-01-19
"""

import requests
import time
import json
import csv
import os
from bs4 import BeautifulSoup
from urllib.parse import quote, urljoin
import random
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CompanyCrawler:
    def __init__(self):
        self.company_name = "南京森立诚信息科技有限公司"
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)
        self.results = []
        
    def random_delay(self, min_delay=1, max_delay=3):
        """随机延迟，避免被反爬"""
        time.sleep(random.uniform(min_delay, max_delay))
        
    def save_result(self, source, title, url, content, extra_info=None):
        """保存爬取结果"""
        result = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'source': source,
            'title': title,
            'url': url,
            'content': content[:500] + '...' if len(content) > 500 else content,
            'full_content': content,
            'extra_info': extra_info or {}
        }
        self.results.append(result)
        logger.info(f"保存结果: {source} - {title}")
        
    def search_baidu(self):
        """百度搜索"""
        logger.info("开始百度搜索...")
        try:
            query = quote(self.company_name)
            url = f"https://www.baidu.com/s?wd={query}"
            
            response = self.session.get(url, timeout=10)
            response.encoding = 'utf-8'
            
            soup = BeautifulSoup(response.text, 'html.parser')
            results = soup.find_all('div', class_='result')
            
            for i, result in enumerate(results[:10]):  # 取前10个结果
                try:
                    title_elem = result.find('h3')
                    if title_elem:
                        title = title_elem.get_text().strip()
                        link_elem = title_elem.find('a')
                        if link_elem and link_elem.get('href'):
                            link = link_elem.get('href')
                            
                            # 获取摘要
                            content_elem = result.find('span', class_='content-right_8Zs40')
                            if not content_elem:
                                content_elem = result.find('div', class_='c-abstract')
                            content = content_elem.get_text().strip() if content_elem else ""
                            
                            self.save_result('百度搜索', title, link, content)
                            
                except Exception as e:
                    logger.warning(f"解析百度结果时出错: {e}")
                    
            self.random_delay()
            
        except Exception as e:
            logger.error(f"百度搜索失败: {e}")
            
    def search_tianyancha(self):
        """天眼查搜索（模拟）"""
        logger.info("尝试获取天眼查信息...")
        try:
            # 注意：天眼查有反爬机制，这里只是示例
            query = quote(self.company_name)
            url = f"https://www.tianyancha.com/search?key={query}"
            
            # 添加更真实的请求头
            headers = self.headers.copy()
            headers.update({
                'Referer': 'https://www.tianyancha.com/',
                'Host': 'www.tianyancha.com'
            })
            
            response = self.session.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                # 这里需要根据天眼查的实际页面结构来解析
                # 由于反爬机制，可能需要更复杂的处理
                self.save_result('天眼查', '企业信息查询', url, '需要进一步处理反爬机制')
            
            self.random_delay(2, 5)
            
        except Exception as e:
            logger.error(f"天眼查搜索失败: {e}")
            
    def search_qichacha(self):
        """企查查搜索（模拟）"""
        logger.info("尝试获取企查查信息...")
        try:
            query = quote(self.company_name)
            url = f"https://www.qcc.com/search?key={query}"
            
            headers = self.headers.copy()
            headers.update({
                'Referer': 'https://www.qcc.com/',
                'Host': 'www.qcc.com'
            })
            
            response = self.session.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                self.save_result('企查查', '企业信息查询', url, '需要进一步处理反爬机制')
            
            self.random_delay(2, 5)
            
        except Exception as e:
            logger.error(f"企查查搜索失败: {e}")
            
    def search_google(self):
        """Google搜索（需要代理）"""
        logger.info("尝试Google搜索...")
        try:
            query = quote(self.company_name)
            url = f"https://www.google.com/search?q={query}"
            
            # 注意：Google可能需要代理访问
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                results = soup.find_all('div', class_='g')
                
                for result in results[:5]:
                    try:
                        title_elem = result.find('h3')
                        if title_elem:
                            title = title_elem.get_text().strip()
                            link_elem = result.find('a')
                            if link_elem and link_elem.get('href'):
                                link = link_elem.get('href')
                                
                                # 获取摘要
                                content_elem = result.find('span', class_='aCOpRe')
                                content = content_elem.get_text().strip() if content_elem else ""
                                
                                self.save_result('Google搜索', title, link, content)
                                
                    except Exception as e:
                        logger.warning(f"解析Google结果时出错: {e}")
                        
            self.random_delay()
            
        except Exception as e:
            logger.error(f"Google搜索失败: {e}")
            
    def search_zhihu(self):
        """知乎搜索"""
        logger.info("搜索知乎相关内容...")
        try:
            query = quote(self.company_name)
            url = f"https://www.zhihu.com/search?type=content&q={query}"
            
            headers = self.headers.copy()
            headers.update({
                'Referer': 'https://www.zhihu.com/',
                'Host': 'www.zhihu.com'
            })
            
            response = self.session.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                # 知乎的搜索结果通常是动态加载的，这里只是示例
                self.save_result('知乎', '相关讨论搜索', url, '需要处理动态加载内容')
            
            self.random_delay()
            
        except Exception as e:
            logger.error(f"知乎搜索失败: {e}")
            
    def search_weibo(self):
        """微博搜索"""
        logger.info("搜索微博相关内容...")
        try:
            query = quote(self.company_name)
            url = f"https://s.weibo.com/weibo?q={query}"
            
            headers = self.headers.copy()
            headers.update({
                'Referer': 'https://weibo.com/',
                'Host': 's.weibo.com'
            })
            
            response = self.session.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                self.save_result('微博', '相关微博搜索', url, '需要处理登录验证')
            
            self.random_delay()
            
        except Exception as e:
            logger.error(f"微博搜索失败: {e}")
            
    def export_results(self):
        """导出结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 导出为JSON
        json_filename = f"company_info_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        # 导出为CSV
        csv_filename = f"company_info_{timestamp}.csv"
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as f:
            if self.results:
                writer = csv.DictWriter(f, fieldnames=['timestamp', 'source', 'title', 'url', 'content'])
                writer.writeheader()
                for result in self.results:
                    writer.writerow({
                        'timestamp': result['timestamp'],
                        'source': result['source'],
                        'title': result['title'],
                        'url': result['url'],
                        'content': result['content']
                    })
        
        # 生成报告
        report_filename = f"crawler_report_{timestamp}.txt"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(f"南京森立诚信息科技有限公司 - 信息收集报告\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总计收集信息: {len(self.results)} 条\n\n")
            
            # 按来源统计
            sources = {}
            for result in self.results:
                source = result['source']
                sources[source] = sources.get(source, 0) + 1
            
            f.write("信息来源统计:\n")
            for source, count in sources.items():
                f.write(f"  {source}: {count} 条\n")
            
            f.write("\n详细信息:\n")
            f.write("=" * 50 + "\n")
            
            for i, result in enumerate(self.results, 1):
                f.write(f"\n{i}. 【{result['source']}】{result['title']}\n")
                f.write(f"   URL: {result['url']}\n")
                f.write(f"   时间: {result['timestamp']}\n")
                f.write(f"   内容: {result['content']}\n")
                f.write("-" * 30 + "\n")
        
        logger.info(f"结果已导出:")
        logger.info(f"  JSON文件: {json_filename}")
        logger.info(f"  CSV文件: {csv_filename}")
        logger.info(f"  报告文件: {report_filename}")
        
    def run(self):
        """运行爬虫"""
        logger.info(f"开始爬取企业信息: {self.company_name}")
        logger.info("=" * 50)
        
        # 执行各种搜索
        search_methods = [
            self.search_baidu,
            self.search_tianyancha,
            self.search_qichacha,
            self.search_google,
            self.search_zhihu,
            self.search_weibo
        ]
        
        for method in search_methods:
            try:
                method()
                self.random_delay(3, 6)  # 每个搜索之间的延迟
            except Exception as e:
                logger.error(f"执行 {method.__name__} 时出错: {e}")
                continue
        
        # 导出结果
        self.export_results()
        
        logger.info("=" * 50)
        logger.info(f"爬取完成！共收集到 {len(self.results)} 条信息")
        
if __name__ == "__main__":
    crawler = CompanyCrawler()
    crawler.run()