# 云监控平台后端API接口分析

## 概述
后端基于Spring Boot 3.4.5构建，所有API统一使用`/api`前缀，采用RESTful风格设计。

## 接口清单

### 1. 磁力监控接口 (MagneticController)
**基础路径**: `/api/magnetic`

| 接口路径 | HTTP方法 | 功能说明 | 请求参数 | 返回数据 |
|---------|---------|---------|---------|---------|
| `/alarm/stats` | GET | 获取铁磁设备报警总数量统计 | `timeSpanType`: 时间范围(day/week/month/year) | `MagneticAlarmTotalStatsDTO`: 各风险等级报警数量 |
| `/alarm/count` | GET | 获取铁磁设备报警次数统计 | `timeSpanType`: 时间范围<br>`locationIds`: 点位ID列表(可选) | `MagneticAlarmCountDTO`: 报警次数 |
| `/alarm/totalCount` | GET | 获取所有传感器报警总次数 | `timeSpanType`: 时间范围<br>`locationIds`: 点位ID列表(可选) | `TotalAlarmCountDTO`: 总报警次数 |
| `/alarm/countsByLocation` | GET | 获取各点位设备报警次数 | `timeSpanType`: 时间范围 | `List<LocationAlarmCountDTO>`: 各点位报警统计 |

### 2. 监控服务接口 (MonitoringController)
**基础路径**: `/api/monitoring`

| 接口路径 | HTTP方法 | 功能说明 | 请求参数 | 返回数据 |
|---------|---------|---------|---------|---------|
| `/location-status` | GET | 获取所有点位的监控状态 | 无 | `List<LocationMonitoringStatusDTO>`: 点位监控状态列表 |

**监控状态说明**：
- **在线**: 所有摄像头RTSP服务正常
- **部分在线**: 部分摄像头正常，部分异常
- **离线**: 所有摄像头异常
- **无设备**: 未关联摄像头设备
- **配置错误**: 无法解析RTSP URL
- **状态未知**: 未知错误

### 3. 系统接口 (SystemController)
**基础路径**: `/api/system`

| 接口路径 | HTTP方法 | 功能说明 | 请求参数 | 返回数据 |
|---------|---------|---------|---------|---------|
| `/currentTime` | GET | 获取服务器当前时间 | 无 | `CurrentTimeDTO`: ISO 8601格式UTC时间 |

### 4. 环境数据接口 (EnvironmentalController)
**基础路径**: `/api/environmental`

| 接口路径 | HTTP方法 | 功能说明 | 请求参数 | 返回数据 |
|---------|---------|---------|---------|---------|
| `/average/latest` | GET | 获取实时平均环境数据 | 无 | `AverageEnvironmentalDataDTO`: 平均氧气、温度、湿度 |
| `/trend/hourly` | GET | 获取当天每小时温湿度趋势 | 无 | `EnvironmentalTrendDTO`: 每小时温湿度数据 |
| `/alarm/count` | GET | 获取环境传感器报警次数 | `timeSpanType`: 时间范围<br>`locationIds`: 点位ID数组(可选) | `EnvironmentalAlarmCountDTO`: 报警次数 |

**数据单位**：
- 氧气浓度: %
- 温度: ℃
- 湿度: %RH

### 5. 天气数据接口 (WeatherController)
**基础路径**: `/api/weather`

| 接口路径 | HTTP方法 | 功能说明 | 请求参数 | 返回数据 |
|---------|---------|---------|---------|---------|
| `/current` | GET | 直接调用第三方API获取天气 | `city`: 城市名称<br>`areaCode`: 城市代码<br>(二选一) | 原始JSON字符串 |
| `/currentFromCache` | GET | 从缓存获取当前天气 | `areaCode`: 城市代码(可选) | `CurrentWeatherDTO`: 天气和温度 |
| `/triggerWeatherUpdate` | GET | 手动触发天气数据更新 | 无 | 成功消息 |

**缓存策略**：
- 使用Redis缓存24小时天气预报数据
- 定时任务定期更新缓存
- 根据当前时间返回最接近的小时数据

## 通用响应格式
所有接口采用统一的响应格式 `ApiResponse<T>`：

```json
{
  "code": 200,
  "message": "成功信息",
  "data": {
    // 具体的业务数据
  }
}
```

## 数据来源
1. **设备数据**: 来自MySQL数据库中的设备表(`eqp_equipment`)和结果表(`mag_result`, `wost_result`)
2. **点位信息**: `eqp_location`表
3. **天气数据**: 阿里云天气API，通过Redis缓存

## 特殊说明
1. 所有时间相关的统计都支持按天(day)、周(week)、月(month)、年(year)维度
2. 摄像头设备类型标识为`SBXH="02"`
3. 环境数据原始值需要缩放处理：
   - 氧气浓度: 原始值 / 100.0
   - 温度和湿度: 原始值 / 10.0
4. RTSP连接检查超时时间为5秒

## API文档
项目集成了Springdoc OpenAPI，可通过以下地址访问：
- Swagger UI: `/swagger-ui.html`
- OpenAPI文档: `/v3/api-docs`