<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MR科数据大屏设计预览 2.1</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #0a2b4a;
            color: #fff;
            overflow: hidden;
            height: 100vh;
        }

        .data-screen {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* 顶部标题栏 */
        .header {
            height: 80px;
            background: linear-gradient(135deg, rgba(0, 146, 255, 0.2), rgba(0, 201, 242, 0.1));
            border-bottom: 2px solid rgba(0, 201, 242, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 40px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 80"><path d="M0,0 L1920,0 L1800,80 L120,80 Z" fill="rgba(0,201,242,0.1)"/></svg>');
            background-size: cover;
        }

        .header-left, .header-right {
            font-size: 16px;
            color: #ade9ff;
            z-index: 2;
            position: relative;
        }

        .header-center {
            font-size: 32px;
            font-weight: bold;
            color: #fff;
            text-shadow: 0 0 20px rgba(0, 201, 242, 0.5);
            z-index: 2;
            position: relative;
        }

        /* 主要内容区 */
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 300px 1fr 300px;
            grid-template-rows: 1fr 300px;
            gap: 20px;
            padding: 20px;
        }

        /* 左侧面板 */
        .left-panel {
            grid-row: 1 / 3;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* 中央区域 */
        .center-panel {
            position: relative;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 400"><rect width="800" height="400" fill="%234a90a4"/><rect x="50" y="50" width="700" height="300" fill="%23ffffff" opacity="0.9"/><rect x="100" y="100" width="200" height="200" fill="%236fa8dc"/><rect x="350" y="100" width="200" height="200" fill="%236fa8dc"/><rect x="600" y="100" width="100" height="200" fill="%236fa8dc"/><text x="400" y="350" text-anchor="middle" fill="%23333" font-size="24" font-family="Arial">医院实景图片展示区域</text></svg>');
            background-size: cover;
            background-position: center;
            border-radius: 12px;
            overflow: hidden;
        }

        .center-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(10, 43, 74, 0.4);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .center-card {
            background: rgba(13, 45, 76, 0.85);
            border: 1px solid rgba(0, 201, 242, 0.3);
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .center-card h3 {
            font-size: 24px;
            margin-bottom: 15px;
            color: #00c9f2;
        }

        .center-score {
            font-size: 48px;
            font-weight: bold;
            color: #00ff9d;
            margin: 15px 0;
        }

        .center-stars {
            font-size: 24px;
            color: #ffd700;
            margin: 10px 0;
        }

        .center-status {
            font-size: 18px;
            color: #ade9ff;
        }

        /* 右侧面板 */
        .right-panel {
            grid-row: 1 / 3;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* 底部数据分析区 */
        .bottom-panel {
            grid-column: 2;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        /* 通用卡片样式 */
        .card {
            background: rgba(13, 45, 76, 0.9);
            border: 1px solid rgba(0, 201, 242, 0.2);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .card-title {
            font-size: 18px;
            color: #00c9f2;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-title::before {
            content: '●';
            color: #00ff9d;
        }

        /* 环境指标卡片 */
        .env-data {
            height: 280px;
        }

        .env-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: rgba(0, 201, 242, 0.1);
            border-radius: 8px;
        }

        .env-label {
            font-size: 14px;
            color: #ade9ff;
        }

        .env-value {
            font-size: 20px;
            font-weight: bold;
            color: #00ff9d;
        }

        .env-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #00ff9d;
            box-shadow: 0 0 10px #00ff9d;
        }

        /* 科室轮播卡片 */
        .department-carousel {
            height: 280px;
            position: relative;
        }

        .department-item {
            background: rgba(0, 201, 242, 0.1);
            border: 1px solid rgba(0, 201, 242, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            transition: all 0.3s ease;
        }

        .department-item:hover {
            background: rgba(0, 201, 242, 0.2);
            transform: translateX(5px);
        }

        .department-name {
            font-size: 16px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 8px;
        }

        .department-stats {
            font-size: 12px;
            color: #ade9ff;
            line-height: 1.4;
        }

        .department-status {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00ff9d;
            margin-right: 5px;
        }

        /* 报警统计卡片 */
        .alarm-stats {
            height: 280px;
        }

        .alarm-item {
            display: flex;
            justify-content: space-between;
            margin: 12px 0;
            padding: 8px;
            border-left: 3px solid #00c9f2;
            background: rgba(0, 201, 242, 0.05);
        }

        .alarm-count {
            font-size: 18px;
            font-weight: bold;
            color: #00ff9d;
        }

        /* 趋势图表卡片 */
        .chart-card {
            min-height: 250px;
            position: relative;
        }

        .chart-placeholder {
            height: 180px;
            background: rgba(0, 201, 242, 0.1);
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #ade9ff;
            font-size: 14px;
            border: 1px dashed rgba(0, 201, 242, 0.3);
        }

        /* 响应式动画 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* 滚动动画 */
        @keyframes scroll {
            0% { transform: translateY(0); }
            100% { transform: translateY(-50%); }
        }

        .scroll-container {
            overflow: hidden;
            height: 200px;
        }

        .scroll-content {
            animation: scroll 10s linear infinite;
        }
    </style>
</head>
<body>
    <div class="data-screen">
        <!-- 顶部标题栏 -->
        <header class="header">
            <div class="header-left">
                <div>🌤️ 晴 17~28°C</div>
            </div>
            <div class="header-center">
                云磁安全云平台 - MR科监控中心
            </div>
            <div class="header-right">
                <div>2025-07-10 10:13:58</div>
            </div>
        </header>

        <!-- 主要内容区 -->
        <main class="main-content">
            <!-- 左侧面板 -->
            <section class="left-panel">
                <!-- 环境指标总览 -->
                <div class="card env-data">
                    <div class="card-title">🌡️ 环境指标总览</div>
                    <div class="env-item">
                        <div>
                            <div class="env-label">氧气浓度</div>
                            <div class="env-value">20.9%</div>
                        </div>
                        <div class="env-status"></div>
                    </div>
                    <div class="env-item">
                        <div>
                            <div class="env-label">环境温度</div>
                            <div class="env-value">22.5°C</div>
                        </div>
                        <div class="env-status"></div>
                    </div>
                    <div class="env-item">
                        <div>
                            <div class="env-label">环境湿度</div>
                            <div class="env-value">45%RH</div>
                        </div>
                        <div class="env-status"></div>
                    </div>
                    <div class="env-item">
                        <div>
                            <div class="env-label">设备在线率</div>
                            <div class="env-value">98%</div>
                        </div>
                        <div class="env-status"></div>
                    </div>
                </div>

                <!-- 24小时趋势图 -->
                <div class="card chart-card">
                    <div class="card-title">📈 24小时环境趋势</div>
                    <div class="chart-placeholder">
                        环境指标趋势图表<br>
                        (氧气浓度、温度、湿度变化曲线)
                    </div>
                </div>
            </section>

            <!-- 中央展示区 -->
            <section class="center-panel">
                <div class="center-overlay">
                    <div class="center-card">
                        <h3>MR科总体评分</h3>
                        <div class="center-score">92</div>
                        <div class="center-stars">⭐⭐⭐⭐⭐</div>
                        <div class="center-status">运行良好</div>
                    </div>
                </div>
            </section>

            <!-- 右侧面板 -->
            <section class="right-panel">
                <!-- 科室状态轮播 -->
                <div class="card department-carousel">
                    <div class="card-title">🎯 科室状态轮播</div>
                    <div class="scroll-container">
                        <div class="scroll-content">
                            <div class="department-item">
                                <div class="department-name">
                                    <span class="department-status"></span>MR-01
                                </div>
                                <div class="department-stats">
                                    氧气:20.8% 温度:22°C<br>
                                    湿度:45% 状态:稳定
                                </div>
                            </div>
                            <div class="department-item">
                                <div class="department-name">
                                    <span class="department-status"></span>MR-02
                                </div>
                                <div class="department-stats">
                                    氧气:21.1% 温度:21°C<br>
                                    湿度:43% 状态:正常
                                </div>
                            </div>
                            <div class="department-item">
                                <div class="department-name">
                                    <span class="department-status" style="background: #faad14;"></span>MR-03
                                </div>
                                <div class="department-stats">
                                    氧气:19.5% 温度:24°C<br>
                                    湿度:50% 状态:警告
                                </div>
                            </div>
                            <div class="department-item">
                                <div class="department-name">
                                    <span class="department-status"></span>MR-04
                                </div>
                                <div class="department-stats">
                                    氧气:20.7% 温度:22°C<br>
                                    湿度:44% 状态:正常
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 报警统计总览 -->
                <div class="card alarm-stats">
                    <div class="card-title">📊 报警统计总览</div>
                    <div style="margin-bottom: 15px;">
                        <div style="color: #00c9f2; font-size: 14px; margin-bottom: 8px;">📊 今日统计</div>
                        <div class="alarm-item">
                            <span>总报警次数</span>
                            <span class="alarm-count">12</span>
                        </div>
                        <div class="alarm-item">
                            <span>环境报警</span>
                            <span class="alarm-count">3</span>
                        </div>
                        <div class="alarm-item">
                            <span>铁磁报警</span>
                            <span class="alarm-count">9</span>
                        </div>
                    </div>
                    
                    <div>
                        <div style="color: #00c9f2; font-size: 14px; margin-bottom: 8px;">🔍 风险分析</div>
                        <div class="alarm-item">
                            <span>低风险</span>
                            <span class="alarm-count">5次</span>
                        </div>
                        <div class="alarm-item">
                            <span>中风险</span>
                            <span class="alarm-count">3次</span>
                        </div>
                        <div class="alarm-item">
                            <span>高风险</span>
                            <span class="alarm-count">1次</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 底部数据分析区 -->
            <section class="bottom-panel">
                <!-- 铁磁报警统计 -->
                <div class="card chart-card">
                    <div class="card-title">🧲 铁磁报警统计</div>
                    <div class="chart-placeholder">
                        铁磁报警饼图<br>
                        (低/中/高风险分布)
                    </div>
                </div>

                <!-- 环境报警分析 -->
                <div class="card chart-card">
                    <div class="card-title">🌡️ 环境报警分析</div>
                    <div class="chart-placeholder">
                        环境报警柱状图<br>
                        (各类型报警次数)
                    </div>
                </div>

                <!-- 科室对比分析 -->
                <div class="card chart-card">
                    <div class="card-title">📊 科室对比分析</div>
                    <div class="chart-placeholder">
                        科室健康雷达图<br>
                        (多维度对比分析)
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        // 简单的数据更新模拟
        function updateData() {
            // 模拟数据变化
            const envValues = document.querySelectorAll('.env-value');
            envValues.forEach(value => {
                if (Math.random() > 0.9) {
                    value.classList.add('pulse');
                    setTimeout(() => value.classList.remove('pulse'), 2000);
                }
            });
        }

        // 每5秒更新一次数据
        setInterval(updateData, 5000);

        // 添加点击事件
        document.querySelectorAll('.department-item').forEach(item => {
            item.addEventListener('click', () => {
                alert('点击进入 ' + item.querySelector('.department-name').textContent + ' 详情页');
            });
        });
    </script>
</body>
</html>