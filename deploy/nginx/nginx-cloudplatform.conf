# CloudPlatform 生产级 Nginx 配置
# 基于后端源代码分析的健壮全面配置
# 支持：Spring Boot API、Vue.js SPA、ZLMediaKit流媒体、WebSocket、文件上传

# 上游服务器定义
upstream cloudplatform_backend {
    server cloudplatform-srv:8081;
    keepalive 32;
}

upstream cloudplatform_frontend {
    server cloudplatform-web:3000;
    keepalive 16;
}

upstream zlmediakit_server {
    server zlmediakit:80;
    keepalive 8;
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name cloudplatform.cloudmagnet.lab;

    # 健康检查端点（不重定向）
    location = /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 其他请求重定向到 HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# HTTPS 主服务
server {
    listen 443 ssl http2;
    server_name cloudplatform.cloudmagnet.lab;

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/cloudplatform.crt;
    ssl_certificate_key /etc/nginx/ssl/cloudplatform.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;

    # 安全头部
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    # 压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        application/vnd.ms-fontobject
        font/opentype;

    # 全局配置
    client_max_body_size 100M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    proxy_connect_timeout 30s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
    proxy_buffering off;
    proxy_request_buffering off;

    # 健康检查端点
    location = /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # ========================================================================
    # Spring Boot API 路由 (基于源代码分析)
    # ========================================================================

    # 环境数据 API (/api/environmental/*)
    location ~ ^/api/environmental/ {
        proxy_pass http://cloudplatform_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
    }

    # 磁性报警 API (/api/magnetic/*)
    location ~ ^/api/magnetic/ {
        proxy_pass http://cloudplatform_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
    }

    # 监控 API (/api/monitoring/*)
    location ~ ^/api/monitoring/ {
        proxy_pass http://cloudplatform_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
        # 监控接口可能需要更长的超时时间（RTSP检查）
        proxy_read_timeout 600s;
    }

    # 天气 API (/api/weather/*)
    location ~ ^/api/weather/ {
        proxy_pass http://cloudplatform_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
    }

    # 系统 API (/api/system/*)
    location ~ ^/api/system/ {
        proxy_pass http://cloudplatform_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
    }

    # 通用 API 路由（兜底）
    location ~ ^/api/ {
        proxy_pass http://cloudplatform_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
    }

    # Spring Boot Actuator 端点
    location ~ ^/actuator/ {
        # 限制访问来源（仅内网）
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;

        proxy_pass http://cloudplatform_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        add_header Cache-Control "no-store";
    }

    # Swagger UI 文档
    location ~ ^/swagger-ui/ {
        proxy_pass http://cloudplatform_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        expires 1h;
        add_header Cache-Control "public, max-age=3600";
    }

    # OpenAPI 规范文档
    location ~ ^/v3/api-docs {
        proxy_pass http://cloudplatform_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        add_header Cache-Control "no-store";
    }

    # ========================================================================
    # ZLMediaKit 流媒体服务路由
    # ========================================================================

    # ZLMediaKit HTTP API (基于config.ini分析)
    location ~ ^/zlm/api/ {
        rewrite ^/zlm/api/(.*)$ /api/$1 break;
        proxy_pass http://zlmediakit_server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # CORS 支持 - 解决跨域问题
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        add_header Access-Control-Expose-Headers "Content-Length,Content-Range" always;

        # 处理 OPTIONS 预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }

        add_header Cache-Control "no-store";
    }

    # ZLMediaKit index API 直接代理
    location ~ ^/zlm/index/ {
        rewrite ^/zlm/(.*)$ /$1 break;
        proxy_pass http://zlmediakit_server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # CORS 支持
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;

        # 处理 OPTIONS 预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }

        add_header Cache-Control "no-store";
    }

    # HLS 流媒体文件 (.m3u8, .ts)
    location ~ ^/hls/ {
        # 重写路径，移除 /hls 前缀
        rewrite ^/hls/(.*)$ /$1 break;
        proxy_pass http://zlmediakit_server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # HLS 特定配置
        proxy_buffering off;
        proxy_cache off;

        # CORS 支持
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Range" always;

        # 处理 OPTIONS 预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS";
            add_header Access-Control-Allow-Headers "Range";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }

        # HLS 文件缓存策略
        location ~* \.m3u8$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate" always;
            add_header Access-Control-Allow-Origin "*" always;
        }

        location ~* \.ts$ {
            expires 10s;
            add_header Cache-Control "public, max-age=10" always;
            add_header Access-Control-Allow-Origin "*" always;
        }
    }

    # HTTP-FLV 流媒体
    location ~ ^/live/.*\.flv$ {
        proxy_pass http://zlmediakit_server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # FLV 流配置
        proxy_buffering off;
        proxy_cache off;
        proxy_read_timeout 3600s;

        # CORS 支持
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS";
    }

    # WebRTC 信令服务
    location ~ ^/webrtc/ {
        proxy_pass http://zlmediakit_server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # WebRTC 长连接配置
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
    }

    # ZLMediaKit 静态资源和管理界面
    location ~ ^/zlm/ {
        rewrite ^/zlm/(.*)$ /$1 break;
        proxy_pass http://zlmediakit_server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        expires 1h;
        add_header Cache-Control "public, max-age=3600";
    }

    # ========================================================================
    # 静态资源和前端 SPA
    # ========================================================================

    # Spring Boot 静态资源
    location ~ ^/static/ {
        proxy_pass http://cloudplatform_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
    }

    # 前端静态资源 (基于serve.json分析)
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        proxy_pass http://cloudplatform_frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 静态资源长期缓存
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";
        add_header Vary "Accept-Encoding";
    }

    # 前端 HTML 文件
    location ~* \.html$ {
        proxy_pass http://cloudplatform_frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # HTML 文件不缓存
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }

    # 前端 SPA 路由 (Vue Router 支持)
    location / {
        proxy_pass http://cloudplatform_frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # WebSocket 支持 (用于热重载等)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # SPA 路由支持
        try_files $uri $uri/ @fallback;
    }

    # SPA 路由回退
    location @fallback {
        proxy_pass http://cloudplatform_frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # ========================================================================
    # 安全和错误处理
    # ========================================================================

    # 禁止访问敏感文件
    location ~ /\.(ht|git|svn) {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

    # 日志配置
    access_log /var/log/nginx/cloudplatform.access.log combined;
    error_log /var/log/nginx/cloudplatform.error.log warn;
}

# 部署说明：
# 1. 复制到 nginx 配置目录：sudo cp nginx-cloudplatform.conf /etc/nginx/sites-available/cloudplatform
# 2. 创建软链接：sudo ln -s /etc/nginx/sites-available/cloudplatform /etc/nginx/sites-enabled/
# 3. 测试配置：sudo nginx -t
# 4. 重载配置：sudo systemctl reload nginx
#
# 注意：
# - 需要在nginx主配置中添加WebSocket映射
# - 确保SSL证书文件存在于指定路径
# - 包含完整的Swagger API文档支持
