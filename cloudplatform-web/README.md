# CloudPlatform-web

[![Vue.js](https://img.shields.io/badge/Vue.js-3.2.40-brightgreen.svg)](https://vuejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-4.8.4-blue.svg)](https://www.typescriptlang.org/)
[![Arco Design](https://img.shields.io/badge/Arco%20Design-2.44.7-blue.svg)](https://arco.design/)
[![Vite](https://img.shields.io/badge/Vite-3.2.5-purple.svg)](https://vitejs.dev/)
[![Pinia](https://img.shields.io/badge/Pinia-2.0.23-yellow.svg)](https://pinia.vuejs.org/)

CloudPlatform-web 是一个现代化的 Vue.js 3 前端应用程序，用于 CloudPlatform 监控和管理系统。基于 TypeScript、Arco Design UI 框架和 Vite 构建，为环境监控、磁性报警管理、天气数据可视化和视频监控提供全面的仪表板。

## 🚀 功能特性

- **现代化 Vue.js 3**: 支持 TypeScript 的组合式 API
- **Arco Design UI**: 专业的企业级 UI 组件库
- **实时监控**: 实时环境数据和报警系统
- **视频流媒体**: 集成 Video.js 的 RTSP/HLS 视频播放器
- **响应式设计**: 适配不同屏幕尺寸的自适应布局
- **国际化支持**: 多语言支持（中文/英文）
- **状态管理**: 使用 Pinia 进行高效状态管理
- **数据可视化**: 集成 ECharts 图表和图形
- **渐进式 Web 应用**: 支持移动设备的 PWA 功能
- **热模块替换**: 使用 Vite HMR 快速开发

## 📋 系统要求

运行此应用程序之前，请确保已安装以下软件：

- **Node.js 14.0+**（推荐：Node.js 16+）
- **npm** 或 **pnpm**（推荐）
- **Git**

## 🛠️ 安装步骤

### 1. 克隆仓库

```bash
git clone ssh://**************************:2289/cm_project/cloud_platform/cy/cloudplatform-web.git
cd CloudPlatform-web
```

### 2. 安装依赖

使用 npm：

```bash
npm install
```

使用 pnpm（推荐）：

```bash
pnpm install
```

### 3. 环境配置

创建环境配置文件：

```bash
# 开发环境
cp .env.example .env.development

# 生产环境
cp .env.example .env.production
```

配置 API 端点和其他环境变量：

```env
# .env.development
VITE_API_BASE_URL=http://localhost:8081
VITE_APP_TITLE=CloudPlatform Dashboard

# .env.production
VITE_API_BASE_URL=https://cloudplatform.cloudmagnet.lab
VITE_APP_TITLE=CloudPlatform Dashboard
```

### 4. 启动开发服务器

```bash
# 启动开发服务器
npm run dev
# 或
pnpm dev

# 应用程序将在 http://localhost:3000 可用
```

## 🔧 配置说明

### 构建配置

项目使用 Vite 和自定义配置：

- **基础配置**: `config/vite.config.base.ts`
- **开发环境**: `config/vite.config.dev.ts`
- **生产环境**: `config/vite.config.prod.ts`

### 样式配置

- **PostCSS**: 配置响应式设计的视口单位
- **Less**: CSS 预处理器，支持 Arco Design 主题定制
- **响应式**: 自动将 px 转换为 vw，适配 1920x1080 设计

### API 配置

API 服务配置在 `src/api/` 目录：

```typescript
// src/api/interceptor.ts
import axios from 'axios';

const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
});
```

#### 重要配置说明

**API 基础 URL 配置规范**：

- ✅ **正确**: `VITE_API_BASE_URL=https://cloudplatform.cloudmagnet.lab`
- ❌ **错误**: `VITE_API_BASE_URL=https://cloudplatform.cloudmagnet.lab/api`

**原因**: API 调用代码中已包含 `/api` 路径前缀，如果在基础 URL 中再次包含会导致路径重复。

**环境变量优先级**：

1. Docker 构建参数 (`--build-arg`)
2. 环境变量文件 (`.env.production`, `.env.development`)
3. Dockerfile 中的默认值

**注意事项**：

- 修改 `.env` 文件后需要重新构建应用
- 使用 Docker 构建时，构建脚本中的 `--build-arg` 会覆盖 `.env` 文件设置
- 确保构建脚本 `build.sh` 中的 API URL 配置与环境变量文件保持一致

## 📚 项目结构

```
src/
├── api/                    # API 服务模块
│   ├── interceptor.ts     # Axios 拦截器
│   ├── user.ts           # 用户认证 API
│   ├── user-center.ts    # 用户中心 API
│   └── visualization.ts  # 数据可视化 API
├── assets/               # 静态资源
│   ├── images/          # 图片文件
│   ├── icons/           # 图标文件
│   └── style/           # 全局样式
├── components/          # 可复用组件
│   ├── global-setting/  # 全局设置组件
│   └── ...             # 其他共享组件
├── hooks/              # Vue 组合式钩子
├── layout/             # 布局组件
├── locale/             # 国际化
├── mock/               # 开发环境模拟数据
├── router/             # Vue Router 配置
│   ├── routes/         # 路由定义
│   └── guard/          # 路由守卫
├── store/              # Pinia 状态管理
│   ├── modules/        # 状态模块
│   └── index.ts        # 状态配置
├── types/              # TypeScript 类型定义
├── utils/              # 工具函数
├── views/              # 页面组件
│   ├── dashboard/      # 仪表板页面
│   ├── login/          # 登录页面
│   └── ...            # 其他页面
├── App.vue             # 根组件
└── main.ts             # 应用程序入口
```

## 🎨 UI 组件

### Arco Design 集成

项目使用 Arco Design Vue 组件：

```vue
<template>
  <a-button type="primary" @click="handleClick"> 点击我 </a-button>
</template>
```

### 自定义组件

- **全局设置**: 主题和布局配置
- **视频播放器**: 使用 Video.js 的 RTSP/HLS 流媒体
- **图表组件**: ECharts 包装组件
- **数据表格**: 增强的表格组件，支持搜索和过滤

## 🔄 状态管理

### Pinia 状态库

```typescript
// 用户状态示例
import { defineStore } from 'pinia';

export const useUserStore = defineStore('user', {
  state: () => ({
    name: '',
    role: '',
    token: '',
  }),

  actions: {
    async login(credentials) {
      // 登录逻辑
    },

    logout() {
      // 登出逻辑
    },
  },
});
```

### 可用状态库

- **App Store**: 应用程序设置和配置
- **User Store**: 用户认证和个人资料
- **Tab Bar Store**: 标签导航状态

## 🌐 路由配置

### 路由配置

```typescript
// 仪表板路由示例
const DASHBOARD: AppRouteRecordRaw = {
  path: '/dashboard',
  name: 'dashboard',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.dashboard',
    requiresAuth: true,
    icon: 'icon-dashboard',
  },
  children: [
    {
      path: 'workplace',
      name: 'Workplace',
      component: () => import('@/views/dashboard/workplace/index.vue'),
    },
  ],
};
```

### 路由守卫

- **身份验证**: 保护需要登录的路由
- **授权**: 基于角色的访问控制
- **导航**: 进度条和加载状态

## 🧪 测试

### 运行测试

```bash
# 运行单元测试
npm run test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行端到端测试
npm run test:e2e
```

### 测试工具

- **Vitest**: 单元测试框架
- **Vue Test Utils**: Vue 组件测试
- **Cypress**: 端到端测试

## 🚀 构建和部署

### 生产构建

```bash
# 构建生产版本
npm run build

# 预览生产构建
npm run preview

# 构建并生成包分析报告
npm run report
```

### 构建输出

```
dist/
├── assets/          # 编译后的资源
├── index.html       # 入口 HTML 文件
└── ...             # 其他构建文件
```

### Docker 部署

```dockerfile
# Dockerfile
FROM node:16-alpine as build-stage

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:stable-alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🔧 开发

### 代码规范

- **ESLint**: 使用 Airbnb 配置的代码检查
- **Prettier**: 代码格式化
- **Stylelint**: CSS/Less 检查
- **Husky**: Git 钩子进行提交前检查

### 开发命令

```bash
# 代码检查
npm run lint

# 代码格式化
npm run format

# 类型检查
npm run type:check
```

## 🛠️ 故障排除

### 常见问题

1. **Node.js 版本兼容性**

   - 确保安装 Node.js 14+
   - 使用 `nvm` 管理 Node.js 版本

2. **依赖安装问题**

   - 清除 node_modules: `rm -rf node_modules package-lock.json`
   - 重新安装: `npm install`

3. **构建错误**

   - 检查 TypeScript 错误: `npm run type:check`
   - 验证环境变量

4. **API 连接问题**

   - 检查后端服务器是否运行
   - 验证环境配置中的 API 基础 URL（确保不包含重复的 `/api` 路径）
   - 检查 CORS 配置
   - 确认构建脚本中的 `--build-arg VITE_API_BASE_URL` 配置正确

5. **API 路径重复问题**
   - 症状：请求 URL 出现 `/api/api/...` 的重复路径
   - 解决：检查 `.env` 文件和 `build.sh` 脚本中的 API URL 配置
   - 确保基础 URL 不包含 `/api` 后缀

## 👥 作者

- **Chen Yu** - _初始工作_ - [chen_yu](https://gitlab.cloudmagnet.lab/cm_project/cloud_platform/cy)

## 🆘 技术支持

如需支持和咨询：

- 在 GitLab 仓库中创建 issue
- 联系开发团队
- 查看组件文档

## 📈 项目状态

本项目正在积极维护和持续开发中。根据用户需求和反馈，定期添加新功能和改进。
