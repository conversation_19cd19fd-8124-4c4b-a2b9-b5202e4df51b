# CloudPlatform-web 容器化指南

## 概述

CloudPlatform-web 是基于 Vue.js 3 + Vite 的现代前端应用，本指南提供完整的容器化解决方案，包括生产级 Dockerfile、构建脚本和多平台支持。

## 技术栈

### 前端技术

- **Vue.js 3.2.40**: 现代响应式前端框架
- **Vite 3.2.5**: 快速构建工具
- **TypeScript**: 类型安全的 JavaScript
- **Arco Design**: 企业级 UI 组件库
- **Pinia**: Vue 状态管理
- **Vue Router**: 路由管理

### 容器化技术

- **Node.js 18 Alpine**: 构建和运行阶段基础镜像
- **serve**: 轻量级静态文件服务器
- **Docker Buildx**: 多平台构建支持
- **多阶段构建**: 优化镜像大小和安全性

## 文件结构

```
CloudPlatform-web/
├── Dockerfile              # 生产级多阶段 Dockerfile
├── serve.json              # serve 静态文件服务器配置
├── .dockerignore           # Docker 构建忽略文件
├── build.sh                # 主要构建脚本
├── build-multiplatform.sh  # 多平台构建脚本
├── test-build.sh           # 构建测试脚本
├── DOCKER_GUIDE.md         # 本文档
├── package.json            # 项目依赖配置
├── vite.config.ts          # Vite 构建配置
└── src/                    # 源代码目录
```

## 快速开始

### 基础构建

```bash
# 1. 进入项目目录
cd CloudPlatform-web

# 2. 基础构建
./build.sh

# 3. 运行容器
docker run -d --name cloudplatform-web -p 3000:3000 cloudmagnet/cloudplatform-web:latest
```

### 生产环境构建

```bash
# 生产环境构建并推送
./build.sh -e production -p

# 多平台构建
./build-multiplatform.sh --platform all --env production --push
```

## 构建脚本详解

### build.sh - 主要构建脚本

#### 功能特性

- ✅ 多环境支持（development、staging、production）
- ✅ 版本管理和自动检测
- ✅ 环境变量配置
- ✅ 构建验证和健康检查
- ✅ Docker 镜像推送

#### 使用示例

```bash
# 开发环境构建
./build.sh -e development

# 生产环境构建，自定义 API URL
./build.sh -e production --api-url https://api.example.com

# 构建并推送到仓库
./build.sh -e production -p

# 跳过本地构建，直接 Docker 构建
./build.sh --skip-build -e production

# 详细模式构建
./build.sh --verbose -e production
```

#### 命令参数

| 参数            | 描述         | 示例                                   |
| --------------- | ------------ | -------------------------------------- |
| `-e, --env`     | 构建环境     | `development`, `staging`, `production` |
| `-v, --version` | 指定版本     | `1.0.0`                                |
| `-p, --push`    | 推送镜像     | -                                      |
| `--api-url`     | API 基础 URL | `https://api.example.com`              |
| `--app-title`   | 应用标题     | `"My Dashboard"`                       |
| `--skip-build`  | 跳过本地构建 | -                                      |
| `--verbose`     | 详细输出     | -                                      |
| `--dry-run`     | 模拟运行     | -                                      |

### build-multiplatform.sh - 多平台构建脚本

#### 功能特性

- ✅ 支持 AMD64 和 ARM64 平台
- ✅ 基于 Docker Buildx
- ✅ 自动平台检测
- ✅ 与主构建脚本一致的用户体验

#### 使用示例

```bash
# 当前平台构建
./build-multiplatform.sh

# ARM64 平台构建
./build-multiplatform.sh --platform arm64

# 多平台构建并推送
./build-multiplatform.sh --platform all --push

# 生产环境多平台构建
./build-multiplatform.sh --platform all --env production --push
```

## Dockerfile 详解

### 多阶段构建

#### 第一阶段：Node.js 构建

```dockerfile
FROM node:18-alpine AS builder
# 安装依赖、构建应用
```

#### 第二阶段：Nginx 生产运行

```dockerfile
FROM nginx:1.25-alpine AS runtime
# 复制构建产物、配置 Nginx
```

### 构建参数

| 参数                | 描述         | 默认值                    |
| ------------------- | ------------ | ------------------------- |
| `VITE_API_BASE_URL` | API 基础 URL | `http://localhost:8081`   |
| `VITE_APP_TITLE`    | 应用标题     | `CloudPlatform Dashboard` |
| `VITE_APP_VERSION`  | 应用版本     | 自动检测                  |

### 安全特性

- ✅ 非 root 用户运行
- ✅ 最小化基础镜像
- ✅ 安全头配置
- ✅ 适当的文件权限

## 环境配置

### 环境变量

#### 构建时环境变量

```bash
# API 配置
VITE_API_BASE_URL=https://api.example.com

# 应用配置
VITE_APP_TITLE="CloudPlatform Dashboard"
VITE_APP_VERSION=1.0.0
```

#### 运行时环境变量

```bash
# 时区设置
TZ=Asia/Shanghai

# Nginx 配置
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=1024
```

### 多环境配置

#### Development 环境

```bash
./build.sh -e development \
  --api-url http://localhost:8081 \
  --app-title "CloudPlatform Dashboard (Dev)"
```

#### Staging 环境

```bash
./build.sh -e staging \
  --api-url https://api-staging.cloudmagnet.lab \
  --app-title "CloudPlatform Dashboard (Staging)"
```

#### Production 环境

```bash
./build.sh -e production \
  --api-url https://api.cloudmagnet.lab \
  --app-title "CloudPlatform Dashboard"
```

## 运行和部署

### 基础运行

```bash
# 基础运行
docker run -d \
  --name cloudplatform-web \
  -p 3000:80 \
  cloudmagnet/cloudplatform-web:latest
```

### 生产环境运行

```bash
# 生产环境运行（推荐）
docker run -d \
  --name cloudplatform-web \
  --restart unless-stopped \
  -p 80:80 \
  --memory=512m \
  --cpus=1 \
  -e TZ=Asia/Shanghai \
  cloudmagnet/cloudplatform-web:latest
```

### Docker Compose 集成

```yaml
version: '3.8'
services:
  cloudplatform-web:
    image: cloudmagnet/cloudplatform-web:latest
    container_name: cloudplatform-web
    restart: unless-stopped
    ports:
      - '80:80'
    environment:
      - TZ=Asia/Shanghai
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost/health']
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1'
        reservations:
          memory: 256M
          cpus: '0.5'
```

## 监控和维护

### 健康检查

```bash
# 容器健康状态
docker ps --filter "name=cloudplatform-web"

# 健康检查端点
curl http://localhost:3000/health

# 查看容器日志
docker logs cloudplatform-web
```

### 性能监控

```bash
# 容器资源使用
docker stats cloudplatform-web

# Nginx 状态（如果启用）
curl http://localhost:3000/nginx_status
```

### 日志管理

```bash
# 实时日志
docker logs -f cloudplatform-web

# 访问日志
docker exec cloudplatform-web tail -f /var/log/nginx/access.log

# 错误日志
docker exec cloudplatform-web tail -f /var/log/nginx/error.log
```

## 故障排除

### 常见问题

#### 构建失败

```bash
# 检查 Docker 服务
docker info

# 清理构建缓存
docker builder prune

# 详细构建日志
./build.sh --verbose
```

#### 容器启动失败

```bash
# 检查容器日志
docker logs cloudplatform-web

# 检查端口占用
netstat -tulpn | grep :80

# 手动启动调试
docker run -it --rm cloudplatform-web:latest sh
```

#### API 连接问题

```bash
# 检查网络连接
docker exec cloudplatform-web curl -I http://api-server:8081

# 检查环境变量
docker exec cloudplatform-web env | grep VITE_
```

### 调试技巧

1. **使用详细模式**: `--verbose` 查看完整构建输出
2. **检查构建日志**: 查看临时目录中的构建日志文件
3. **容器内调试**: 使用 `docker exec -it container sh` 进入容器
4. **网络诊断**: 检查容器间网络连接

## 最佳实践

### 构建优化

1. **利用缓存**: 合理安排 Dockerfile 层顺序
2. **多阶段构建**: 分离构建和运行环境
3. **最小化镜像**: 使用 Alpine 基础镜像
4. **并行构建**: 使用多平台构建提高效率

### 安全考虑

1. **非 root 用户**: 容器内使用 nginx 用户运行
2. **最小权限**: 只安装必要的系统包
3. **安全头**: 配置适当的 HTTP 安全头
4. **定期更新**: 及时更新基础镜像

### 生产部署

1. **资源限制**: 设置合适的内存和 CPU 限制
2. **健康检查**: 配置容器健康检查
3. **日志管理**: 配置日志轮转和持久化
4. **监控告警**: 设置性能和可用性监控

## 与后端集成

### 网络配置

```bash
# 创建自定义网络
docker network create cloudplatform-network

# 启动后端服务
docker run -d \
  --name cloudplatform-srv \
  --network cloudplatform-network \
  cloudmagnet/cloudplatform-srv:latest

# 启动前端服务
docker run -d \
  --name cloudplatform-web \
  --network cloudplatform-network \
  -p 80:80 \
  cloudmagnet/cloudplatform-web:latest
```

### API 代理配置

在 `nginx.conf` 中配置 API 代理：

```nginx
location /api/ {
    proxy_pass http://cloudplatform-srv:8081/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 版本管理

### 版本策略

1. **Git 标签**: 使用 Git 标签作为发布版本
2. **语义化版本**: 遵循 SemVer 规范
3. **构建标识**: 自动添加 Git 提交哈希
4. **环境标签**: 为不同环境创建特定标签

### 发布流程

```bash
# 1. 创建发布标签
git tag -a v1.0.0 -m "Release version 1.0.0"

# 2. 构建发布版本
./build.sh -e production -p

# 3. 多平台构建
./build-multiplatform.sh --platform all --env production --push

# 4. 验证部署
docker run -d --name test-web -p 8080:80 cloudmagnet/cloudplatform-web:v1.0.0
```
