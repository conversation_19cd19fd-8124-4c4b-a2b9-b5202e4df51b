<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ZLMediaKit直接API测试</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
      }
      button {
        padding: 8px 15px;
        background-color: #4caf50;
        color: white;
        border: none;
        cursor: pointer;
        margin-right: 10px;
        margin-bottom: 10px;
      }
      .result {
        margin-top: 10px;
        padding: 10px;
        border: 1px solid #ccc;
        background-color: #f5f5f5;
        white-space: pre-wrap;
        font-family: monospace;
        min-height: 200px;
        max-height: 400px;
        overflow-y: auto;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>ZLMediaKit直接API测试</h1>

      <button id="testApi">API测试</button>
      <button id="testConfig">配置测试</button>
      <button id="startRtspProxy">测试RTSP代理</button>

      <div class="result" id="result">测试结果将显示在这里...</div>
    </div>

    <script>
      function sendRequest(url, method, data, callback) {
        const resultElement = document.getElementById('result');
        resultElement.textContent = `发送请求到 ${url}...`;

        const xhr = new XMLHttpRequest();
        xhr.open(method, url, true);
        xhr.setRequestHeader('Content-Type', 'application/json');

        xhr.onreadystatechange = function () {
          if (xhr.readyState === 4) {
            let result = '';

            result += `状态: ${xhr.status} ${xhr.statusText}\n`;
            result += `响应头:\n`;

            const headers = xhr.getAllResponseHeaders().split('\r\n');
            headers.forEach((header) => {
              if (header) {
                result += `  ${header}\n`;
              }
            });

            result += `\n响应体:\n`;

            try {
              const json = JSON.parse(xhr.responseText);
              result += JSON.stringify(json, null, 2);
            } catch (e) {
              result += xhr.responseText;
            }

            callback(result);
          }
        };

        xhr.onerror = function () {
          callback(`请求错误: ${xhr.status}`);
        };

        if (data) {
          xhr.send(JSON.stringify(data));
        } else {
          xhr.send();
        }
      }

      document.getElementById('testApi').addEventListener('click', function () {
        sendRequest('/api/', 'GET', null, function (result) {
          document.getElementById('result').textContent = result;
        });
      });

      document
        .getElementById('testConfig')
        .addEventListener('click', function () {
          const data = {
            secret: 'yHCY82V4RJVWobtCwzmsC2BNPdhUz0qA',
          };

          sendRequest('/api/getServerConfig', 'POST', data, function (result) {
            document.getElementById('result').textContent = result;
          });
        });

      document
        .getElementById('startRtspProxy')
        .addEventListener('click', function () {
          const rtspUrl =
            'rtsp://admin:yunci123@**************/Streaming/Channels/101';
          const streamId = btoa(rtspUrl)
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '');

          const data = {
            secret: 'yHCY82V4RJVWobtCwzmsC2BNPdhUz0qA',
            vhost: '__defaultVhost__',
            app: 'live',
            stream: streamId,
            url: rtspUrl,
          };

          const result = document.getElementById('result');
          result.textContent = `尝试创建RTSP代理\n`;
          result.textContent += `Stream ID: ${streamId}\n`;
          result.textContent += `请求数据: ${JSON.stringify(
            data,
            null,
            2
          )}\n\n`;

          sendRequest(
            '/api/startProxy',
            'POST',
            data,
            function (responseResult) {
              result.textContent += responseResult;

              // 添加HLS URL
              const hlsUrl = `/live/${streamId}/hls.m3u8`;
              result.textContent += `\n\nHLS URL (如果成功): ${hlsUrl}\n`;
              result.textContent += `测试HLS连接: <a href="${hlsUrl}" target="_blank">${hlsUrl}</a>`;
            }
          );
        });
    </script>
  </body>
</html>
