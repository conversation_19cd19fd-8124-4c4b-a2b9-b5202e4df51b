<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>API测试</title>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .button-group {
        margin: 20px 0;
      }
      button {
        margin: 5px;
        padding: 8px 16px;
        cursor: pointer;
      }
      pre {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
      }
      .result {
        margin-top: 20px;
      }
      .status {
        margin-top: 10px;
        color: #666;
      }
      video {
        width: 100%;
        max-width: 800px;
        margin-top: 20px;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
      }
      input[type='text'] {
        width: 100%;
        padding: 8px;
        margin-bottom: 10px;
      }
    </style>
  </head>
  <body>
    <h1>API测试</h1>

    <div class="form-group">
      <label for="rtspUrl">RTSP URL:</label>
      <input
        type="text"
        id="rtspUrl"
        value="rtsp://admin:yunci123@192.168.20.168/Streaming/Channels/101"
      />
    </div>

    <div class="button-group">
      <button onclick="testApiRoot()">测试API根路径</button>
      <button onclick="testApiVersion()">测试API版本</button>
      <button onclick="testServerConfig()">测试服务器配置</button>
      <button onclick="testCreateRtspProxy()">测试创建RTSP代理</button>
      <button onclick="testRtspToHls()">测试RTSP转HLS</button>
    </div>

    <div class="status" id="status"></div>
    <div class="result">
      <h3>原始响应：</h3>
      <pre id="rawResponse"></pre>
      <h3>解析结果：</h3>
      <pre id="parsedResult"></pre>
    </div>

    <video id="videoPlayer" controls></video>

    <script>
      const API_BASE = 'http://localhost:80';
      const API_SECRET = 'yHCY82V4RJVWobtCwzmsC2BNPdhUz0qA';

      function updateStatus(message) {
        document.getElementById('status').textContent = message;
      }

      async function makeRequest(endpoint, method = 'GET', body = null) {
        try {
          updateStatus(`正在请求: ${endpoint}`);

          const options = {
            method,
            headers: {
              'Content-Type': 'application/json',
            },
          };

          if (body) {
            options.body = JSON.stringify(body);
          }

          const response = await fetch(`${API_BASE}${endpoint}`, options);
          const rawText = await response.text();

          document.getElementById('rawResponse').textContent = rawText;

          try {
            const parsed = JSON.parse(rawText);
            document.getElementById('parsedResult').textContent =
              JSON.stringify(parsed, null, 2);
            updateStatus(`请求成功: ${endpoint}`);
            return parsed;
          } catch (e) {
            document.getElementById('parsedResult').textContent =
              '无法解析JSON响应';
            updateStatus(`请求成功但响应不是JSON: ${endpoint}`);
            return null;
          }
        } catch (error) {
          document.getElementById(
            'rawResponse'
          ).textContent = `错误: ${error.message}`;
          document.getElementById('parsedResult').textContent = '';
          updateStatus(`请求失败: ${error.message}`);
          return null;
        }
      }

      async function testApiRoot() {
        const body = {
          secret: API_SECRET,
        };
        await makeRequest('/index/api/getApiList', 'POST', body);
      }

      async function testApiVersion() {
        const body = {
          secret: API_SECRET,
        };
        await makeRequest('/index/api/version', 'POST', body);
      }

      async function testServerConfig() {
        const body = {
          secret: API_SECRET,
        };
        await makeRequest('/index/api/getServerConfig', 'POST', body);
      }

      async function testCreateRtspProxy() {
        const rtspUrl = document.getElementById('rtspUrl').value;
        const body = {
          secret: API_SECRET,
          vhost: '__defaultVhost__',
          app: 'live',
          stream: 'test',
          url: rtspUrl,
        };
        await makeRequest('/index/api/addStreamProxy', 'POST', body);
      }

      async function testRtspToHls() {
        const rtspUrl = document.getElementById('rtspUrl').value;
        const streamId = btoa(rtspUrl)
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=/g, '');

        updateStatus(`正在创建RTSP代理...`);

        // 1. 创建RTSP代理
        const proxyResult = await testCreateRtspProxy();
        if (!proxyResult || proxyResult.code !== 0) {
          updateStatus(`创建RTSP代理失败: ${JSON.stringify(proxyResult)}`);
          return;
        }

        // 2. 构建HLS URL
        const hlsUrl = `${API_BASE}/live/test/hls.m3u8`;
        updateStatus(`HLS URL: ${hlsUrl}`);

        // 3. 使用HLS.js播放视频
        const video = document.getElementById('videoPlayer');
        if (Hls.isSupported()) {
          const hls = new Hls({
            debug: true,
            enableWorker: true,
            lowLatencyMode: true,
          });

          hls.loadSource(hlsUrl);
          hls.attachMedia(video);

          hls.on(Hls.Events.MANIFEST_PARSED, function () {
            video.play();
            updateStatus(`视频加载成功，正在播放...`);
          });

          hls.on(Hls.Events.ERROR, function (event, data) {
            updateStatus(`HLS错误: ${data.type} - ${data.details}`);
            if (data.fatal) {
              switch (data.type) {
                case Hls.ErrorTypes.NETWORK_ERROR:
                  updateStatus(`网络错误，尝试恢复...`);
                  hls.startLoad();
                  break;
                case Hls.ErrorTypes.MEDIA_ERROR:
                  updateStatus(`媒体错误，尝试恢复...`);
                  hls.recoverMediaError();
                  break;
                default:
                  updateStatus(`无法恢复的错误`);
                  hls.destroy();
                  break;
              }
            }
          });
        } else {
          updateStatus(`您的浏览器不支持HLS.js`);
        }
      }
    </script>
  </body>
</html>
