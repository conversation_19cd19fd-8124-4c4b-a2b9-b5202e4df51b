<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ZLMediaKit配置测试</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
      }
      .test-card {
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }
      .test-card h2 {
        margin-top: 0;
      }
      button {
        padding: 8px 15px;
        background-color: #4caf50;
        color: white;
        border: none;
        cursor: pointer;
        margin-right: 10px;
      }
      .result {
        margin-top: 10px;
        padding: 10px;
        border: 1px solid #ccc;
        background-color: #f5f5f5;
        white-space: pre-wrap;
        font-family: monospace;
        min-height: 50px;
        max-height: 200px;
        overflow-y: auto;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>ZLMediaKit配置测试</h1>

      <div class="test-card">
        <h2>1. 基础连接测试</h2>
        <p>检查是否可以访问ZLMediaKit服务器</p>
        <button id="testConnection">测试连接</button>
        <div class="result" id="connectionResult">等待测试...</div>
      </div>

      <div class="test-card">
        <h2>2. API接口测试</h2>
        <p>测试API接口是否可访问</p>
        <button id="testAPI">测试API</button>
        <div class="result" id="apiResult">等待测试...</div>
      </div>

      <div class="test-card">
        <h2>3. API版本和配置</h2>
        <p>获取API版本信息</p>
        <button id="testVersion">获取版本</button>
        <div class="result" id="versionResult">等待测试...</div>
      </div>

      <div class="test-card">
        <h2>4. 直接创建RTSP代理</h2>
        <p>测试直接通过CURL创建RTSP代理</p>
        <div class="result" id="curlCommand">
          curl -i -X POST -d '{ "secret": "yHCY82V4RJVWobtCwzmsC2BNPdhUz0qA",
          "vhost": "__defaultVhost__", "app": "live", "stream": "test", "url":
          "rtsp://admin:yunci123@**************/Streaming/Channels/101" }' -H
          "Content-Type: application/json" http://localhost/api/startProxy
        </div>
      </div>
    </div>

    <script>
      document
        .getElementById('testConnection')
        .addEventListener('click', async function () {
          const resultElement = document.getElementById('connectionResult');
          resultElement.textContent = '测试中...';

          try {
            // 测试根路径
            const response = await fetch('/', { method: 'GET' });
            const text = await response.text();

            resultElement.textContent = `
状态: ${response.status} ${response.statusText}
内容长度: ${text.length} 字节
内容类型: ${response.headers.get('content-type')}

响应内容 (前100字符): 
${text.substring(0, 100)}...`;
          } catch (error) {
            resultElement.textContent = `错误: ${error.message}`;
          }
        });

      document
        .getElementById('testAPI')
        .addEventListener('click', async function () {
          const resultElement = document.getElementById('apiResult');
          resultElement.textContent = '测试中...';

          try {
            // 测试API前缀
            const response = await fetch('/api/', {
              method: 'GET',
              headers: {
                Accept: 'application/json',
              },
            });

            // 获取响应文本
            const text = await response.text();
            let formattedResponse;

            try {
              // 尝试格式化为JSON
              const json = JSON.parse(text);
              formattedResponse = JSON.stringify(json, null, 2);
            } catch (e) {
              formattedResponse = text;
            }

            resultElement.textContent = `
状态: ${response.status} ${response.statusText}
内容类型: ${response.headers.get('content-type')}

响应:
${formattedResponse}`;
          } catch (error) {
            resultElement.textContent = `错误: ${error.message}`;
          }
        });

      document
        .getElementById('testVersion')
        .addEventListener('click', async function () {
          const resultElement = document.getElementById('versionResult');
          resultElement.textContent = '测试中...';

          try {
            // 获取版本信息
            const response = await fetch('/api/getServerConfig', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                secret: 'yHCY82V4RJVWobtCwzmsC2BNPdhUz0qA',
              }),
            });

            // 获取响应文本
            const text = await response.text();
            let formattedResponse;

            try {
              // 尝试格式化为JSON
              const json = JSON.parse(text);
              formattedResponse = JSON.stringify(json, null, 2);
            } catch (e) {
              formattedResponse = text;
            }

            resultElement.textContent = `
状态: ${response.status} ${response.statusText}
内容类型: ${response.headers.get('content-type')}

响应:
${formattedResponse}`;
          } catch (error) {
            resultElement.textContent = `错误: ${error.message}`;
          }
        });
    </script>
  </body>
</html>
