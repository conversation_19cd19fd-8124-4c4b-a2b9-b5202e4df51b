<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RTSP转HLS测试</title>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
      }
      input[type='text'] {
        width: 100%;
        padding: 8px;
        box-sizing: border-box;
      }
      button {
        padding: 10px 15px;
        background-color: #4caf50;
        color: white;
        border: none;
        cursor: pointer;
      }
      video {
        width: 100%;
        margin-top: 20px;
        background-color: #000;
      }
      .status {
        margin-top: 10px;
        padding: 10px;
        border: 1px solid #ccc;
        background-color: #f5f5f5;
        white-space: pre-wrap;
        font-family: monospace;
        max-height: 200px;
        overflow-y: auto;
      }
      .raw-response {
        margin-top: 10px;
        padding: 10px;
        border: 1px solid #ccc;
        background-color: #f5f5f5;
        white-space: pre-wrap;
        font-family: monospace;
        max-height: 200px;
        overflow-y: auto;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>RTSP转HLS测试</h1>

      <div class="form-group">
        <label for="rtspUrl">RTSP URL:</label>
        <input
          type="text"
          id="rtspUrl"
          value="rtsp://admin:yunci123@192.168.20.168/Streaming/Channels/101"
        />
      </div>

      <div class="form-group">
        <label for="secret">API密钥:</label>
        <input
          type="text"
          id="secret"
          value="yHCY82V4RJVWobtCwzmsC2BNPdhUz0qA"
        />
      </div>

      <button id="startButton">开始测试</button>
      <button id="directTestButton">直接测试HLS播放</button>
      <button id="deleteStreamButton">删除流</button>

      <div class="status" id="status">状态: 等待开始测试...</div>
      <div class="raw-response" id="rawResponse">原始响应: 无</div>

      <video id="videoPlayer" controls></video>
    </div>

    <script>
      const API_BASE = 'http://localhost:80';
      const API_SECRET = 'yHCY82V4RJVWobtCwzmsC2BNPdhUz0qA';

      // 生成streamId
      function generateStreamId(rtspUrl) {
        return btoa(rtspUrl)
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=/g, '');
      }

      // 删除流
      async function deleteStream(streamId) {
        const secret = document.getElementById('secret').value;
        const statusElement = document.getElementById('status');
        const rawResponseElement = document.getElementById('rawResponse');

        statusElement.textContent = `正在删除流: ${streamId}...\n`;

        try {
          const response = await fetch(`${API_BASE}/index/api/delStreamProxy`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              secret: secret,
              vhost: '__defaultVhost__',
              app: 'live',
              stream: streamId,
            }),
          });

          const responseText = await response.text();
          rawResponseElement.textContent = `原始响应: ${responseText}`;

          try {
            const result = JSON.parse(responseText);
            statusElement.textContent += `删除流结果: ${JSON.stringify(
              result,
              null,
              2
            )}\n`;
            return result;
          } catch (e) {
            statusElement.textContent += `解析响应失败: ${e.message}\n`;
            return null;
          }
        } catch (error) {
          statusElement.textContent += `删除流失败: ${error.message}\n`;
          return null;
        }
      }

      document
        .getElementById('deleteStreamButton')
        .addEventListener('click', async function () {
          const rtspUrl = document.getElementById('rtspUrl').value;
          const streamId = generateStreamId(rtspUrl);
          await deleteStream(streamId);
        });

      document
        .getElementById('startButton')
        .addEventListener('click', async function () {
          const rtspUrl = document.getElementById('rtspUrl').value;
          const secret = document.getElementById('secret').value;
          const statusElement = document.getElementById('status');
          const rawResponseElement = document.getElementById('rawResponse');
          const videoElement = document.getElementById('videoPlayer');

          if (!rtspUrl) {
            statusElement.textContent = '错误: 请输入RTSP URL';
            return;
          }

          statusElement.textContent = '状态: 正在请求RTSP代理...\n';

          try {
            // 生成streamId
            const streamId = generateStreamId(rtspUrl);

            statusElement.textContent += `Stream ID: ${streamId}\n`;

            // 先尝试删除已存在的流
            statusElement.textContent += `检查并删除已存在的流...\n`;
            await deleteStream(streamId);

            statusElement.textContent += `API请求地址: ${API_BASE}/index/api/addStreamProxy\n`;

            const requestBody = {
              secret: secret,
              vhost: '__defaultVhost__',
              app: 'live',
              stream: streamId,
              url: rtspUrl,
            };

            statusElement.textContent += `请求体: ${JSON.stringify(
              requestBody,
              null,
              2
            )}\n`;

            // 发送请求到ZLMediaKit API
            const response = await fetch(
              `${API_BASE}/index/api/addStreamProxy`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody),
              }
            );

            statusElement.textContent += `状态码: ${response.status} ${response.statusText}\n`;
            statusElement.textContent += `响应头:\n`;
            response.headers.forEach((value, key) => {
              statusElement.textContent += `  ${key}: ${value}\n`;
            });

            // 获取原始响应文本
            const responseText = await response.text();
            rawResponseElement.textContent = `原始响应: ${responseText}`;

            // 尝试解析JSON
            let result;
            try {
              if (responseText.trim()) {
                result = JSON.parse(responseText);
                statusElement.textContent += `API响应解析成功: ${JSON.stringify(
                  result,
                  null,
                  2
                )}\n`;
              } else {
                statusElement.textContent += `警告: 服务器返回了空响应\n`;
                result = { code: -1, msg: 'Empty response' };
              }
            } catch (parseError) {
              statusElement.textContent += `JSON解析错误: ${parseError.message}\n`;
              statusElement.textContent += `响应不是有效的JSON: ${responseText}\n`;
              result = { code: -1, msg: parseError.message };
            }

            if (result && result.code === 0) {
              // 构建HLS URL并尝试播放
              const hlsUrl = `${API_BASE}/live/${streamId}/hls.m3u8`;
              statusElement.textContent += `HLS URL: ${hlsUrl}\n`;

              // 使用HLS.js播放视频
              if (Hls.isSupported()) {
                const hls = new Hls({ debug: true });
                hls.loadSource(hlsUrl);
                hls.attachMedia(videoElement);

                hls.on(Hls.Events.MANIFEST_PARSED, function () {
                  videoElement.play();
                  statusElement.textContent += `视频加载成功，正在播放...\n`;
                });

                hls.on(Hls.Events.ERROR, function (event, data) {
                  statusElement.textContent += `HLS错误: ${data.type} - ${data.details}\n`;
                  if (data.fatal) {
                    statusElement.textContent += `致命错误: ${data.type}\n`;
                    switch (data.type) {
                      case Hls.ErrorTypes.NETWORK_ERROR:
                        statusElement.textContent += `网络错误，尝试恢复...\n`;
                        hls.startLoad();
                        break;
                      case Hls.ErrorTypes.MEDIA_ERROR:
                        statusElement.textContent += `媒体错误，尝试恢复...\n`;
                        hls.recoverMediaError();
                        break;
                      default:
                        statusElement.textContent += `无法恢复的错误\n`;
                        hls.destroy();
                        break;
                    }
                  }
                });
              } else {
                statusElement.textContent += `您的浏览器不支持HLS.js\n`;
              }
            } else {
              statusElement.textContent += `创建RTSP代理失败: ${
                result ? JSON.stringify(result) : '未知错误'
              }\n`;
            }
          } catch (error) {
            statusElement.textContent += `错误: ${error.message}\n`;
            console.error(error);
          }
        });

      // 直接测试HLS播放
      document
        .getElementById('directTestButton')
        .addEventListener('click', function () {
          const rtspUrl = document.getElementById('rtspUrl').value;
          const statusElement = document.getElementById('status');
          const videoElement = document.getElementById('videoPlayer');

          if (!rtspUrl) {
            statusElement.textContent = '错误: 请输入RTSP URL';
            return;
          }

          // 生成streamId
          const streamId = generateStreamId(rtspUrl);

          // 构建HLS URL
          const hlsUrl = `${API_BASE}/live/${streamId}/hls.m3u8`;
          statusElement.textContent = `直接尝试HLS播放\nStream ID: ${streamId}\nHLS URL: ${hlsUrl}\n`;

          // 使用HLS.js播放视频
          if (Hls.isSupported()) {
            const hls = new Hls({
              debug: true,
              // enableWorker: true,
              // lowLatencyMode: true,
              // maxBufferLength: 30,
              // maxMaxBufferLength: 600,
              // maxBufferSize: 60 * 1000 * 1000,
              // maxBufferHole: 0.5,
              // highBufferWatchdogPeriod: 2,
              // nudgeMaxRetry: 5,
              // maxFragLookUpTolerance: 0.25,
              // liveSyncDurationCount: 3,
              // liveMaxLatencyDurationCount: 10,
              // liveDurationInfinity: true
            });
            hls.loadSource(hlsUrl);
            hls.attachMedia(videoElement);

            hls.on(Hls.Events.MANIFEST_PARSED, function () {
              videoElement.play();
              statusElement.textContent += `视频加载成功，正在播放...\n`;
            });

            hls.on(Hls.Events.ERROR, function (event, data) {
              statusElement.textContent += `HLS错误: ${data.type} - ${data.details}\n`;
              console.log('HLS错误:', event, data);
            });
          } else {
            statusElement.textContent += `您的浏览器不支持HLS.js\n`;
          }
        });
    </script>
  </body>
</html>
