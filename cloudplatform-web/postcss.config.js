module.exports = {
  plugins: {
    'postcss-px-to-viewport-8-plugin': {
      viewportWidth: 1920, // 设计稿的视口宽度
      viewportHeight: 1080, // 设计稿的视口高度
      unitPrecision: 8, // 单位转换后保留的精度
      viewportUnit: 'vw', // 指定需要转换成的视窗单位，默认vw
      fontViewportUnit: 'vw', // 字体使用的视口单位，通常字体大小用vh更合适
      selectorBlackList: [
        '.ignore',
        '.hairlines',
        /^body$/,
        /^html$/,
        /^#app$/,
        /^\.el-/, // Element UI 组件
        /^\.arco-/, // Arco Design 组件
        /^\.ant-/, // Ant Design 组件
        /^\.vant-/, // Vant UI 组件
        /^\.van-/, // Vant UI 组件简写
        /^\.n-/, // Naive UI 组件
        /^\.ivu-/, // iView UI 组件
        /^\.mu-/, // Muse UI 组件
        /^\.v-/, // Vuetify 组件
        /^\.md-/, // Vue Material 组件
      ], // 需要忽略的CSS选择器，支持正则
      exclude: [
        /node_modules/,
        /dist/,
        /\.git/,
        /public/,
        /src\/assets\/fonts/,
        /src\/assets\/icons/,
      ], // 忽略某些文件夹下的文件
      replace: true, // 是否直接更换属性值，而不添加备用属性
      // propList: ['*', '!border*', '!font*', '!line-height'], // 指定要转换的属性，*表示全部，!表示不转换
      landscapeUnit: 'vh', // 横屏时使用的单位
      landscapeWidth: 1080, // 横屏时使用的视口宽度
      mediaQuery: true, // 允许在媒体查询中转换px
      minPixelValue: 0, // 设置最小的转换数值，小于该值的px单位不会被转换
      customFunctions: {
        px2vw: (pixels) => {
          return (pixels * 100) / 1920 + 'vw';
        },
        px2vh: (pixels) => {
          return (pixels * 100) / 1080 + 'vh';
        },
      },
    },
    'autoprefixer': {
      overrideBrowserslist: [
        'last 2 versions',
        '> 1%',
        'iOS 7',
        'last 3 iOS versions',
      ],
    },
  },
};
