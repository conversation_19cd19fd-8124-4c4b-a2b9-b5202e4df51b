# CloudPlatform-web 生产级优化 Dockerfile
# 基于 Node.js 18+ 和 Nginx Alpine 的多阶段构建
# 支持 Vue.js 3+ 和 Vite 构建工具

# ============================================================================
# 第一阶段：Node.js 构建阶段
# ============================================================================
FROM node:18-alpine AS builder

# 设置构建阶段的标签信息
LABEL stage="builder" \
      description="CloudPlatform-web Vue.js 构建阶段" \
      node.version="18" \
      vue.version="3.2.40"

# 设置工作目录
WORKDIR /app

# 安装构建时需要的系统工具（最小化安装）
# git: 用于获取版本信息
# python3, make, g++: 用于编译原生依赖
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++

# 设置构建参数，允许在构建时自定义
ARG NODE_ENV=production
ARG VITE_API_BASE_URL=http://localhost:8081
ARG VITE_APP_TITLE="CloudPlatform Dashboard"
ARG VITE_APP_VERSION=""

# 设置 Node.js 环境变量
ENV NODE_ENV=${NODE_ENV}
ENV VITE_API_BASE_URL=${VITE_API_BASE_URL}
ENV VITE_APP_TITLE=${VITE_APP_TITLE}
ENV VITE_APP_VERSION=${VITE_APP_VERSION}

# 优化 npm 配置
ENV NPM_CONFIG_REGISTRY=https://registry.npmjs.org
ENV NPM_CONFIG_CACHE=/tmp/.npm
ENV NPM_CONFIG_FUND=false
ENV NPM_CONFIG_AUDIT=false

# 复制 package 文件（利用 Docker 层缓存优化）
# 先复制 package.json 和 pnpm-lock.yaml，这样依赖下载可以被缓存
COPY package.json pnpm-lock.yaml ./

# 安装 pnpm 包管理器
RUN npm install -g pnpm@latest

# 配置 pnpm 使用官方 npm 源
RUN pnpm config set registry https://registry.npmjs.org

# 下载项目依赖（这一层会被缓存，除非 package.json 发生变化）
RUN pnpm install --frozen-lockfile --production=false

# 复制源代码（放在依赖安装之后，避免代码变更导致重新下载依赖）
COPY . .

# 构建应用程序
# 使用生产配置进行构建
RUN pnpm run build

# 验证构建结果
RUN ls -la dist/ && \
    test -f dist/index.html && \
    echo "构建产物验证成功"

# ============================================================================
# 第二阶段：Node.js 生产运行阶段
# ============================================================================
FROM node:18-alpine AS runtime

# 设置镜像元数据和标签
LABEL maintainer="CloudMagnet Lab <<EMAIL>>" \
      description="CloudPlatform-web 前端服务 - Vue.js 3 + Vite + Node.js" \
      version="1.0.0" \
      vue.version="3.2.40" \
      vite.version="3.2.5" \
      node.version="18" \
      build.date="2025-06-19" \
      project.name="cloudplatform-web" \
      project.group="cloudmagnet"

# 设置运行时参数
ARG APP_USER=node
ARG APP_UID=1000
ARG APP_GID=1000

# 设置时区为上海时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装运行时必要的系统工具（最小化安装）
# curl: 用于健康检查
# ca-certificates: SSL/TLS 证书
RUN apk add --no-cache \
    curl \
    ca-certificates \
    tzdata

# 创建应用目录
WORKDIR /app

# 安装 serve 用于提供静态文件服务
RUN npm install -g serve@latest

# 从构建阶段复制构建产物
COPY --from=builder --chown=${APP_USER}:${APP_USER} /app/dist ./dist

# 复制 serve 配置文件
COPY --chown=${APP_USER}:${APP_USER} serve.json ./serve.json

# 验证复制的文件
RUN ls -la dist/ && \
    test -f dist/index.html && \
    test -f serve.json && \
    echo "静态文件和配置复制成功"

# ============================================================================
# 运行时配置
# ============================================================================

# 切换到非 root 用户（安全最佳实践）
USER ${APP_USER}

# 暴露应用端口
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=production \
    PORT=3000 \
    HOST=0.0.0.0

# 健康检查配置
# --interval: 健康检查间隔时间（30秒）
# --timeout: 单次健康检查超时时间（10秒）
# --start-period: 容器启动后等待时间（30秒）
# --retries: 连续失败次数后标记为不健康（3次）
HEALTHCHECK --interval=30s \
            --timeout=10s \
            --start-period=30s \
            --retries=3 \
    CMD curl -f http://localhost:3000/ || exit 1

# 设置容器启动时的信号处理
# SIGTERM: 优雅关闭信号，Node.js 会处理这个信号进行优雅关闭
STOPSIGNAL SIGTERM

# 启动命令
# 使用 serve 提供静态文件服务，使用配置文件
CMD ["serve", "-c", "serve.json", "-l", "3000", "--no-clipboard"]

# ============================================================================
# 构建和运行最佳实践说明
# ============================================================================

# 构建命令示例：
# 基础构建：
# docker build -t cloudmagnet/cloudplatform-web:latest .
# 
# 自定义 API URL 构建：
# docker build --build-arg VITE_API_BASE_URL=https://api.example.com -t cloudmagnet/cloudplatform-web:latest .
# 
# 多平台构建：
# docker buildx build --platform linux/amd64,linux/arm64 -t cloudmagnet/cloudplatform-web:latest .

# 运行命令示例：
# 基础运行：
# docker run -d --name cloudplatform-web -p 3000:3000 cloudmagnet/cloudplatform-web:latest
#
# 生产环境运行（推荐）：
# docker run -d \
#   --name cloudplatform-web \
#   --restart unless-stopped \
#   -p 3000:3000 \
#   --memory=256m \
#   --cpus=0.5 \
#   cloudmagnet/cloudplatform-web:latest

# 环境变量说明：
# 构建时环境变量：
# - VITE_API_BASE_URL: API 基础 URL
# - VITE_APP_TITLE: 应用标题
# - VITE_APP_VERSION: 应用版本
# 
# 运行时环境变量：
# - TZ: 时区设置
# - NGINX_WORKER_PROCESSES: Nginx 工作进程数
# - NGINX_WORKER_CONNECTIONS: 每个工作进程的连接数

# 数据卷挂载建议：
# - /var/log/nginx: Nginx 日志目录
# - /etc/nginx/conf.d: 额外的 Nginx 配置目录

# 资源限制建议：
# - 内存: 最少 256MB，推荐 512MB
# - CPU: 最少 0.5 核，推荐 1 核
# - 磁盘: 最少 1GB，用于日志和缓存

# 监控和健康检查：
# - 健康检查端点: http://localhost/
# - Nginx 状态: 可配置 /nginx_status 端点
# - 访问日志: /var/log/nginx/access.log
# - 错误日志: /var/log/nginx/error.log
