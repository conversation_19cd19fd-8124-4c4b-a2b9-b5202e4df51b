<template>
  <div class="video-player-container">
    <div v-if="error" class="error-message">{{ error }}</div>
    <div v-if="loading" class="loading-message">正在加载视频流...</div>
    <video
      ref="videoPlayer"
      controls
      autoplay
      width="100%"
      height="100%"
      class="custom-video-player"
    ></video>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
  import Hls from 'hls.js';

  // API配置 - 使用相对路径通过nginx代理访问
  const API_BASE = 'http://localhost:80'; // 通过nginx代理访问ZLMediaKit
  const API_SECRET = 'xiaoye71'; // 与服务器配置一致的API密钥
  const APP_NAME = 'CloudPlatform';
  const SCHEME = 'rtsp';
  const VHOST = '__defaultVhost__';

  // 组件属性
  const props = defineProps<{
    rtspUrl: string;
  }>();

  // 组件状态
  const videoPlayer = ref<HTMLVideoElement | null>(null);
  const error = ref<string>('');
  const loading = ref<boolean>(false);
  let hls: Hls | null = null;
  const streamKey = ref<string>('');
  const isDestroyed = ref<boolean>(false);

  // 生成streamId
  const generateStreamId = (rtspUrl: string) => {
    return btoa(rtspUrl)
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  };

  // 检查流是否存在
  const checkStreamExists = async (streamId: string) => {
    try {
      console.log('检查流是否存在:', streamId);
      const response = await fetch(`${API_BASE}/index/api/isMediaOnline`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: API_SECRET,
          schema: SCHEME,
          vhost: VHOST,
          app: APP_NAME,
          stream: streamId,
        }),
      });

      const result = await response.json();
      console.log('流状态检查结果:', result);
      return result.code === 0 && result.online === 1;
    } catch (error) {
      console.error('检查流是否存在失败:', error);
      return false;
    }
  };

  // 删除流
  const deleteStream = async (key: string) => {
    try {
      console.log('删除流:', key);
      const response = await fetch(`${API_BASE}/index/api/delStreamProxy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: API_SECRET,
          key: key,
        }),
      });

      const result = await response.json();
      console.log('删除流结果:', result);
      return result;
    } catch (error) {
      console.error('删除流失败:', error);
      throw error;
    }
  };

  // 创建RTSP代理
  const createRtspProxy = async (rtspUrl: string, streamId: string) => {
    try {
      console.log('创建RTSP代理:', rtspUrl);
      const response = await fetch(`${API_BASE}/index/api/addStreamProxy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: API_SECRET,
          vhost: VHOST,
          app: APP_NAME,
          stream: streamId,
          url: rtspUrl,
        }),
      });

      const result = await response.json();
      console.log('创建RTSP代理结果:', result);

      if (result.code === -1 && result.msg?.includes('already exists')) {
        console.log('流已存在');
        throw new Error('流已存在');
      }

      if (result.code !== 0 && result.code !== -1) {
        throw new Error(`创建RTSP代理失败: ${JSON.stringify(result)}`);
      }

      // 保存stream key
      if (result.data && result.data.key) {
        streamKey.value = result.data.key;
        console.log('保存的stream key:', streamKey.value);
      }

      return result;
    } catch (error) {
      console.error('创建RTSP代理失败:', error);
      throw error;
    }
  };

  // 构建HLS URL - 通过nginx代理访问
  const buildHlsUrl = (streamId: string) => {
    return `${API_BASE}/${APP_NAME}/${streamId}/hls.m3u8`;
  };

  // 初始化播放器
  const initPlayer = async () => {
    // 如果视频播放器元素不存在或组件已被销毁，则直接返回
    if (!videoPlayer.value || isDestroyed.value) return;

    loading.value = true;
    error.value = '';

    try {
      // 验证RTSP URL
      if (!props.rtspUrl) {
        throw new Error('RTSP URL不能为空');
      }

      // 清理旧的播放器实例
      if (hls) {
        hls.destroy();
        hls = null;
      }

      // 生成streamId
      const streamId = generateStreamId(props.rtspUrl);
      console.log('Stream ID:', streamId);

      // 检查流是否存在
      const streamExists = await checkStreamExists(streamId);
      console.log('Stream exists:', streamExists);

      // 如果流不存在，创建新的流
      if (!streamExists) {
        console.log('创建新的流...');
        try {
          await createRtspProxy(props.rtspUrl, streamId);
        } catch (error: any) {
          // 如果错误是因为流已存在，我们可以直接继续使用
          if (error.message && error.message.includes('流已存在')) {
            console.log('流已存在，直接使用');
          } else {
            // 其他错误则抛出
            throw error;
          }
        }
      } else {
        console.log('流已存在，直接使用');
      }

      // 构建HLS URL
      const hlsUrl = buildHlsUrl(streamId);
      console.log('RTSP URL:', props.rtspUrl);
      console.log('Stream ID:', streamId);
      console.log('HLS URL:', hlsUrl);

      // 使用HLS.js播放视频
      if (Hls.isSupported()) {
        hls = new Hls({
          debug: false,

          // 启用低延迟模式
          lowLatencyMode: true,

          // 降低初始缓冲区大小
          maxBufferLength: 1,

          // 限制最大缓冲区大小
          maxMaxBufferLength: 2,

          // 减小缓冲区大小限制
          maxBufferSize: 2 * 1000 * 1000, // 2MB

          // 减小缓冲区空洞容差
          maxBufferHole: 0.1,

          // 高缓冲区监控周期
          highBufferWatchdogPeriod: 1,

          // 增加最大重试次数
          nudgeMaxRetry: 6,

          // 减小分片查找容差
          maxFragLookUpTolerance: 0.1,

          // 减少直播同步持续时间
          liveSyncDurationCount: 1,

          // 减少最大延迟持续时间
          liveMaxLatencyDurationCount: 2,

          // 启用直播同步
          liveDurationInfinity: false,

          // 从最新片段开始播放
          startLevel: -1,

          // 启用实时模式
          enableWorker: true,

          // 减少清单加载超时时间
          manifestLoadingTimeOut: 5000,
          manifestLoadingMaxRetry: 2,

          // 减少级别加载超时时间
          levelLoadingTimeOut: 5000,
          levelLoadingMaxRetry: 2,

          // 减少分片加载超时时间
          fragLoadingTimeOut: 5000,
          fragLoadingMaxRetry: 2,

          // 预加载分片
          startFragPrefetch: true,

          // 启用带宽测试
          testBandwidth: true,

          // 启用渐进式加载
          progressive: true,

          // 减少回放缓冲区长度
          backBufferLength: 30,

          // 自适应比特率配置
          abrEwmaDefaultEstimate: 500000,
          abrEwmaFastLive: 3,
          abrEwmaSlowLive: 9,

          // 实时流配置
          liveBackBufferLength: 0,
        });

        hls.on(Hls.Events.MANIFEST_LOADING, (event, data) => {
          console.log('清单加载中:', event, data); // 开始加载m3u8清单文件
        });

        hls.on(Hls.Events.MANIFEST_LOADED, (event, data) => {
          console.log('清单加载完成:', event, data); // m3u8清单文件加载完成
        });

        hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
          console.log('清单解析完成:', event, data); // m3u8清单文件解析完成，可以开始播放
          if (!isDestroyed.value && videoPlayer.value) {
            console.log('尝试播放视频...');
            videoPlayer.value.play().catch((err) => {
              console.warn('视频播放失败:', err);
              videoPlayer.value?.load();
              videoPlayer.value
                ?.play()
                .catch((e) => console.error('重试播放失败:', e));
            });
            error.value = '';
            loading.value = false;
          }
        });

        hls.on(Hls.Events.LEVEL_LOADED, (event, data) => {
          console.log('级别加载完成:', event, data); // 视频质量级别信息加载完成
        });

        hls.on(Hls.Events.FRAG_LOADED, (event, data) => {
          console.log('分片加载完成:', event, data); // 视频分片(.ts文件)加载完成
        });

        hls.on(Hls.Events.FRAG_PARSED, (event, data) => {
          console.log('分片解析完成:', event, data); // 视频分片解析完成，准备添加到缓冲区
        });

        hls.on(Hls.Events.BUFFER_APPENDING, (event, data) => {
          console.log('缓冲区添加中:', event, data); // 正在将视频数据添加到媒体缓冲区
        });

        hls.on(Hls.Events.BUFFER_APPENDED, (event, data) => {
          console.log('缓冲区添加完成:', event, data); // 视频数据已成功添加到媒体缓冲区
        });

        hls.on(Hls.Events.BUFFER_FLUSHING, (event, data) => {
          console.log('缓冲区刷新中:', event, data); // 正在清空媒体缓冲区
        });

        hls.on(Hls.Events.BUFFER_FLUSHED, (event, data) => {
          console.log('缓冲区刷新完成:', event, data); // 媒体缓冲区已清空
        });

        hls.on(Hls.Events.ERROR, (event, data) => {
          console.error('HLS错误:', event, data);
          if (data.fatal) {
            switch (data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                console.warn('网络错误，立即重试...');
                hls?.startLoad();
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                console.warn('媒体错误，尝试恢复...');
                hls?.recoverMediaError();
                break;
              default:
                error.value = `播放错误: ${data.details}`;
                loading.value = false;
                // 对于其他错误，尝试重新初始化播放器
                setTimeout(() => {
                  initPlayer();
                }, 1000);
                break;
            }
          }
        });

        // 监听缓冲区状态
        hls.on(Hls.Events.BUFFER_APPENDING, () => {
          if (videoPlayer.value) {
            // 尝试保持低延迟
            if (videoPlayer.value.buffered.length > 0) {
              const currentDelay =
                videoPlayer.value.buffered.end(0) -
                videoPlayer.value.currentTime;
              if (currentDelay > 1) {
                // 如果延迟超过1秒，尝试追赶直播
                videoPlayer.value.playbackRate = 1.1;
              } else {
                videoPlayer.value.playbackRate = 1.0;
              }
            }
          }
        });

        console.log('开始加载HLS源:', hlsUrl);
        hls.loadSource(hlsUrl); // 加载HLS流地址

        console.log('附加到视频元素');
        hls.attachMedia(videoPlayer.value); // 将HLS实例附加到视频元素

        if (videoPlayer.value) {
          videoPlayer.value.addEventListener('loadedmetadata', () => {
            console.log('视频元数据已加载'); // 视频元数据（如时长、尺寸）已加载
          });

          videoPlayer.value.addEventListener('loadeddata', () => {
            console.log('视频数据已加载'); // 视频第一帧数据已加载
          });

          videoPlayer.value.addEventListener('canplay', () => {
            console.log('视频可以播放'); // 视频已缓冲足够数据可以开始播放
            loading.value = false;
          });

          videoPlayer.value.addEventListener('playing', () => {
            console.log('视频正在播放'); // 视频开始播放
            loading.value = false;
          });

          videoPlayer.value.addEventListener('waiting', () => {
            console.log('视频正在缓冲'); // 视频因缓冲不足而暂停
            loading.value = true;
          });

          videoPlayer.value.addEventListener('error', (e) => {
            console.error('视频元素错误:', e); // 视频元素发生错误
            error.value = `视频错误: ${
              videoPlayer.value?.error?.message || '未知错误'
            }`;
            loading.value = false;
          });
        }
      } else {
        error.value = '您的浏览器不支持HLS.js';
        loading.value = false;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '未知错误';
      loading.value = false;
      console.error('初始化播放器失败:', err);
    }
  };

  // 监听rtspUrl变化
  watch(() => props.rtspUrl, initPlayer);

  onMounted(initPlayer);

  onBeforeUnmount(() => {
    isDestroyed.value = true;

    // 先停止视频播放
    if (videoPlayer.value) {
      videoPlayer.value.pause();
      videoPlayer.value.src = '';
    }

    // 然后销毁HLS实例
    if (hls) {
      hls.destroy();
      hls = null;
    }

    // 最后删除流
    if (streamKey.value) {
      deleteStream(streamKey.value).catch((error) => {
        console.error('组件卸载时删除流失败:', error);
      });
    }
  });
</script>

<style scoped>
  .video-player-container {
    width: 100%;
    height: 100%;
    background-color: #000;
    position: relative;
  }

  video {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: #000;
  }

  /* 自定义视频播放器样式 */
  .custom-video-player::-webkit-media-controls {
    display: none !important;
  }

  .custom-video-player::-webkit-media-controls-panel {
    display: none !important;
  }

  .custom-video-player::-webkit-media-controls-play-button {
    display: none !important;
  }

  .custom-video-player::-webkit-media-controls-timeline {
    display: none !important;
  }

  .custom-video-player::-webkit-media-controls-current-time-display {
    display: none !important;
  }

  .custom-video-player::-webkit-media-controls-time-remaining-display {
    display: none !important;
  }

  .custom-video-player::-webkit-media-controls-mute-button {
    display: none !important;
  }

  .custom-video-player::-webkit-media-controls-volume-slider {
    display: none !important;
  }

  .custom-video-player::-webkit-media-controls-fullscreen-button {
    display: none !important;
  }

  .custom-video-player::-webkit-media-controls-rewind-button {
    display: none !important;
  }

  .custom-video-player::-webkit-media-controls-return-to-realtime-button {
    display: none !important;
  }

  .custom-video-player::-webkit-media-controls-toggle-closed-captions-button {
    display: none !important;
  }

  /* Firefox */
  .custom-video-player::-moz-range-track {
    display: none !important;
  }

  .custom-video-player::-moz-range-thumb {
    display: none !important;
  }

  /* 禁用鼠标事件 */
  .custom-video-player {
    pointer-events: none;
  }

  .error-message,
  .loading-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 10px 20px;
    border-radius: 4px;
    z-index: 1;
  }

  .loading-message {
    background-color: rgba(0, 0, 0, 0.5);
  }
</style>
