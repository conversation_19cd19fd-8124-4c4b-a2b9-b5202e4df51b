<script lang="ts">
  import { defineComponent, computed } from 'vue';
  import { Card } from '@arco-design/web-vue';
  import { IconCommon } from '@arco-design/web-vue/es/icon';
  import cardTitleBg from '@/assets/images/dataScreen/卡片组件/标题渐变背景.png';

  export default defineComponent({
    name: 'BaseDataCard',
    components: {
      ACard: Card,
      IconCommon,
    },
    props: {
      /**
       * 卡片标题
       */
      title: {
        type: String,
        required: true,
      },
      /**
       * 是否显示标题图标
       */
      showIcon: {
        type: Boolean,
        default: true,
      },
      /**
       * 卡片最小高度
       */
      minHeight: {
        type: String,
        default: '200px',
      },
      /**
       * 是否可点击
       */
      clickable: {
        type: Boolean,
        default: false,
      },
      /**
       * 标题栏高度
       */
      titleHeight: {
        type: String,
        default: '50px',
      },
    },
    emits: ['click'],
    setup(props, { emit }) {
      /**
       * 计算卡片样式
       */
      const cardStyles = computed(() => ({
        cursor: props.clickable ? 'pointer' : 'default',
      }));

      /**
       * 计算header样式
       */
      const headerStyle = computed(() => ({
        border: 'none',
        padding: 0,
        height: props.titleHeight,
        backgroundColor: 'transparent',
        backgroundImage: `url(${cardTitleBg})`,
        backgroundSize: '100% 100%',
        backgroundRepeat: 'no-repeat',
        borderRadius: '0 0 0 0',
      }));

      /**
       * 计算body样式
       */
      const bodyStyle = computed(() => ({
        padding: '20px',
        backgroundColor: 'transparent',
        borderRadius: '0 0 8px 8px',
        minHeight: props.minHeight,
      }));

      /**
       * 处理卡片点击事件
       */
      const handleClick = () => {
        if (props.clickable) {
          emit('click');
        }
      };

      return {
        cardStyles,
        headerStyle,
        bodyStyle,
        handleClick,
      };
    },
  });
</script>

<template>
  <a-card
    class="base-data-card"
    :style="cardStyles"
    :bordered="false"
    :header-style="headerStyle"
    :body-style="bodyStyle"
    @click="handleClick"
  >
    <template #title>
      <div class="title-content">
        <IconCommon v-if="showIcon" class="card-icon" />
        <span class="title-text">{{ title }}</span>
      </div>
    </template>

    <div class="card-content">
      <slot />
    </div>
  </a-card>
</template>

<style scoped lang="less">
  .base-data-card {
    background: transparent;
    border: none;
    overflow: hidden;
    height: 100%;

    :deep(.arco-card-header) {
      border: none;
      background: transparent;
    }

    :deep(.arco-card-body) {
      background: transparent;
      position: relative;
      background: rgba(13, 45, 76, 0.15);
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 201, 242, 0.1);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(0, 201, 242, 0.05) 0%,
          rgba(0, 201, 242, 0.02) 50%,
          rgba(0, 201, 242, 0) 100%
        );
        border-radius: 8px;
        pointer-events: none;
      }
    }

    .title-content {
      padding: 0 10px;
      height: 100%;
      display: flex;
      align-items: center;
      position: relative;

      .card-icon {
        margin-right: 10px;
        font-size: 18px;
        color: #fff;
      }

      .title-text {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
      }
    }

    .card-content {
      height: 100%;
      position: relative;
      z-index: 1;
    }
  }
</style>
