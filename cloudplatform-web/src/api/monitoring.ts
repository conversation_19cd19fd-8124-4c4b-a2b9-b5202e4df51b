import axios, { AxiosResponse } from 'axios';
import { ApiResponse } from '@/types/api'; // 假设后端通用响应类型

export interface LocationMonitoringStatus {
  locationId: number;
  locationName: string;
  monitoringStatus: string;
  rtspUrls: string[];
}

/**
 * @description 获取所有点位的监控状态列表
 * @returns {Promise<ApiResponse<LocationMonitoringStatus[]>>}
 */
export async function getLocationMonitoringStatusList(): Promise<
  ApiResponse<LocationMonitoringStatus[]>
> {
  // axios.get返回的是AxiosResponse<T>，其中T是我们期望的data类型，即ApiResponse<LocationMonitoringStatus[]>
  // 拦截器中已经处理了剥离AxiosResponse并返回data的部分，或者直接返回AxiosResponse然后由调用处处理
  // 根据interceptor.ts的逻辑，它最终返回res (AxiosResponse<ApiResponse>) 或者 Promise.reject
  // 因此，调用处应该期望 AxiosResponse<ApiResponse<...>>
  const response: AxiosResponse<ApiResponse<LocationMonitoringStatus[]>> =
    await axios.get('/api/monitoring/location-status');
  return response.data; // 我们通常关心的是response.data中的内容
}
