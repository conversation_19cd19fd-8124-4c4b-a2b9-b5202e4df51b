import axios from 'axios';
import type { AxiosResponse } from 'axios';

// Define the type for the average environmental data based on backend DTO
export interface AverageEnvironmentalData {
  averageOxygenConcentration: number | null;
  oxygenUnit: string;
  averageHumidity: number | null;
  humidityUnit: string;
  averageTemperature: number | null;
  temperatureUnit: string;
}

export interface HourlyEnvironmentalData {
  hour: number;
  temperature: number | null;
  humidity: number | null;
}

export interface EnvironmentalTrendDTO {
  hourlyData: HourlyEnvironmentalData[];
}

export interface EnvironmentalAlarmCountDTO {
  alarmCount: number;
}

export interface ApiResponse<T> {
  statusCode: number;
  message: string;
  success: boolean;
  data: T;
}

/**
 * Get the latest average environmental data for all points.
 * @returns Promise resolving to ApiResponse containing AverageEnvironmentalData
 */
export function getAverageLatestEnvironmentalData(): Promise<
  AxiosResponse<ApiResponse<any>>
> {
  return axios.get('/api/environmental/average/latest');
}

// 获取当天每小时温湿度趋势
export function getTempHumidityTrend(): Promise<
  AxiosResponse<ApiResponse<EnvironmentalTrendDTO>>
> {
  return axios.get('/api/environmental/trend/hourly');
}

/**
 * Get the environmental alarm count for a specific time span and optional locations.
 * @param timeSpanType The time span type (e.g., "day", "week", "month", "year")
 * @param locationIds Optional array of location IDs
 * @returns Promise resolving to ApiResponse containing EnvironmentalAlarmCountDTO
 */
export function getEnvironmentalAlarmCount(
  timeSpanType: string,
  locationIds?: string[]
): Promise<AxiosResponse<ApiResponse<EnvironmentalAlarmCountDTO>>> {
  let url = `/api/environmental/alarm/count?timeSpanType=${timeSpanType}`;
  if (locationIds && locationIds.length > 0) {
    url += `&locationIds=${locationIds.join(',')}`;
  }
  return axios.get(url);
}
