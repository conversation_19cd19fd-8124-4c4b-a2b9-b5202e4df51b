import axios from 'axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Message, Modal } from '@arco-design/web-vue';
import { useUserStore } from '@/store';
import { getToken } from '@/utils/auth';
import { ApiResponse } from '@/types/api';
import { ErrorHandler } from '@/utils/errorHandler';

export interface HttpResponse<T = unknown> {
  statusCode: number;
  message: string;
  success: boolean;
  data: T;
}

if (import.meta.env.VITE_API_BASE_URL) {
  axios.defaults.baseURL = import.meta.env.VITE_API_BASE_URL;
}

axios.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 让每个请求都携带token
    // 这个例子使用JWT token
    // Authorization是一个自定义的请求头键
    // 请根据实际情况修改
    const token = getToken();
    if (token) {
      if (!config.headers) {
        config.headers = {};
      }
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    // 处理错误
    return Promise.reject(error);
  }
);
// 添加响应拦截器
axios.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const res = response;
    // 如果success为false，则判断为错误
    if (
      res.status !== 200 ||
      res.data.statusCode !== 200 ||
      res.data.success === false
    ) {
      Message.error({
        content: res.data.message || '错误',
        duration: 5 * 1000,
      });
      // 50008: 非法的token; 50012: 其他客户端登录; 50014: Token过期; (这些状态码可能需要根据后端实际返回调整)
      // 注意：这里的状态码判断逻辑可能需要根据后端的实际错误处理方式进行调整
      // 目前后端ApiResponse的success=false时，statusCode可能不是特定的错误码，而是其他HTTP状态码
      // 如果后端在success=false时会返回特定的code字段，可以继续使用res.code进行更精细的错误判断
      // 否则，仅根据success=false来判断业务错误
      // if (
      //   [50008, 50012, 50014].includes(res.statusCode) &&
      //   response.config.url !== '/api/user/info'
      // ) {
      //   Modal.error({
      //     title: '确认登出',
      //     content:
      //       '您已登出，您可以取消以停留在此页面，或重新登录',
      //     okText: '重新登录',
      //     async onOk() {
      //       const userStore = useUserStore();

      //       await userStore.logout();
      //       window.location.reload();
      //     },
      //   });
      // }
      return Promise.reject(new Error(res.data.message || '错误'));
    }
    return res;
  },
  (error) => {
    // 使用统一的错误处理
    ErrorHandler.handleApiError(error);
    return Promise.reject(error);
  }
);
