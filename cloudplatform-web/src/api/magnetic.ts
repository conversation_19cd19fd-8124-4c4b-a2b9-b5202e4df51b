import axios from 'axios';
import { ApiResponse } from '@/types/api';
import type { AxiosResponse } from 'axios';

/**
 * 表示按风险等级划分的磁性报警总数。
 */
export interface MagneticAlarmTotalStatsDTO {
  /** 安全报警的数量。 */
  safeCount: number;
  /** 低风险报警的数量。 */
  lowRiskCount: number;
  /** 中风险报警的数量。 */
  mediumRiskCount: number;
  /** 高风险报警的数量。 */
  highRiskCount: number;
}

/**
 * 表示磁性报警的简单总数。
 */
export interface MagneticAlarmCountDTO {
  /** 报警总数。 */
  alarmCount: number;
}

/**
 * 表示特定地点的报警计数。
 */
export interface LocationAlarmCountDTO {
  /** 此地点的报警总数。 */
  alarmCount: number;
  /** 地点的名称。 */
  locationName: string;
}

/**
 * 检索指定时间范围内的按风险等级划分的磁性报警总统计信息。
 * @param timeSpanType 时间范围类型（例如：“day”, “week”, “month”, “year”）。
 * @returns Promise 解析为包含 ApiResponse（包含 MagneticAlarmTotalStatsDTO）的 AxiosResponse。
 */
export function getMagneticAlarmStats(
  timeSpanType: string
): Promise<AxiosResponse<ApiResponse<MagneticAlarmTotalStatsDTO>>> {
  return axios.get('/api/magnetic/alarm/stats', {
    params: {
      timeSpanType,
    },
  });
}

/**
 * 获取指定时间范围和可选地点的磁性报警计数。
 * @param timeSpanType 时间范围类型（例如：“day”, “week”, “month”, “year”）
 * @param locationIds 可选的地点ID数组
 * @returns Promise 解析为包含 MagneticAlarmCountDTO 的 ApiResponse
 */
export function getMagneticAlarmCount(
  timeSpanType: string,
  locationIds?: string[]
): Promise<AxiosResponse<ApiResponse<MagneticAlarmCountDTO>>> {
  let url = `/api/magnetic/alarm/count?timeSpanType=${timeSpanType}`;
  if (locationIds && locationIds.length > 0) {
    url += `&locationIds=${locationIds.join(',')}`;
  }
  return axios.get(url);
}

/**
 * 获取指定时间范围内的每个地点的磁性、氧气和温湿度报警计数。
 * @param timeSpanType 时间范围类型（例如：“day”, “week”, “month”, “year”）
 * @returns Promise 解析为包含 LocationAlarmCountDTO 列表的 ApiResponse
 */
export function getLocationAlarmCounts(
  timeSpanType: string
): Promise<AxiosResponse<ApiResponse<LocationAlarmCountDTO[]>>> {
  return axios.get('/api/magnetic/alarm/countsByLocation', {
    params: {
      timeSpanType,
    },
  });
}
