<template>
  <div class="monitor-overview">
    <div class="monitor-grid" :class="`grid-${layout}`">
      <div
        v-for="camera in currentPageCameras"
        :key="camera.id"
        class="monitor-item"
      >
        <div class="monitor-title">{{ camera.name }}</div>
        <div class="monitor-player">
          <video-player :rtsp-url="camera.rtspUrl" />
        </div>
      </div>
    </div>

    <!-- 左侧翻页按钮 -->
    <div
      v-show="currentPage > 1"
      class="page-button page-button-left"
      @click="prevPage"
    >
      <a-button class="custom-page-button">
        <template #icon><icon-left /></template>
      </a-button>
    </div>

    <!-- 右侧翻页按钮 -->
    <div
      v-show="currentPage < totalPages"
      class="page-button page-button-right"
      @click="nextPage"
    >
      <a-button class="custom-page-button">
        <template #icon><icon-right /></template>
      </a-button>
    </div>

    <!-- 底部感应区域 -->
    <div class="layout-button-sensor"></div>

    <!-- 底部布局切换按钮 -->
    <div class="layout-button-container">
      <div class="custom-layout-buttons">
        <div
          class="custom-layout-button"
          :class="{ active: layout === 4 }"
          @click="layout = 4"
        >
          4画面
        </div>
        <div class="divider"></div>
        <div
          class="custom-layout-button"
          :class="{ active: layout === 9 }"
          @click="layout = 9"
        >
          9画面
        </div>
        <div class="divider"></div>
        <div
          class="custom-layout-button"
          :class="{ active: layout === 16 }"
          @click="layout = 16"
        >
          16画面
        </div>
      </div>
    </div>

    <!-- iOS 风格的分页指示点 -->
    <div class="page-dots-container">
      <div
        v-for="page in totalPages"
        :key="page"
        class="page-dot"
        :class="{ active: page === currentPage }"
      ></div>
    </div>

    <div v-if="loading" class="loading-overlay">
      <a-spin :size="40" />
    </div>

    <!-- 右下角跳转按钮 -->
    <div
      class="floating-button-container"
      @mouseenter="showFullButton = true"
      @mouseleave="showFullButton = false"
    >
      <div
        class="floating-button"
        :class="{ 'show-full': showFullButton }"
        @click="goToDataScreen"
      >
        <icon-fullscreen-exit size="24px" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, watch } from 'vue';
  import VideoPlayer from '@/components/VideoPlayer.vue';
  import {
    IconLeft,
    IconRight,
    IconArrowLeft,
  } from '@arco-design/web-vue/es/icon';
  import { useRouter } from 'vue-router';

  // 模拟摄像头数据
  // const mockCameras = [
  //   { id: 1, name: '摄像头 1', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/101' },
  //   { id: 2, name: '摄像头 2', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/201' },
  //   { id: 3, name: '摄像头 3', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/301' },
  //   { id: 4, name: '摄像头 4', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/101' },
  //   { id: 5, name: '摄像头 5', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/201' },
  //   { id: 6, name: '摄像头 6', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/301' },
  //   { id: 7, name: '摄像头 7', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/101' },
  //   { id: 8, name: '摄像头 8', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/201' },
  //   { id: 9, name: '摄像头 9', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/301' },
  //   { id: 10, name: '摄像头 10', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/101' },
  //   { id: 11, name: '摄像头 11', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/201' },
  //   { id: 12, name: '摄像头 12', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/301' },
  //   { id: 13, name: '摄像头 13', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/101' },
  //   { id: 14, name: '摄像头 14', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/201' },
  //   { id: 15, name: '摄像头 15', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/301' },
  //   { id: 16, name: '摄像头 16', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/101' },
  //   { id: 17, name: '摄像头 17', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/201' },
  //   { id: 18, name: '摄像头 18', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/301' },
  //   { id: 19, name: '摄像头 19', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/101' },
  //   { id: 20, name: '摄像头 20', rtspUrl: 'rtsp://admin:yunci123@**************/Streaming/Channels/201' },
  // ];

  // 使用循环生成摄像头数据
  const generateMockCameras = (count: number) => {
    const cameras = [];
    for (let i = 1; i <= count; i++) {
      cameras.push({
        id: i,
        name: `摄像头 ${i}`,
        rtspUrl: `rtsp://admin:yunci123@**************/Streaming/Channels/101`, // 初始值，会被随机化
      });
    }
    return cameras;
  };

  // 状态变量
  const layout = ref<number>(9); // 默认9画面
  const currentPage = ref<number>(1);
  const loading = ref<boolean>(false);
  const cameras = ref<any[]>([]);
  const showFullButton = ref<boolean>(false);
  const router = useRouter();

  // 随机分配摄像头地址
  const randomizeCameras = () => {
    const channels = ['101', '201', '301'];
    const mockCameras = generateMockCameras(100); // 生成100个摄像头
    const randomizedCameras = mockCameras.map((camera) => {
      const randomChannel =
        channels[Math.floor(Math.random() * channels.length)];
      return {
        ...camera,
        rtspUrl: `rtsp://admin:yunci123@**************/Streaming/Channels/${randomChannel}`,
      };
    });
    cameras.value = randomizedCameras;
  };

  // 计算总项目数
  const totalItems = computed(() => cameras.value.length);

  // 计算总页数
  const totalPages = computed(() => Math.ceil(totalItems.value / layout.value));

  // 计算当前页显示的摄像头
  const currentPageCameras = computed(() => {
    const startIndex = (currentPage.value - 1) * layout.value;
    return cameras.value.slice(startIndex, startIndex + layout.value);
  });

  // 上一页
  const prevPage = () => {
    if (currentPage.value > 1) {
      currentPage.value--;
    }
  };

  // 下一页
  const nextPage = () => {
    if (currentPage.value < totalPages.value) {
      currentPage.value++;
    }
  };

  // 监听布局变化，重置页码
  watch(layout, (newLayout) => {
    console.log('布局已更改为:', newLayout);
    // 计算新的总页数
    const newTotalPages = Math.ceil(totalItems.value / newLayout);

    // 如果当前页码超出新的总页数，则重置为最后一页
    if (currentPage.value > newTotalPages) {
      currentPage.value = newTotalPages || 1;
    }
  });

  // 组件挂载时加载数据
  onMounted(() => {
    // 随机分配摄像头地址
    randomizeCameras();
  });

  // 模拟从API获取摄像头列表
  const fetchCameras = async () => {
    loading.value = true;
    try {
      // 模拟API请求
      // const response = await api.getCameras();
      // cameras.value = response.data;

      // 使用随机分配的摄像头数据
      randomizeCameras();
    } catch (error) {
      console.error('获取摄像头列表失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 跳转到大屏页面
  const goToDataScreen = () => {
    router.push('/dataScreen');
  };
</script>

<style scoped>
  .monitor-overview {
    padding: 0;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(13, 45, 76, 0.9);
    overflow: hidden;
  }

  .page-info {
    font-size: 1.5vh;
    color: #666;
  }

  .monitor-grid {
    display: grid;
    gap: 1vh;
    height: 100%;
    width: 100%;
    padding: 1vh;
    box-sizing: border-box;
    overflow: hidden;
  }

  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
  }

  .grid-9 {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
  }

  .grid-16 {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(4, 1fr);
  }

  .monitor-item {
    border: 0.1vh solid #333;
    border-radius: 0.4vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background-color: #000;
    height: 100%;
    width: 100%;
  }

  .monitor-title {
    padding: 0.8vh;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 1.5vh;
    text-align: center;
  }

  .monitor-player {
    flex: 1;
    position: relative;
    overflow: hidden;
    height: calc(100% - 3vh);
  }

  /* 翻页按钮样式 */
  .page-button {
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;
    cursor: pointer;
    opacity: 0.5;
    transition: opacity 0.3s;
  }

  .page-button:hover {
    opacity: 0.8;
  }

  .page-button-left {
    left: 2vw;
  }

  .page-button-right {
    right: 2vw;
  }

  .custom-page-button {
    width: 6vh !important;
    height: 6vh !important;
    border-radius: 50% !important;
    background-color: rgba(255, 255, 255, 0.5) !important;
    border: none !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    box-shadow: 0 0.2vh 1vh rgba(0, 0, 0, 0.2) !important;
  }

  .custom-page-button :deep(.arco-icon) {
    font-size: 2.5vh !important;
    color: #333 !important;
  }

  /* 底部布局按钮样式 */
  .layout-button-container {
    position: fixed;
    bottom: -3vh; /* 默认隐藏在底部以下 */
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 3vh;
    box-shadow: 0 0.2vh 1vh rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transition: bottom 0.3s ease-in-out; /* 添加过渡效果 */
  }

  /* 创建一个感应区域 */
  .layout-button-sensor {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 14vh;
    z-index: 99;
  }

  /* 当鼠标移入感应区域时显示布局按钮 */
  .layout-button-sensor:hover + .layout-button-container,
  .layout-button-container:hover {
    bottom: 5vh;
  }

  .custom-layout-buttons {
    display: flex;
    align-items: center;
  }

  .custom-layout-button {
    padding: 1vh 2vw;
    color: #333;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 1.5vh;
    text-align: center;
    min-width: 8vw;
  }

  .custom-layout-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .custom-layout-button.active {
    background-color: rgba(22, 93, 255, 0.2);
    color: #165dff;
  }

  .divider {
    width: 0.1vw;
    height: 2vh;
    background-color: rgba(0, 0, 0, 0.2);
  }

  /* iOS 风格的分页指示点 */
  .page-dots-container {
    position: fixed;
    bottom: 2vh;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.8vh;
    z-index: 100;
  }

  .page-dot {
    width: 0.8vh;
    height: 0.8vh;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    transition: all 0.3s;
    opacity: 0.5;
    box-shadow: 0 0.1vh 0.3vh rgba(0, 0, 0, 0.2);
  }

  .page-dot:hover {
    opacity: 0.8;
  }

  .page-dot.active {
    width: 2vh;
    border-radius: 0.4vh;
    background-color: rgba(255, 255, 255, 0.5);
    opacity: 0.8;
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  /* 浮动按钮样式 */
  .floating-button-container {
    position: fixed;
    bottom: 0;
    right: 0;
    z-index: 100;
    overflow: hidden;
    width: 14vw;
    height: 14vh;
    transition: all 0.3s ease;
  }

  .floating-button {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 6vh;
    height: 6vh;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 0.2vh 1vh rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    transform: translate(3vh, 3vh);
  }

  .floating-button.show-full {
    transform: translate(-3vh, -3vh);
  }

  .floating-button:hover {
    background-color: rgba(255, 255, 255, 0.7);
  }

  .floating-button :deep(.arco-icon) {
    color: #333;
    font-size: 2.5vh;
  }
</style>
