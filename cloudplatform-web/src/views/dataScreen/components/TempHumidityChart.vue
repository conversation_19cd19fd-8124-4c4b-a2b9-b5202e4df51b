<script lang="ts">
  import { defineComponent } from 'vue';
  import { Card } from '@arco-design/web-vue';
  import { IconCommon } from '@arco-design/web-vue/es/icon';
  import EnvironmentTrendChart from './common/EnvironmentTrendChart.vue';
  import cardTitleBg from '@/assets/images/dataScreen/卡片组件/标题渐变背景.png';

  interface TempHumidityData {
    times: string[];
    temperatures: number[];
    humidities: number[];
    oxygenConcentrations: number[];
  }

  export default defineComponent({
    name: 'TempHumidityChart',
    components: {
      ACard: Card,
      IconCommon,
      EnvironmentTrendChart,
    },
    props: {
      /**
       * 温湿度数据
       */
      tempHumidityData: {
        type: Object as () => TempHumidityData,
        required: true,
        default: () => ({
          times: [],
          temperatures: [],
          humidities: [],
          oxygenConcentrations: [],
        }),
      },
      /**
       * 屏幕适配函数
       */
      adaptSize: {
        type: Function,
        required: true,
      },
    },
    setup(props) {
      return {
        cardTitleBg,
      };
    },
  });
</script>

<template>
  <a-card
    class="data-card temp-humidity-card"
    :bordered="false"
    :header-style="{
      border: 'none',
      padding: 0,
      height: 'auto',
      minHeight: '40px',
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      backgroundImage: `url(${cardTitleBg})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      borderRadius: '0 0 0 0',
    }"
    :body-style="{
      padding: 0,
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      borderRadius: '0 0 10px 10px',
      minHeight: '190px',
    }"
  >
    <template #title>
      <div class="title-content">
        <icon-common class="card-icon" />
        <span class="title-text">24小时环境变化趋势</span>
      </div>
    </template>
    <div class="card-content">
      <EnvironmentTrendChart
        :data="tempHumidityData"
        :adapt-size="adaptSize"
        height="190px"
      />
    </div>
  </a-card>
</template>

<style scoped lang="less">
  .temp-humidity-card {
    background: transparent;
    border: none;
    overflow: hidden;

    :deep(.arco-card-header) {
      border: none;
      background: transparent;
    }

    :deep(.arco-card-body) {
      position: relative;
      background: rgba(13, 45, 76, 0.15);
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 201, 242, 0.1);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(0, 201, 242, 0.05) 0%,
          rgba(0, 201, 242, 0.02) 50%,
          rgba(0, 201, 242, 0) 100%
        );
        border-radius: 8px;
        pointer-events: none;
      }
    }

    .title-content {
      padding: 8px 15px;
      min-height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      flex-wrap: nowrap;
      white-space: nowrap;

      .card-icon {
        margin-right: 10px;
        font-size: 18px;
        color: #fff;
      }

      .title-text {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }
    }

    .card-content {
      padding: 20px;
    }
  }
</style>
