<script lang="ts">
  import { defineComponent } from 'vue';
  import { Card } from '@arco-design/web-vue';
  import cardTitleBg from '@/assets/images/dataScreen/卡片组件/标题渐变背景.png';

  export default defineComponent({
    name: 'AlarmStatistics',
    components: {
      ACard: Card,
    },
    props: {
      /**
       * 监控数据对象
       */
      monitoringData: {
        type: Object,
        required: true,
        default: () => ({
          cameraCount: 0,
          recordCount: 0,
        }),
      },
    },
    setup() {
      return {
        cardTitleBg,
      };
    },
  });
</script>

<template>
  <a-card
    class="data-card alarm-statistics-card"
    :bordered="false"
    :header-style="{
      border: 'none',
      padding: 0,
      height: 'auto',
      minHeight: '40px',
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      backgroundImage: `url(${cardTitleBg})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      borderRadius: '0 0 0 0',
    }"
    :body-style="{
      padding: 0,
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      borderRadius: '0 0 10px 10px',
      minHeight: '80px',
    }"
  >
    <template #title>
      <div class="title-content">
        <span class="title-text">报警统计</span>
      </div>
    </template>
    <div class="alarm-statistics">
      <div class="alarm-item">
        <div class="icon-box">
          <img
            src="@/assets/images/dataScreen/报警组件/背景.png"
            class="icon-bg"
          />
          <img
            src="@/assets/images/dataScreen/报警组件/盾徽.png"
            class="icon-front"
          />
        </div>
        <div class="alarm-content">
          <div class="label">铁磁系统报警</div>
          <div class="value">
            {{ monitoringData.cameraCount }}<span class="unit">次</span>
          </div>
        </div>
      </div>
      <div class="alarm-item">
        <div class="icon-box">
          <img
            src="@/assets/images/dataScreen/报警组件/背景.png"
            class="icon-bg"
          />
          <img
            src="@/assets/images/dataScreen/报警组件/身份.png"
            class="icon-front"
          />
        </div>
        <div class="alarm-content">
          <div class="label">传感器报警</div>
          <div class="value">
            {{ monitoringData.recordCount }}<span class="unit">次</span>
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<style scoped lang="less">
  .alarm-statistics-card {
    background: transparent;
    border: none;
    overflow: hidden;

    :deep(.arco-card-header) {
      border: none;
      background: transparent;
    }

    :deep(.arco-card-body) {
      position: relative;
      background: rgba(13, 45, 76, 0.15);
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 201, 242, 0.1);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(0, 201, 242, 0.05) 0%,
          rgba(0, 201, 242, 0.02) 50%,
          rgba(0, 201, 242, 0) 100%
        );
        border-radius: 8px;
        pointer-events: none;
      }
    }

    .title-content {
      padding: 8px 15px;
      min-height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      flex-wrap: nowrap;
      white-space: nowrap;

      .card-icon {
        margin-right: 10px;
        font-size: 18px;
        color: #fff;
      }

      .title-text {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }
    }
  }

  .alarm-statistics {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 20px;
    height: 100%;
    gap: 40px;

    .alarm-item {
      display: flex;
      align-items: center;
      gap: 12px;

      .icon-box {
        position: relative;
        width: 60px;
        height: 60px;

        .icon-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }

        .icon-front {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 46px;
          height: 46px;
          filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
            drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
            drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));
        }
      }

      .alarm-content {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .label {
          color: #ffffff;
          font-size: 14px;
          margin-bottom: 4px;
          filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
            drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
            drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));
        }

        .value {
          color: #ffffff;
          font-size: 24px;
          font-weight: 500;
          filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
            drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
            drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));

          .unit {
            font-size: 14px;
            margin-left: 4px;
            opacity: 0.8;
          }
        }
      }
    }
  }
</style>
