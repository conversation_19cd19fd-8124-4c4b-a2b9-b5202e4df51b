<script lang="ts">
  import { defineComponent } from 'vue';
  import { Card } from '@arco-design/web-vue';
  import {
    IconCommon,
    IconClose,
    IconLink,
  } from '@arco-design/web-vue/es/icon';
  import VideoPlayer from '@/components/VideoPlayer.vue';
  import EnvironmentTrendChart from './common/EnvironmentTrendChart.vue';
    import cardTitleBg from '@/assets/images/dataScreen/卡片组件/标题渐变背景.png';

  interface SelectedCamera {
    title: string;
    rtspUrls: string[];
    currentUrlIndex?: number;
    locationId?: number;
  }

  export default defineComponent({
    name: 'VideoMonitorPanel',
    components: {
      ACard: Card,
      IconCommon,
      IconClose,
      IconLink,
      VideoPlayer,
      EnvironmentTrendChart,
    },
    props: {
      /**
       * 是否显示监控卡片
       */
      showMonitoringCard: {
        type: Boolean,
        required: true,
        default: false,
      },
      /**
       * 选中的摄像头信息
       */
      selectedCamera: {
        type: Object as () => SelectedCamera | null,
        default: null,
      },
    },
    emits: ['close', 'jump-to-external'],
    setup(props, { emit }) {
      /**
       * 处理关闭事件
       */
      const handleClose = () => {
        emit('close');
      };

      /**
       * 处理跳转到外部系统事件
       */
      const handleJumpToExternalSystem = () => {
        emit('jump-to-external');
      };

      /**
       * 图表数据 - 环境变化趋势
       */
      const environmentData = {
        times: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
        temperatures: [22, 21, 23, 25, 26, 24, 22],
        humidities: [65, 68, 70, 72, 69, 67, 66],
        oxygenConcentrations: [20.9, 20.8, 20.7, 20.8, 20.9, 20.8, 20.9],
      };

      /**
       * 铁磁报警风险等级数据 - 假数据用于测试
       */
      const ferromagneticRiskData = {
        lowRiskCount: 12,
        mediumRiskCount: 8,
        highRiskCount: 3,
      };

      /**
       * 屏幕适配函数
       */
      const adaptSize = (size: number) => {
        const baseSize = 1920;
        const scale = window.innerWidth / baseSize;
        return Math.floor(size * scale);
      };

      return {
        cardTitleBg,
        handleClose,
        handleJumpToExternalSystem,
        environmentData,
        ferromagneticRiskData,
        adaptSize,
      };
    },
  });
</script>

<template>
  <a-card
    v-if="showMonitoringCard"
    class="data-card video-monitor-panel"
    :bordered="false"
    :header-style="{
      border: 'none',
      padding: 0,
      height: 'auto',
      minHeight: '40px',
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      backgroundImage: `url(${cardTitleBg})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      borderRadius: '0 0 0 0',
    }"
    :body-style="{
      padding: 0,
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      borderRadius: '0 0 10px 10px',
      minHeight: '760px',
    }"
  >
    <template #title>
      <div class="title-content">
        <icon-common class="card-icon" />
        <span class="title-text">科室详情</span>
        <div
          class="jump-btn"
          @click="handleJumpToExternalSystem"
          title="跳转到外部系统"
        >
          <icon-link size="20px" />
        </div>
        <div class="close-btn" @click="handleClose">
          <icon-close size="24px" />
        </div>
      </div>
    </template>
    <div class="card-content">
      <div v-if="selectedCamera" class="video-section">
        <div class="department-info">
          <div class="department-label">当前科室</div>
          <div class="department-name">{{ selectedCamera.title }}</div>
        </div>
        <div class="main-video-container">
          <VideoPlayer
            :rtsp-url="
              selectedCamera.rtspUrls[selectedCamera.currentUrlIndex || 0]
            "
          />
        </div>
      </div>
      <div v-else class="no-camera-selected">
        请选择一个摄像头查看监控画面
      </div>

      <div class="charts-container">
        <div class="chart-item">
          <div class="chart-title">报警综合分析</div>
          <div class="chart-placeholder">
            <!-- 报警分析图表占位符 -->
          </div>
        </div>
        <div class="chart-item">
          <div class="chart-title">24小时环境变化趋势</div>
          <EnvironmentTrendChart
            :data="environmentData"
            :adapt-size="adaptSize"
            height="170px"
            :enable-auto-scroll="false"
          />
        </div>
      </div>
    </div>
  </a-card>
</template>

<style scoped lang="less">
  .video-monitor-panel {
    background: transparent;
    border: none;
    overflow: hidden;

    :deep(.arco-card-header) {
      border: none;
      background: transparent;
    }

    :deep(.arco-card-body) {
      position: relative;
      background: rgba(13, 45, 76, 0.15);
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 201, 242, 0.1);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(0, 201, 242, 0.05) 0%,
          rgba(0, 201, 242, 0.02) 50%,
          rgba(0, 201, 242, 0) 100%
        );
        border-radius: 8px;
        pointer-events: none;
      }
    }

    .title-content {
      padding: 8px 15px;
      min-height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      flex-wrap: nowrap;
      white-space: nowrap;

      .card-icon {
        margin-right: 10px;
        font-size: 18px;
        color: #fff;
      }

      .title-text {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }

      .jump-btn {
        position: absolute;
        right: 45px;
        top: 50%;
        transform: translateY(-50%);
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #fff;
        opacity: 0.8;
        transition: all 0.3s;

        &:hover {
          opacity: 1;
          transform: translateY(-50%) scale(1.1);
          color: #00d4ff;
        }

        :deep(.arco-icon) {
          font-size: 16px;
        }
      }

      .close-btn {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #fff;
        opacity: 0.8;
        transition: all 0.3s;

        &:hover {
          opacity: 1;
          transform: translateY(-50%) scale(1.1);
        }

        :deep(.arco-icon) {
          font-size: 18px;
        }
      }
    }

    .card-content {
      padding: 16px;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 16px;

      .video-section {
        height: 65%;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .department-info {
          background: linear-gradient(135deg, rgba(0, 201, 242, 0.1) 0%, rgba(0, 201, 242, 0.05) 100%);
          border: 1px solid rgba(0, 201, 242, 0.3);
          border-radius: 8px;
          padding: 12px 20px;
          display: flex;
          align-items: center;
          gap: 16px;
          backdrop-filter: blur(10px);
          box-shadow: 0 4px 20px rgba(0, 201, 242, 0.1);

          .department-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
          }

          .department-name {
            color: #00d4ff;
            font-size: 20px;
            font-weight: 600;
            letter-spacing: 1px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            flex: 1;
          }
        }

        .main-video-container {
          flex: 1;
          width: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 4px;
          overflow: hidden;
        }
      }

      .no-camera-selected {
        height: 65%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 16px;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 4px;
      }

      .charts-container {
        height: 30%;
        display: flex;
        gap: 16px;

        .chart-item {
          flex: 1;
          background-color: rgba(0, 0, 0, 0.3);
          border-radius: 8px;
          overflow: hidden;
          padding: 16px;
          border: 1px solid rgba(0, 201, 242, 0.2);
          
          .chart-title {
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            text-align: center;
            text-shadow: 0 0 10px rgba(0, 201, 242, 0.5);
          }
          
          .chart-placeholder {
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
            border: 1px dashed rgba(0, 201, 242, 0.3);
            border-radius: 4px;
          }
        }
      }
    }
  }
</style>
