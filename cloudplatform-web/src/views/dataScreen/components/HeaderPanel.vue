<script lang="ts">
  import { defineComponent } from 'vue';
  import { IconSunFill } from '@arco-design/web-vue/es/icon';
  import headerImage1 from '@/assets/images/dataScreen/标题组件/图层1.png';
  import headerImage2 from '@/assets/images/dataScreen/标题组件/图层2.png';

  export default defineComponent({
    name: 'HeaderPanel',
    components: {
      IconSunFill,
    },
    props: {
      /**
       * 天气信息对象
       */
      weather: {
        type: Object,
        required: true,
        default: () => ({
          type: '晴',
          temperature: '17~28℃',
        }),
      },
      /**
       * 服务器时间
       */
      serverTime: {
        type: [Date, String],
        required: true,
      },
      /**
       * 页面标题
       */
      title: {
        type: String,
        default: '云磁安全云平台',
      },
      /**
       * Logo 图片路径
       */
      logo: {
        type: String,
        default: '',
      },
    },
    setup(props) {
      /**
       * 时间格式化函数 格式为 YYYY:MM:DD HH:mm:ss
       */
      const formatDate = (date: Date | string): string => {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = ('0' + (d.getMonth() + 1)).slice(-2);
        const day = ('0' + d.getDate()).slice(-2);
        const hour = ('0' + d.getHours()).slice(-2);
        const minute = ('0' + d.getMinutes()).slice(-2);
        const second = ('0' + d.getSeconds()).slice(-2);
        return `${year}年${month}月${day}日 ${hour}时${minute}分${second}秒`;
      };

      return {
        formatDate,
        headerImage1,
        headerImage2,
      };
    },
  });
</script>

<template>
  <header class="header">
    <div
      class="header-bg header-bg-1"
      :style="{ backgroundImage: `url(${headerImage1})` }"
    ></div>
    <div
      class="header-bg header-bg-2"
      :style="{ backgroundImage: `url(${headerImage2})` }"
    ></div>
    <div class="header-content">
      <div v-if="logo" class="logo-container">
        <img :src="logo" alt="湘雅二医院" class="hospital-logo" />
      </div>
      <div v-else class="weather-info">
        <icon-sun-fill :spin="true" size="36px" />
        <div>{{ weather.type }}</div>
        <div>{{ weather.temperature }}</div>
      </div>
      <div class="title">{{ title }}</div>
      <div class="time">{{ serverTime ? formatDate(serverTime) : '' }}</div>
    </div>
  </header>
</template>

<style scoped lang="less">
  .header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 110px;
    z-index: 100;
    overflow: hidden;

    .header-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-position: center;
    }

    .header-bg-1 {
      z-index: 3;
      filter: contrast(117%) saturate(111%);
    }

    .header-bg-2 {
      z-index: 4;
      filter: contrast(105%) saturate(105%) brightness(109%);
    }

    .header-content {
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 40px;
      position: relative;
      z-index: 5;
    }

    .weather-info {
      font-size: 28px;
      color: #ade9ff;
      display: flex;
      align-items: center;
      gap: 24px;
      margin-top: 20px;
      margin-left: 50px;
      position: relative;
      z-index: 1;
      filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
        drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
        drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));
      font-weight: 500;

      :deep(.arco-icon-spin) {
        animation: weather-icon-spin 8s infinite linear;
      }
    }

    @keyframes weather-icon-spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .title {
      font-size: 40px;
      font-weight: bold;
      background: linear-gradient(180deg, #fff 0%, #bcedff 100%);
      -webkit-background-clip: text;
      color: transparent;
      font-family: 'Bank Gothic', 'Orbitron', 'Rajdhani', 'Quantico',
        'Audiowide', 'Oxanium', 'Transparent', system-ui;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: 45%;
      transform: translate(-50%, -50%);
      margin: 0;
      z-index: 2;
      font-weight: 600;
      letter-spacing: 4px;
      filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
    }

    .time {
      font-size: 26px;
      color: #ffffff;
      position: relative;
      z-index: 1;
      margin-top: 20px;
      margin-right: 50px;
      filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
        drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
        drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));
      font-weight: 500;
    }

    .logo-container {
      display: flex;
      align-items: center;
      margin-left: 50px;
      margin-top: 20px;

      .hospital-logo {
        height: 50px;
        width: auto;
        filter: brightness(1.1) contrast(1.1);
      }
    }
  }
</style>
