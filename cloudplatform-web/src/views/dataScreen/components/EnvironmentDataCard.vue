<script lang="ts">
  import { defineComponent } from 'vue';
  import { Card } from '@arco-design/web-vue';
  import { IconCommon } from '@arco-design/web-vue/es/icon';
  import cardTitleBg from '@/assets/images/dataScreen/卡片组件/标题渐变背景.png';

  export default defineComponent({
    name: 'EnvironmentDataCard',
    components: {
      ACard: Card,
      IconCommon,
    },
    props: {
      /**
       * 环境数据对象
       */
      environmentData: {
        type: Object,
        required: true,
        default: () => ({
          airQuality: 0,
          temperature: 0,
          humidity: 0,
        }),
      },
      /**
       * 点击事件处理函数
       */
      onCardClick: {
        type: Function,
        default: null,
      },
    },
    setup(props) {
      /**
       * 处理卡片点击事件
       */
      const handleCardClick = () => {
        if (props.onCardClick) {
          props.onCardClick('environmentData');
        }
      };

      return {
        cardTitleBg,
        handleCardClick,
      };
    },
  });
</script>

<template>
  <a-card
    class="data-card environment-card"
    :bordered="false"
    :header-style="{
      border: 'none',
      padding: 0,
      height: 'auto',
      minHeight: '40px',
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      backgroundImage: `url(${cardTitleBg})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      borderRadius: '0 0 0 0',
    }"
    :body-style="{
      padding: 0,
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      borderRadius: '0 0 10px 10px',
      minHeight: '160px',
    }"
    @click="handleCardClick"
  >
    <template #title>
      <div class="title-content">
        <icon-common class="card-icon" />
        <span class="title-text">科室数据总览</span>
      </div>
    </template>
    <a-row class="environment-data">
      <a-col :span="10" class="icon-container">
        <div class="oxygen-component">
          <img
            src="@/assets/images/dataScreen/氧气组件/底座.png"
            alt="base"
            class="base"
          />
          <img
            src="@/assets/images/dataScreen/氧气组件/icon.png"
            alt="icon"
            class="icon"
          />
          <img
            src="@/assets/images/dataScreen/氧气组件/光柱.png"
            alt="light"
            class="light"
          />
          <img
            src="@/assets/images/dataScreen/氧气组件/光环.png"
            alt="ring"
            class="ring"
          />
          <img
            src="@/assets/images/dataScreen/氧气组件/倒影.png"
            alt="reflection"
            class="reflection"
          />
          <video class="particle-effect" autoplay loop muted playsinline>
            <source
              src="@/assets/images/dataScreen/氧气组件/粒子特效.webm"
              type="video/webm"
            />
          </video>
        </div>
      </a-col>
      <a-col :span="14" class="data-container">
        <div class="data-row">
          <div class="data-item oxygen">
            <span class="label">氧气浓度</span>
            <span class="value"
              >{{ environmentData.airQuality }}<span class="unit">%</span></span
            >
          </div>
        </div>
        <div class="data-row two-columns">
          <div class="data-item">
            <span class="label">环境温度</span>
            <span class="value"
              >{{ environmentData.temperature
              }}<span class="unit">℃</span></span
            >
          </div>
          <div class="data-item">
            <span class="label">环境湿度</span>
            <span class="value"
              >{{ environmentData.humidity }}<span class="unit">%RH</span></span
            >
          </div>
        </div>
      </a-col>
    </a-row>
  </a-card>
</template>

<style scoped lang="less">
  .environment-card {
    background: transparent;
    border: none;
    overflow: hidden;
    cursor: default;

    :deep(.arco-card-header) {
      border: none;
      background: transparent;
    }

    :deep(.arco-card-body) {
      position: relative;
      background: rgba(13, 45, 76, 0.15);
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 201, 242, 0.1);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(0, 201, 242, 0.05) 0%,
          rgba(0, 201, 242, 0.02) 50%,
          rgba(0, 201, 242, 0) 100%
        );
        border-radius: 8px;
        pointer-events: none;
      }
    }

    .title-content {
      padding: 8px 15px;
      min-height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      flex-wrap: nowrap;
      white-space: nowrap;

      .card-icon {
        margin-right: 10px;
        font-size: 18px;
        color: #fff;
      }

      .title-text {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }
    }
  }

  .environment-data {
    height: 100%;
    min-height: 160px;
    display: flex;
    align-items: center;

    .icon-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      padding-left: 20px;

      .oxygen-component {
        position: relative;
        width: 100px;
        height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
        transform: translateY(20%);

        img,
        video {
          position: absolute;
          width: 100%;
          height: 100%;
          object-fit: contain;
        }

        .particle-effect {
          z-index: 3;
          opacity: 0.8;
          mix-blend-mode: screen;
          pointer-events: none;
          bottom: 40%;
        }

        .base {
          z-index: 1;
          filter: hue-rotate(350deg);
        }

        .icon {
          z-index: 2;
          width: 80%;
          height: 80%;
          right: 25%;
          top: 15%;
          filter: saturate(144%) contrast(150%);
        }

        .light {
          z-index: 3;
          bottom: 10%;
          opacity: 0.7;
          filter: hue-rotate(350deg);
        }

        .ring {
          z-index: 4;
          animation: ringRise 3s infinite linear;
          transform-origin: center bottom;
          filter: brightness(120%) hue-rotate(350deg);
        }

        .reflection {
          z-index: 3;
          bottom: -10%;
          opacity: 0.3;
          transform: scaleY(-1);
          filter: brightness(120%) hue-rotate(350deg) blur(2.5px);
        }
      }
    }

    .data-container {
      padding: 10px 20px;

      .data-row {
        margin-bottom: 15px;
        width: 100%;
        display: flex;
        justify-content: flex-end;
        position: relative;
        padding: 5px;
        border-radius: 0px;

        // 氧气浓度组的渐变背景
        &:first-child {
          background: linear-gradient(
            to left,
            rgba(138, 222, 255, 0.15),
            transparent
          );
        }

        // 环境温湿度组的渐变背景
        &.two-columns {
          display: flex;
          justify-content: space-between;
          gap: 20px;
          background: linear-gradient(
            to left,
            rgba(138, 222, 255, 0.15),
            transparent
          );

          .data-item {
            flex: 1;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      .data-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        width: calc(50% - 10px);

        &.oxygen {
          .value {
            font-size: 24px;
          }
        }

        .label {
          color: #ffffff;
          font-size: 14px;
          margin-bottom: 4px;
          filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
            drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
            drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));
        }

        .value {
          color: #ffffff;
          font-size: 24px;
          font-weight: 500;
          filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
            drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
            drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));

          .unit {
            font-size: 14px;
            margin-left: 4px;
            opacity: 0.8;
          }
        }
      }
    }
  }

  @keyframes ringRise {
    0% {
      transform: translateY(0px);
      opacity: 0;
    }

    20% {
      opacity: 1;
    }

    80% {
      opacity: 1;
    }

    100% {
      transform: translateY(-50px);
      opacity: 0;
    }
  }
</style>
