<script lang="ts">
  import { defineComponent, ref, computed, onMounted, onUnmounted } from 'vue';
  import { Card } from '@arco-design/web-vue';
  import cardTitleBg from '@/assets/images/dataScreen/卡片组件/标题渐变背景.png';

  interface DepartmentStatus {
    id: string;
    name: string;
    status: '正常' | '警告' | '离线';
    oxygenConcentration: number;
    temperature: number;
    humidity: number;
    // 报警数量分类
    sensorAlarmCount?: number; // 传感器报警次数
    // 趋势数据 - 最近7天的历史数据
    trends: {
      oxygenHistory: number[];
      temperatureHistory: number[];
      humidityHistory: number[];
    };
  }

  export default defineComponent({
    name: 'DepartmentCarousel',
    components: {
      ACard: Card,
    },
    setup() {
      // 生成趋势数据的辅助函数
      const generateTrendData = (baseValue: number, variance = 2) => {
        return Array.from({ length: 7 }, () =>
          Math.max(0, baseValue + (Math.random() - 0.5) * variance * 2)
        );
      };

      // 模拟科室数据
      const departments = ref<DepartmentStatus[]>([
        {
          id: 'MR-01',
          name: 'MR-01',
          status: '正常',
          oxygenConcentration: 20.8,
          temperature: 22,
          humidity: 45,
          sensorAlarmCount: 0,
          trends: {
            oxygenHistory: generateTrendData(20.8, 0.5),
            temperatureHistory: generateTrendData(22, 1),
            humidityHistory: generateTrendData(45, 3),
          },
        },
        {
          id: 'MR-02',
          name: 'MR-02',
          status: '警告',
          oxygenConcentration: 18.5, // 过低
          temperature: 21,
          humidity: 43,
          sensorAlarmCount: 0,
          trends: {
            oxygenHistory: generateTrendData(18.5, 0.8),
            temperatureHistory: generateTrendData(21, 1.2),
            humidityHistory: generateTrendData(43, 4),
          },
        },
        {
          id: 'MR-03',
          name: 'MR-03',
          status: '警告',
          oxygenConcentration: 24.2, // 过高
          temperature: 26, // 过高
          humidity: 65, // 过高
          sensorAlarmCount: 3,
          trends: {
            oxygenHistory: generateTrendData(24.2, 0.6),
            temperatureHistory: generateTrendData(26, 1.5),
            humidityHistory: generateTrendData(65, 5),
          },
        },
        {
          id: 'MR-04',
          name: 'MR-04',
          status: '警告',
          oxygenConcentration: 20.7,
          temperature: 17, // 过低
          humidity: 35, // 过低
          sensorAlarmCount: 2,
          trends: {
            oxygenHistory: generateTrendData(20.7, 0.4),
            temperatureHistory: generateTrendData(17, 1.8),
            humidityHistory: generateTrendData(35, 3),
          },
        },
        {
          id: 'MR-05',
          name: 'MR-05',
          status: '正常',
          oxygenConcentration: 20.9,
          temperature: 23,
          humidity: 46,
          sensorAlarmCount: 0,
          trends: {
            oxygenHistory: generateTrendData(20.9, 0.3),
            temperatureHistory: generateTrendData(23, 0.8),
            humidityHistory: generateTrendData(46, 2),
          },
        },
        {
          id: 'MR-06',
          name: 'MR-06',
          status: '离线',
          oxygenConcentration: 0,
          temperature: 0,
          humidity: 0,
          trends: {
            oxygenHistory: [0, 0, 0, 0, 0, 0, 0],
            temperatureHistory: [0, 0, 0, 0, 0, 0, 0],
            humidityHistory: [0, 0, 0, 0, 0, 0, 0],
          },
        },
      ]);

      // 获取状态对应的颜色和图标
      const getStatusInfo = (status: string) => {
        switch (status) {
          case '正常':
            return { color: '#52c41a', icon: '🟢' };
          case '警告':
            return { color: '#faad14', icon: '🟡' };
          case '离线':
            return { color: '#8c8c8c', icon: '⚫' };
          default:
            return { color: '#52c41a', icon: '🟢' };
        }
      };

      // 获取指标阈值判断
      const getThresholdStatus = (type: string, value: number) => {
        let thresholds: { low: number; high: number } = { low: 0, high: 100 };

        switch (type) {
          case 'oxygen':
            thresholds = { low: 19.5, high: 23.0 };
            break;
          case 'temperature':
            thresholds = { low: 18, high: 25 };
            break;
          case 'humidity':
            thresholds = { low: 40, high: 60 };
            break;
        }

        if (value < thresholds.low) {
          return {
            text: '', // 删除文字，仅保留图标
            icon: '🔻',
            color: '#ff7875',
            class: 'status-low',
          };
        } else if (value > thresholds.high) {
          return {
            text: '', // 删除文字，仅保留图标
            icon: '🔺',
            color: '#ff7875',
            class: 'status-high',
          };
        } else {
          return {
            text: '',
            icon: '',
            color: '#52c41a',
            class: 'status-normal',
          };
        }
      };

      // 点击查看详情
      const handleViewDetails = (card: DepartmentStatus) => {
        console.log('查看科室详情:', card.id);
        // 这里可以添加跳转到科室详情页的逻辑
      };

      // 垂直滚动轮播实现
      const scrollOffset = ref(0);
      const animationId = ref<number | null>(null);
      const itemHeight = 100; // 单个项目高度（对应 CSS 的 min-height）
      const itemGap = 10; // 项目间距（对应 CSS 的 margin-bottom）
      const totalHeight = itemHeight + itemGap;
      const currentIndex = ref(0); // 当前显示的科室索引
      const isScrolling = ref(false); // 是否正在滚动
      const pauseTimeout = ref<number | null>(null); // 暂停定时器

      // 滚动到下一个科室
      const scrollToNext = () => {
        if (isScrolling.value) return;
        
        isScrolling.value = true;
        // 计算目标偏移量，让项目在容器中居中
        // 容器高度是 110px，所以要减去 (110 - itemHeight) / 2 来使项目居中
        const containerHeight = 110;
        const centerOffset = (containerHeight - itemHeight) / 2;
        const targetOffset = -(currentIndex.value + 1) * totalHeight + centerOffset;
        const startOffset = scrollOffset.value;
        const distance = targetOffset - startOffset;
        const duration = 500; // 滚动动画持续时间（毫秒）
        const startTime = Date.now();
        
        const animate = () => {
          const elapsed = Date.now() - startTime;
          const progress = Math.min(elapsed / duration, 1);
          
          // 使用缓动函数使动画更平滑
          const easeProgress = 1 - Math.pow(1 - progress, 3);
          scrollOffset.value = startOffset + distance * easeProgress;
          
          if (progress < 1) {
            animationId.value = requestAnimationFrame(animate);
          } else {
            // 动画完成
            isScrolling.value = false;
            currentIndex.value++;
            
            // 检查是否需要重置到开始位置（实现无限循环）
            if (currentIndex.value >= departments.value.length) {
              currentIndex.value = 0;
              // 重置时也要保持居中
              const containerHeight = 110;
              const centerOffset = (containerHeight - itemHeight) / 2;
              scrollOffset.value = centerOffset;
            }
            
            // 停留1秒后继续滚动
            pauseTimeout.value = window.setTimeout(() => {
              scrollToNext();
            }, 1000);
          }
        };
        
        animate();
      };
      
      // 开始自动滚动
      const startScrolling = () => {
        // 初始化时让第一个项目居中
        const containerHeight = 110;
        const centerOffset = (containerHeight - itemHeight) / 2;
        scrollOffset.value = centerOffset;
        
        // 1秒后开始滚动到下一个
        pauseTimeout.value = window.setTimeout(() => {
          scrollToNext();
        }, 1000);
      };

      // 停止滚动动画
      const stopScrolling = () => {
        if (animationId.value) {
          cancelAnimationFrame(animationId.value);
          animationId.value = null;
        }
        if (pauseTimeout.value) {
          clearTimeout(pauseTimeout.value);
          pauseTimeout.value = null;
        }
        isScrolling.value = false;
      };

      onMounted(() => {
        startScrolling();
      });

      onUnmounted(() => {
        stopScrolling();
      });

      // 不再需要多倍数据集，因为使用了新的滚动逻辑

      return {
        departments,
        getStatusInfo,
        getThresholdStatus,
        handleViewDetails,
        cardTitleBg,
        scrollOffset,
      };
    },
  });
</script>

<template>
  <a-card
    class="data-card card-carousel-card"
    :bordered="false"
    :header-style="{
      border: 'none',
      padding: 0,
      height: 'auto',
      minHeight: '40px',
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      backgroundImage: `url(${cardTitleBg})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      borderRadius: '0 0 0 0',
    }"
    :body-style="{
      padding: 0,
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      borderRadius: '0 0 10px 10px',
      minHeight: '110px', // 调整高度以适应时间轴
    }"
  >
    <template #title>
      <div class="title-content">
        <icon-common class="card-icon" />
        <span class="title-text">科室传感器数据轮播</span>
      </div>
    </template>

    <div class="timeline-content">
      <!-- 垂直时间轴 -->
      <div class="timeline-container">
        <div
          class="timeline-track"
          :style="{ transform: `translateY(${scrollOffset}px)` }"
        >
          <!-- 逐项滚动并停留 -->
          <div
            v-for="(department, index) in departments"
            :key="`${department.id}-${index}`"
            class="timeline-item"
            @click="handleViewDetails(department)"
          >
            <!-- 时间轴节点 -->
            <div class="timeline-node">
              <div class="timeline-line"></div>
              <div
                class="timeline-dot"
                :class="`status-${department.status.toLowerCase()}`"
              ></div>
            </div>

            <!-- 内容区域 -->
            <div class="timeline-content">
              <div class="timeline-title">
                {{ department.name }}
                <span
                  v-if="
                    department.sensorAlarmCount &&
                    department.sensorAlarmCount > 0
                  "
                  class="alarm-badge sensor"
                >
                  传感器 {{ department.sensorAlarmCount }} 次
                </span>
              </div>
              <!-- 指标卡片 -->
              <div class="metrics-card">
                <div class="metrics-container">
                  <!-- 氧气浓度 -->
                  <div class="metric-item">
                    <div class="metric-label">氧气浓度</div>
                    <div class="metric-value">
                      <span
                        class="value"
                        :style="
                          getThresholdStatus(
                            'oxygen',
                            department.oxygenConcentration
                          ).icon
                            ? {
                                color: getThresholdStatus(
                                  'oxygen',
                                  department.oxygenConcentration
                                ).color,
                              }
                            : {}
                        "
                      >
                        {{ department.oxygenConcentration }}%
                        <span
                          v-if="
                            getThresholdStatus(
                              'oxygen',
                              department.oxygenConcentration
                            ).icon
                          "
                          class="status-icon"
                        >
                          {{
                            getThresholdStatus(
                              'oxygen',
                              department.oxygenConcentration
                            ).icon
                          }}
                        </span>
                      </span>
                    </div>
                  </div>

                  <!-- 环境温度 -->
                  <div class="metric-item">
                    <div class="metric-label">环境温度</div>
                    <div class="metric-value">
                      <span
                        class="value"
                        :style="
                          getThresholdStatus(
                            'temperature',
                            department.temperature
                          ).icon
                            ? {
                                color: getThresholdStatus(
                                  'temperature',
                                  department.temperature
                                ).color,
                              }
                            : {}
                        "
                      >
                        {{ department.temperature }}℃
                        <span
                          v-if="
                            getThresholdStatus(
                              'temperature',
                              department.temperature
                            ).icon
                          "
                          class="status-icon"
                        >
                          {{
                            getThresholdStatus(
                              'temperature',
                              department.temperature
                            ).icon
                          }}
                        </span>
                      </span>
                    </div>
                  </div>

                  <!-- 环境湿度 -->
                  <div class="metric-item">
                    <div class="metric-label">环境湿度</div>
                    <div class="metric-value">
                      <span
                        class="value"
                        :style="
                          getThresholdStatus('humidity', department.humidity)
                            .icon
                            ? {
                                color: getThresholdStatus(
                                  'humidity',
                                  department.humidity
                                ).color,
                              }
                            : {}
                        "
                      >
                        {{ department.humidity }}%RH
                        <span
                          v-if="
                            getThresholdStatus('humidity', department.humidity)
                              .icon
                          "
                          class="status-icon"
                        >
                          {{
                            getThresholdStatus('humidity', department.humidity)
                              .icon
                          }}
                        </span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<style scoped lang="less">
  .card-carousel-card {
    background: transparent;
    border: none;
    overflow: hidden;

    :deep(.arco-card-header) {
      border: none;
      background: transparent;
    }

    :deep(.arco-card-body) {
      position: relative;
      background: rgba(13, 45, 76, 0.15);
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 201, 242, 0.1);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(0, 201, 242, 0.05) 0%,
          rgba(0, 201, 242, 0.02) 50%,
          rgba(0, 201, 242, 0) 100%
        );
        border-radius: 8px;
        pointer-events: none;
      }
    }

    .title-content {
      padding: 8px 15px;
      min-height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      flex-wrap: nowrap;
      white-space: nowrap;

      .card-icon {
        margin-right: 10px;
        font-size: 18px;
        color: #fff;
      }

      .title-text {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }
    }

    .timeline-content {
      padding: 20px;
      position: relative;
      z-index: 2;

      .timeline-container {
        width: 100%;
        height: 110px; // 固定高度，刚好容纳一个项目（100px + 10px margin）
        overflow: hidden;
        position: relative;
      }

      .timeline-track {
        display: flex;
        flex-direction: column;
        transition: transform 0.3s ease;
      }

      .timeline-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 10px;
        min-height: 100px;
        cursor: pointer;
        position: relative;
      }

      // 时间轴节点样式
      .timeline-node {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 40px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .timeline-line {
          position: absolute;
          left: 19px;
          top: 0;
          bottom: 0;
          width: 2px;
          background: rgba(0, 201, 242, 0.3);
        }

        .timeline-dot {
          position: absolute;
          top: 15px;
          left: 14px;
          width: 12px;
          height: 12px;
          background: #00d4ff;
          border-radius: 50%;
          box-shadow: 0 0 15px rgba(0, 212, 255, 0.6);
          z-index: 2;

          // 根据状态设置不同颜色
          &.status-normal {
            background: #52c41a;
            box-shadow: 0 0 15px rgba(82, 196, 26, 0.6);
          }

          &.status-warning {
            background: #faad14;
            box-shadow: 0 0 15px rgba(250, 173, 20, 0.6);
          }

          &.status-offline {
            background: #8c8c8c;
            box-shadow: 0 0 15px rgba(140, 140, 140, 0.6);
          }
        }
      }

      .timeline-content {
        margin-left: 40px;
        padding: 5px 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;

        .timeline-title {
          font-size: 14px;
          font-weight: 600;
          color: #fff;
          margin-bottom: 5px;
          display: flex;
          align-items: center;
          gap: 10px;

          .alarm-badge {
            font-size: 12px;
            font-weight: normal;
            padding: 2px 8px;
            border-radius: 12px;
            margin-left: 4px;

            &.sensor {
              color: #ffcc00;
              background: rgba(255, 204, 0, 0.2);
              border: 1px solid rgba(255, 204, 0, 0.4);
            }
          }
        }
      }

      // 指标卡片样式
      .metrics-card {
        background: rgba(0, 201, 242, 0.1);
        border: 1px solid rgba(0, 201, 242, 0.3);
        border-radius: 6px;
        padding: 8px 12px;
        margin-top: 8px;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: -8px;
          left: 20px;
          width: 0;
          height: 0;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-bottom: 8px solid rgba(0, 201, 242, 0.3);
        }

        &::after {
          content: '';
          position: absolute;
          top: -7px;
          left: 21px;
          width: 0;
          height: 0;
          border-left: 7px solid transparent;
          border-right: 7px solid transparent;
          border-bottom: 7px solid rgba(0, 201, 242, 0.1);
        }
      }

      // 指标容器样式
      .metrics-container {
        display: flex;
        gap: 10px;
        color: rgba(255, 255, 255, 0.9);
        font-size: 12px;
        flex-wrap: nowrap;
        justify-content: space-around;
        width: 100%;

                .metric-item {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 2px;
          flex-shrink: 0;
          min-width: 60px;
          width: calc(33.33% - 7px); // 固定等宽，减去gap的分配
          transition: all 0.3s ease;

          &:hover {
            color: #fff;
            transform: translateX(2px);
          }

          .metric-label {
            color: #ffffff;
            font-size: 12px;
            font-weight: 600;
            white-space: nowrap;
            text-align: left;
            margin-bottom: 4px;
            opacity: 0.9;
          }

                    .metric-value {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            white-space: nowrap;
            text-align: left;
            min-width: 80px; // 为数值和箭头预留固定宽度
          }

                    .value {
            color: #ffffff;
            font-size: 16px;
            font-weight: 700;
            white-space: nowrap;
            display: inline-flex;
            align-items: center;
            letter-spacing: 0.5px;
            min-width: 60px; // 数值部分固定宽度
          }

          .status-icon {
            display: inline;
            font-size: 14px;
            line-height: 1;
            margin-left: 4px;
          }
        }
      }

      // 最后一个时间轴项隐藏连接线
      .timeline-item:last-child {
        .timeline-line {
          display: none;
        }
      }
    }
  }

  // 动画效果
  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 0.5;
    }
    50% {
      transform: scale(1.5);
      opacity: 0.3;
    }
    100% {
      transform: scale(1);
      opacity: 0.5;
    }
  }

  // 状态脉动动画
  @keyframes statusPulse {
    0%,
    100% {
      opacity: 0.9;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
  }
</style>
