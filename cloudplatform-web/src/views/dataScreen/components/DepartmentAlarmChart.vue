<script lang="ts">
  import { defineComponent, ref, onMounted, onUnmounted, watch } from 'vue';
  import * as echarts from 'echarts';
  import { Card } from '@arco-design/web-vue';
  import cardTitleBg from '@/assets/images/dataScreen/卡片组件/标题渐变背景.png';

  export default defineComponent({
    name: 'DepartmentAlarmChart',
    components: {
      ACard: Card,
    },
    props: {
      /**
       * 科室数据
       */
      departmentData: {
        type: Object,
        required: true,
        default: () => ({
          departments: [],
          values: [],
        }),
      },
      /**
       * 当前视窗
       */
      currentView: {
        type: String,
        required: true,
        default: 'MR',
      },
      /**
       * 屏幕适配函数
       */
      adaptSize: {
        type: Function,
        required: true,
      },
    },
    setup(props) {
      let myChart: echarts.ECharts | null = null;
      let currentIndex = 0;
      const viewItems = 5;
      let scrollInterval: number;
      let unwatch: (() => void) | null = null;

      /**
       * 创建条纹纹理
       */
      const createStripePattern = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) return '';
        canvas.width = 4;
        canvas.height = 8;

        // 设置透明背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 绘制条纹
        ctx.fillStyle = 'rgba(107, 209, 255, 2)';
        ctx.fillRect(0, 0, canvas.width, 4);

        return canvas.toDataURL('image/png');
      };

      /**
       * 更新图表显示
       */
      const updateChartDisplay = () => {
        if (!myChart || myChart.isDisposed()) return;

        const currentTotalItems = props.departmentData.departments.length;
        if (currentTotalItems === 0) {
          myChart.setOption({
            xAxis: { data: [] },
            series: [{ data: [] }],
          });
          return;
        }

        if (currentTotalItems <= viewItems) {
          myChart.setOption({
            xAxis: { data: props.departmentData.departments },
            series: [{ data: props.departmentData.values }],
          });
        } else {
          const showData = {
            departments: [] as string[],
            values: [] as number[],
          };
          for (let i = 0; i < viewItems; i++) {
            const idx = (currentIndex + i) % currentTotalItems;
            if (idx < props.departmentData.departments.length) {
              showData.departments.push(props.departmentData.departments[idx]);
              showData.values.push(props.departmentData.values[idx]);
            }
          }
          myChart.setOption({
            xAxis: { data: showData.departments },
            series: [{ data: showData.values }],
          });
        }
      };

      /**
       * 初始化图表
       */
      const initChart = () => {
        const chartId = `${props.currentView.toLowerCase()}DepartmentChart`;
        const chartDom = document.getElementById(chartId);
        if (!chartDom) return;

        myChart = echarts.init(chartDom);
        const option = {
          grid: {
            left: '0%',
            right: '0%',
            top: '10%',
            bottom: '0%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              color: '#CDF0FF',
              fontSize: props.adaptSize(12),
              interval: 0,
              rotate: 0,
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(0, 201, 242, 0.3)',
                width: props.adaptSize(1),
              },
            },
            axisTick: {
              show: false,
            },
          },
          yAxis: {
            type: 'value',
            max: 200,
            interval: 100,
            axisLabel: {
              color: '#CDF0FF',
              fontSize: props.adaptSize(12),
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(0, 201, 242, 0.3)',
                width: props.adaptSize(1),
              },
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(0, 201, 242, 0.1)',
                width: props.adaptSize(1),
              },
            },
          },
          series: [
            {
              data: [],
              type: 'bar',
              barWidth: props.adaptSize(10),
              showBackground: true,
              backgroundStyle: {
                color: 'rgba(0, 201, 242, 0.1)',
              },
              itemStyle: {
                color: {
                  type: 'pattern',
                  image: new Image(),
                  repeat: 'repeat',
                },
              },
              label: {
                show: true,
                position: 'top',
                color: '#CDF0FF',
                fontSize: props.adaptSize(12),
              },
            },
          ],
          animation: true,
          animationDuration: 1000,
        };

        if (myChart) {
          myChart.setOption(option);
        }

        // 更新纹理
        const pattern = new Image();
        pattern.src = createStripePattern();
        pattern.onload = () => {
          if (myChart) {
            myChart.setOption({
              series: [
                {
                  itemStyle: {
                    color: {
                      type: 'pattern',
                      image: pattern,
                      repeat: 'repeat',
                    },
                  },
                },
              ],
            });
          }
        };

        updateChartDisplay();

        // 监听数据变化
        unwatch = watch(
          () => props.departmentData,
          () => {
            currentIndex = 0;
            updateChartDisplay();
          },
          { deep: true }
        );

        // 设置滚动定时器
        scrollInterval = setInterval(() => {
          const currentTotalItems = props.departmentData.departments.length;
          if (currentTotalItems > viewItems) {
            currentIndex = (currentIndex + 1) % currentTotalItems;
            updateChartDisplay();
          }
        }, 3000);

        // 窗口大小改变事件
        const resizeHandler = () => {
          if (myChart && !myChart.isDisposed()) {
            myChart.resize();
            myChart.setOption({
              xAxis: {
                axisLabel: { fontSize: props.adaptSize(12) },
                axisLine: { lineStyle: { width: props.adaptSize(1) } },
              },
              yAxis: {
                axisLabel: { fontSize: props.adaptSize(12) },
                axisLine: { lineStyle: { width: props.adaptSize(1) } },
                splitLine: { lineStyle: { width: props.adaptSize(1) } },
              },
              series: [
                {
                  barWidth: props.adaptSize(10),
                  label: { fontSize: props.adaptSize(12) },
                },
              ],
            });
          }
        };
        window.addEventListener('resize', resizeHandler);

        return () => {
          clearInterval(scrollInterval);
          if (unwatch) unwatch();
          window.removeEventListener('resize', resizeHandler);
          if (myChart && !myChart.isDisposed()) {
            myChart.dispose();
          }
        };
      };

      // 监听视窗变化，重新初始化图表
      watch(
        () => props.currentView,
        () => {
          // 清理旧图表
          if (myChart && !myChart.isDisposed()) {
            myChart.dispose();
          }
          if (scrollInterval) {
            clearInterval(scrollInterval);
          }
          if (unwatch) {
            unwatch();
          }

          // 延迟初始化新图表
          setTimeout(() => {
            initChart();
          }, 100);
        }
      );

      onMounted(() => {
        initChart();
      });

      onUnmounted(() => {
        clearInterval(scrollInterval);
        if (unwatch) unwatch();
        if (myChart && !myChart.isDisposed()) {
          myChart.dispose();
        }
      });

      return {
        cardTitleBg,
      };
    },
  });
</script>

<template>
  <a-card
    class="data-card department-alarm-card"
    :bordered="false"
    :header-style="{
      border: 'none',
      padding: 0,
      height: 'auto',
      minHeight: '40px',
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      backgroundImage: `url(${cardTitleBg})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      borderRadius: '0 0 0 0',
    }"
    :body-style="{
      padding: 0,
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      borderRadius: '0 0 10px 10px',
      minHeight: '100px',
    }"
  >
    <template #title>
      <div class="title-content">
        <span class="title-text">科室报警情况跟踪</span>
      </div>
    </template>
    <div class="card-content">
      <div
        :id="`${currentView.toLowerCase()}DepartmentChart`"
        style="width: 100%; height: 124px"
      ></div>
    </div>
  </a-card>
</template>

<style scoped lang="less">
  .department-alarm-card {
    background: transparent;
    border: none;
    overflow: hidden;

    :deep(.arco-card-header) {
      border: none;
      background: transparent;
    }

    :deep(.arco-card-body) {
      position: relative;
      background: rgba(13, 45, 76, 0.15);
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 201, 242, 0.1);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(0, 201, 242, 0.05) 0%,
          rgba(0, 201, 242, 0.02) 50%,
          rgba(0, 201, 242, 0) 100%
        );
        border-radius: 8px;
        pointer-events: none;
      }
    }

    .title-content {
      padding: 8px 15px;
      min-height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      flex-wrap: nowrap;
      white-space: nowrap;

      .card-icon {
        margin-right: 10px;
        font-size: 18px;
        color: #fff;
      }

      .title-text {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }
    }

    .card-content {
      padding: 20px;
    }
  }
</style>
