<script lang="ts">
  import { defineComponent, ref, onMounted, onUnmounted, watch, PropType } from 'vue';
  import * as echarts from 'echarts';

  interface ChartData {
    times: string[];
    temperatures: number[];
    humidities: number[];
    oxygenConcentrations: number[];
  }

  export default defineComponent({
    name: 'EnvironmentTrendChart',
    props: {
      /**
       * 图表数据
       */
      data: {
        type: Object as PropType<ChartData>,
        required: true,
        default: () => ({
          times: [],
          temperatures: [],
          humidities: [],
          oxygenConcentrations: [],
        }),
      },
      /**
       * 图表高度
       */
      height: {
        type: String,
        default: '190px',
      },
      /**
       * 屏幕适配函数
       */
      adaptSize: {
        type: Function,
        required: true,
      },
      /**
       * 是否启用自动滚动
       */
      enableAutoScroll: {
        type: Boolean,
        default: true,
      },
    },
    setup(props) {
      let chart: echarts.ECharts | null = null;
      let currentDataZoomIndex = 0;
      let animationFrameId: number;
      let flowingOffset = -1.0;
      let scrollInterval: number;
      const chartRef = ref<HTMLDivElement>();

      /**
       * 初始化图表
       */
      const initChart = () => {
        if (!chartRef.value) return;
        chart = echarts.init(chartRef.value);

        // 添加流动线条动画效果
        const flowingLineTempEffect = {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: 'rgba(77, 133, 255, 0.1)' },
              { offset: 0.2, color: 'rgba(77, 133, 255, 0.1)' },
              { offset: 0.5, color: 'rgba(77, 133, 255, 1)' },
              { offset: 0.8, color: 'rgba(77, 133, 255, 0.1)' },
              { offset: 1, color: 'rgba(77, 133, 255, 0.1)' },
            ],
            global: false,
          },
        };
        const flowingLineHumidEffect = {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: 'rgba(0, 254, 255, 0.1)' },
              { offset: 0.2, color: 'rgba(0, 254, 255, 0.1)' },
              { offset: 0.5, color: 'rgba(0, 254, 255, 1)' },
              { offset: 0.8, color: 'rgba(0, 254, 255, 0.1)' },
              { offset: 1, color: 'rgba(0, 254, 255, 0.1)' },
            ],
            global: false,
          },
        };
        const flowingLineOxygenEffect = {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: 'rgba(255, 165, 0, 0.1)' },
              { offset: 0.2, color: 'rgba(255, 165, 0, 0.1)' },
              { offset: 0.5, color: 'rgba(255, 165, 0, 1)' },
              { offset: 0.8, color: 'rgba(255, 165, 0, 0.1)' },
              { offset: 1, color: 'rgba(255, 165, 0, 0.1)' },
            ],
            global: false,
          },
        };

        // 初始option
        const option = {
          backgroundColor: 'transparent',
          grid: {
            left: '0%',
            right: '0%',
            top: '20%',
            bottom: '0%',
            containLabel: true,
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'line',
              lineStyle: {
                color: 'rgba(0, 201, 242, 0.5)',
                type: 'dashed',
              },
            },
            backgroundColor: 'rgba(9, 44, 76, 0.8)',
            borderColor: 'rgba(0, 201, 242, 0.8)',
            textStyle: {
              color: '#CDF0FF',
              fontSize: props.adaptSize(12),
            },
            formatter: function (params: any[]) {
              let result = `<span style="color: #CDF0FF">${params[0].axisValue}</span><br/>`;
              params.forEach((item) => {
                let color, value;
                if (item.seriesName === '环境温度') {
                  color = '#4d85ff';
                  value = item.value + '℃';
                } else if (item.seriesName === '环境湿度') {
                  color = '#00feff';
                  value = item.value + '%RH';
                } else if (item.seriesName === '氧气浓度') {
                  color = '#ffa500';
                  value = item.value + '%';
                } else {
                  return; // 跳过流动线系列
                }
                const marker = `<span style="display:inline-block;width:${props.adaptSize(
                  10
                )}px;height:${props.adaptSize(
                  10
                )}px;background:${color};margin-right:${props.adaptSize(
                  5
                )}px;"></span>`;
                result +=
                  marker +
                  `<span style="color: #CDF0FF">${item.seriesName}：${value}</span><br/>`;
              });
              return result;
            },
          },
          legend: {
            data: [
              {
                name: '环境温度',
                itemStyle: {
                  color: '#4d85ff',
                },
              },
              {
                name: '环境湿度',
                itemStyle: {
                  color: '#00feff',
                },
              },
              {
                name: '氧气浓度',
                itemStyle: {
                  color: '#ffa500',
                },
              },
            ],
            right: null,
            top: '0%',
            left: 'center',
            textStyle: {
              color: '#ffffff',
              fontSize: props.adaptSize(12),
            },
            itemWidth: props.adaptSize(12),
            itemHeight: props.adaptSize(12),
            icon: 'roundRect',
            itemStyle: {
              borderWidth: 0,
            },
            selectedMode: false,
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: props.data.times,
            axisLabel: {
              color: '#CDF0FF',
              fontSize: props.adaptSize(12),
              showMaxLabel: true,
              showMinLabel: true,
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(0, 201, 242, 0.3)',
                width: props.adaptSize(1),
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
          },
          yAxis: [
            {
              name: '',
              nameTextStyle: {
                color: '#CDF0FF',
                fontSize: props.adaptSize(12),
                padding: [0, props.adaptSize(30), 0, 0],
              },
              type: 'value',
              min: -20,
              max: 60,
              interval: 20,
              axisLabel: {
                color: '#CDF0FF',
                fontSize: props.adaptSize(12),
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(77, 133, 255, 0.3)',
                  width: props.adaptSize(1),
                },
              },
              splitLine: {
                lineStyle: {
                  color: 'rgba(77, 133, 255, 0.1)',
                  width: props.adaptSize(1),
                },
              },
            },
            {
              name: '',
              nameTextStyle: {
                color: '#CDF0FF',
                fontSize: props.adaptSize(12),
                padding: [0, 0, 0, props.adaptSize(30)],
              },
              type: 'value',
              min: 0,
              max: 100,
              interval: 20,
              position: 'right',
              axisLabel: {
                color: '#CDF0FF',
                fontSize: props.adaptSize(12),
                formatter: '{value}',
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(0, 254, 255, 0.3)',
                  width: props.adaptSize(1),
                },
              },
              splitLine: {
                show: false,
              },
            },
          ],
          series: [
            {
              name: '环境温度',
              type: 'line',
              smooth: true,
              symbol: 'none',
              lineStyle: {
                color: '#4d85ff',
                width: props.adaptSize(1),
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(77, 133, 255, 0.3)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(77, 133, 255, 0.05)',
                    },
                  ],
                },
              },
              data: props.data.temperatures,
              z: 2,
              zlevel: 2,
            },
            {
              name: '环境湿度',
              type: 'line',
              smooth: true,
              symbol: 'none',
              yAxisIndex: 1,
              lineStyle: {
                color: '#00feff',
                width: props.adaptSize(1),
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(0, 254, 255, 0.3)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(0, 254, 255, 0.05)',
                    },
                  ],
                },
              },
              data: props.data.humidities,
              z: 1,
              zlevel: 1,
            },
            {
              name: '氧气浓度',
              type: 'line',
              smooth: true,
              symbol: 'none',
              yAxisIndex: 1,
              lineStyle: {
                color: '#ffa500',
                width: props.adaptSize(1),
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(255, 165, 0, 0.3)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(255, 165, 0, 0.05)',
                    },
                  ],
                },
              },
              data: props.data.oxygenConcentrations,
              z: 1,
              zlevel: 1,
            },
            // 添加流动线效果 - 温度
            {
              name: '温度流动线',
              type: 'line',
              smooth: true,
              symbol: 'none',
              showSymbol: false,
              lineStyle: {
                ...flowingLineTempEffect,
                width: props.adaptSize(4),
              },
              data: props.data.temperatures,
              z: 4,
              zlevel: 4,
              silent: true,
              tooltip: {
                show: false,
              },
            },
            // 添加流动线效果 - 湿度
            {
              name: '湿度流动线',
              type: 'line',
              smooth: true,
              symbol: 'none',
              yAxisIndex: 1,
              lineStyle: {
                ...flowingLineHumidEffect,
                width: props.adaptSize(4),
              },
              data: props.data.humidities,
              z: 3,
              zlevel: 3,
              silent: true,
              tooltip: {
                show: false,
              },
            },
            // 添加流动线效果 - 氧气浓度
            {
              name: '氧气流动线',
              type: 'line',
              smooth: true,
              symbol: 'none',
              yAxisIndex: 1,
              lineStyle: {
                ...flowingLineOxygenEffect,
                width: props.adaptSize(4),
              },
              data: props.data.oxygenConcentrations,
              z: 3,
              zlevel: 3,
              silent: true,
              tooltip: {
                show: false,
              },
            },
          ],
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 20,
              minValueSpan: 4,
              zoomLock: true,
            },
          ],
        };
        chart.setOption(option);

        // 监听数据变化，自动刷新图表
        watch(
          () => props.data,
          (newVal) => {
            if (chart) {
              chart.setOption({
                xAxis: { data: newVal.times },
                series: [
                  { data: newVal.temperatures },
                  { data: newVal.humidities },
                  { data: newVal.oxygenConcentrations },
                  { data: newVal.temperatures }, // 流动线-温度
                  { data: newVal.humidities }, // 流动线-湿度
                  { data: newVal.oxygenConcentrations }, // 流动线-氧气浓度
                ],
              });
            }
          },
          { deep: true }
        );

        // 添加窗口大小改变事件监听
        window.addEventListener('resize', handleResize);

        // 设置自动滚动动画
        if (props.enableAutoScroll) {
          const animationDuration = 1000;
          scrollInterval = setInterval(() => {
            if (!props.data.times || props.data.times.length === 0) return;
            const totalPoints = props.data.times.length;
            const viewPoints = Math.max(1, Math.floor(totalPoints * 0.2));
            if (totalPoints <= viewPoints) return;

            currentDataZoomIndex =
              (currentDataZoomIndex + 1) % (totalPoints - viewPoints + 1);
            const start = (currentDataZoomIndex / totalPoints) * 100;
            const end = ((currentDataZoomIndex + viewPoints) / totalPoints) * 100;

            if (chart) {
              chart.setOption({
                dataZoom: [
                  {
                    start: start,
                    end: end,
                  },
                ],
                animation: true,
                animationDurationUpdate: animationDuration,
                animationEasingUpdate: 'cubicOut',
              });
            }
          }, 4000);
        }

        // 设置渐变流动动画
        const animateFlowingLines = () => {
          flowingOffset = flowingOffset + 0.015;
          if (flowingOffset > 1.5) {
            flowingOffset = -1.0;
          }

          const createColorStops = (baseColor: string) =>
            [
              { offset: 0, color: `rgba(${baseColor}, 0.0)` },
              {
                offset: Math.max(0, Math.min(1, 0.1 + flowingOffset)),
                color: `rgba(${baseColor}, 0.0)`,
              },
              {
                offset: Math.max(0, Math.min(1, 0.4 + flowingOffset)),
                color: `rgba(${baseColor}, 0.8)`,
              },
              {
                offset: Math.max(0, Math.min(1, 0.5 + flowingOffset)),
                color: `rgba(${baseColor}, 1)`,
              },
              {
                offset: Math.max(0, Math.min(1, 0.6 + flowingOffset)),
                color: `rgba(${baseColor}, 0.8)`,
              },
              {
                offset: Math.max(0, Math.min(1, 0.9 + flowingOffset)),
                color: `rgba(${baseColor}, 0.0)`,
              },
              { offset: 1, color: `rgba(${baseColor}, 0.0)` },
            ]
              .filter((item) => item.offset >= 0 && item.offset <= 1)
              .sort((a, b) => a.offset - b.offset);

          const tempColorStops = createColorStops('77, 133, 255');
          const humidColorStops = createColorStops('0, 254, 255');
          const oxygenColorStops = createColorStops('255, 165, 0');

          if (chart) {
            chart.setOption(
              {
                series: [
                  {},
                  {},
                  {},
                  {
                    lineStyle: {
                      color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 1,
                        y2: 0,
                        colorStops: tempColorStops,
                        global: false,
                      },
                    },
                  },
                  {
                    lineStyle: {
                      color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 1,
                        y2: 0,
                        colorStops: humidColorStops,
                        global: false,
                      },
                    },
                  },
                  {
                    lineStyle: {
                      color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 1,
                        y2: 0,
                        colorStops: oxygenColorStops,
                        global: false,
                      },
                    },
                  },
                ],
              },
              false
            );
          }
          animationFrameId = requestAnimationFrame(animateFlowingLines);
        };
        animateFlowingLines();
      };

      /**
       * 处理窗口大小改变
       */
      const handleResize = () => {
        if (chart) {
          chart.resize();
          // 更新自适应配置
          chart.setOption({
            tooltip: {
              textStyle: {
                fontSize: props.adaptSize(12),
              },
            },
            legend: {
              textStyle: {
                fontSize: props.adaptSize(12),
              },
              itemWidth: props.adaptSize(12),
              itemHeight: props.adaptSize(12),
            },
            xAxis: {
              axisLabel: {
                fontSize: props.adaptSize(12),
              },
              axisLine: {
                lineStyle: {
                  width: props.adaptSize(1),
                },
              },
            },
            yAxis: [
              {
                nameTextStyle: {
                  fontSize: props.adaptSize(12),
                  padding: [0, props.adaptSize(30), 0, 0],
                },
                axisLabel: {
                  fontSize: props.adaptSize(12),
                },
                axisLine: {
                  lineStyle: {
                    width: props.adaptSize(1),
                  },
                },
                splitLine: {
                  lineStyle: {
                    width: props.adaptSize(1),
                  },
                },
              },
              {
                nameTextStyle: {
                  fontSize: props.adaptSize(12),
                  padding: [0, 0, 0, props.adaptSize(30)],
                },
                axisLabel: {
                  fontSize: props.adaptSize(12),
                },
                axisLine: {
                  lineStyle: {
                    width: props.adaptSize(1),
                  },
                },
              },
            ],
            series: [
              {
                lineStyle: {
                  width: props.adaptSize(1),
                },
              },
              {
                lineStyle: {
                  width: props.adaptSize(1),
                },
              },
              {
                lineStyle: {
                  width: props.adaptSize(1),
                },
              },
              {
                lineStyle: {
                  width: props.adaptSize(4),
                },
              },
              {
                lineStyle: {
                  width: props.adaptSize(4),
                },
              },
              {
                lineStyle: {
                  width: props.adaptSize(4),
                },
              },
            ],
          });
        }
      };

      onMounted(() => {
        initChart();
      });

      onUnmounted(() => {
        clearInterval(scrollInterval);
        if (animationFrameId) {
          cancelAnimationFrame(animationFrameId);
        }
        if (chart && !chart.isDisposed()) {
          chart.dispose();
          chart = null;
        }
        window.removeEventListener('resize', handleResize);
      });

      return {
        chartRef,
      };
    },
  });
</script>

<template>
  <div ref="chartRef" :style="{ width: '100%', height: height }"></div>
</template>