<script lang="ts">
  import { defineComponent, ref, onMounted, onUnmounted, watch, PropType } from 'vue';
  import * as echarts from 'echarts';

  interface FerromagneticRiskData {
    lowRiskCount: number;
    mediumRiskCount: number;
    highRiskCount: number;
  }

  export default defineComponent({
    name: 'FerromagneticRiskGauge',
    props: {
      /**
       * 铁磁报警风险数据
       */
      data: {
        type: Object as PropType<FerromagneticRiskData>,
        required: true,
        default: () => ({
          lowRiskCount: 0,
          mediumRiskCount: 0,
          highRiskCount: 0,
        }),
      },
      /**
       * 图表高度
       */
      height: {
        type: String,
        default: '150px',
      },
      /**
       * 屏幕适配函数
       */
      adaptSize: {
        type: Function,
        required: true,
      },
    },
    setup(props) {
      let chart: echarts.ECharts | null = null;
      const chartRef = ref<HTMLDivElement>();

      /**
       * 计算总数和百分比
       */
      const calculateStats = () => {
        const total = props.data.lowRiskCount + props.data.mediumRiskCount + props.data.highRiskCount;
        return {
          total,
          lowPercent: total > 0 ? Math.round((props.data.lowRiskCount / total) * 100) : 0,
          mediumPercent: total > 0 ? Math.round((props.data.mediumRiskCount / total) * 100) : 0,
          highPercent: total > 0 ? Math.round((props.data.highRiskCount / total) * 100) : 0,
        };
      };

      /**
       * 初始化图表
       */
      const initChart = () => {
        if (!chartRef.value) return;
        chart = echarts.init(chartRef.value);

        updateChart();

        // 添加窗口大小改变事件监听
        window.addEventListener('resize', handleResize);
      };

      /**
       * 更新图表
       */
      const updateChart = () => {
        if (!chart) return;

        const stats = calculateStats();
        
        // 风险等级颜色配置
        const riskColors = {
          low: '#90ee90',    // 浅绿色
          medium: '#00bfff', // 深蓝色  
          high: '#ffd700',   // 金黄色
        };

        const option = {
          backgroundColor: 'transparent',
          title: {
            text: stats.total.toString(),
            subtext: '总报警次数',
            left: 'center',
            top: '45%',
            textStyle: {
              fontSize: props.adaptSize(24),
              fontWeight: 'bold',
              color: '#fff',
              textShadow: '0 0 10px rgba(0, 201, 242, 0.8)',
            },
            subtextStyle: {
              fontSize: props.adaptSize(12),
              color: 'rgba(255, 255, 255, 0.8)',
              fontWeight: 'normal',
            },
          },
          tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(9, 44, 76, 0.9)',
            borderColor: 'rgba(0, 201, 242, 0.8)',
            borderWidth: 1,
            textStyle: {
              color: '#CDF0FF',
              fontSize: props.adaptSize(12),
            },
            formatter: function (params: any) {
              return `
                <div style="padding: 5px;">
                  <div style="color: ${params.color}; font-weight: bold; margin-bottom: 5px;">
                    ${params.name}
                  </div>
                  <div>报警次数: ${params.value}</div>
                  <div>占比: ${params.percent}%</div>
                </div>
              `;
            },
          },
          legend: {
            orient: 'horizontal',
            bottom: '5%',
            left: 'center',
            itemWidth: props.adaptSize(12),
            itemHeight: props.adaptSize(8),
            itemGap: props.adaptSize(15),
            textStyle: {
              color: '#fff',
              fontSize: props.adaptSize(11),
            },
            data: [
              { name: '低风险', itemStyle: { color: riskColors.low } },
              { name: '中风险', itemStyle: { color: riskColors.medium } },
              { name: '高风险', itemStyle: { color: riskColors.high } },
            ],
          },
          series: [
            {
              name: '铁磁报警风险等级',
              type: 'pie',
              radius: ['45%', '70%'],
              center: ['50%', '45%'],
              startAngle: 180,
              endAngle: 360,
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: props.adaptSize(3),
                borderColor: 'rgba(0, 0, 0, 0.2)',
                borderWidth: 1,
                shadowBlur: props.adaptSize(8),
                shadowColor: 'rgba(0, 0, 0, 0.3)',
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: props.adaptSize(15),
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                  scale: true,
                  scaleSize: 5,
                },
                label: {
                  show: true,
                  fontSize: props.adaptSize(14),
                  fontWeight: 'bold',
                  color: '#fff',
                  textShadow: '0 0 8px rgba(0, 0, 0, 0.8)',
                },
              },
              labelLine: {
                show: false,
              },
              label: {
                show: false,
              },
              data: [
                {
                  value: props.data.lowRiskCount,
                  name: '低风险',
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                      { offset: 0, color: riskColors.low },
                      { offset: 1, color: echarts.color.lift(riskColors.low, -0.2) },
                    ]),
                  },
                },
                {
                  value: props.data.mediumRiskCount,
                  name: '中风险',
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                      { offset: 0, color: riskColors.medium },
                      { offset: 1, color: echarts.color.lift(riskColors.medium, -0.2) },
                    ]),
                  },
                },
                {
                  value: props.data.highRiskCount,
                  name: '高风险',
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                      { offset: 0, color: riskColors.high },
                      { offset: 1, color: echarts.color.lift(riskColors.high, -0.2) },
                    ]),
                  },
                },
              ],
              animationType: 'scale',
              animationEasing: 'elasticOut',
              animationDelay: function (idx: number) {
                return Math.random() * 200;
              },
            },
          ],
        };

        chart.setOption(option, true);
      };

      /**
       * 处理窗口大小改变
       */
      const handleResize = () => {
        if (chart) {
          chart.resize();
          updateChart(); // 重新计算适配尺寸
        }
      };

      // 监听数据变化
      watch(
        () => props.data,
        () => {
          updateChart();
        },
        { deep: true }
      );

      onMounted(() => {
        initChart();
      });

      onUnmounted(() => {
        if (chart && !chart.isDisposed()) {
          chart.dispose();
          chart = null;
        }
        window.removeEventListener('resize', handleResize);
      });

      return {
        chartRef,
      };
    },
  });
</script>

<template>
  <div ref="chartRef" :style="{ width: '100%', height: height }"></div>
</template>

<style scoped lang="less">
  // 组件样式已通过ECharts配置实现，无需额外CSS
</style>
