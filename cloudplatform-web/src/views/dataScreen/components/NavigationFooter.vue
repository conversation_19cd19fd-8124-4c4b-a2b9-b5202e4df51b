<script lang="ts">
  import { defineComponent } from 'vue';

  export default defineComponent({
    name: 'NavigationFooter',
    props: {
      /**
       * 当前选中的视窗
       */
      currentView: {
        type: String,
        required: true,
        default: 'MR',
      },
    },
    emits: ['switch-view'],
    setup(props, { emit }) {
      /**
       * 切换视窗
       */
      const switchView = (view: string) => {
        emit('switch-view', view);
      };

      return {
        switchView,
      };
    },
  });
</script>

<template>
  <footer class="footer">
    <div
      class="nav-item"
      :class="{ active: currentView === 'MR' }"
      @click="switchView('MR')"
    >
      <img
        src="@/assets/images/dataScreen/底部组件/1.png"
        alt="MR视窗"
        class="nav-icon"
      />
      <span>MR视窗</span>
    </div>
    <div
      class="nav-item"
      :class="{ active: currentView === 'CT' }"
      @click="switchView('CT')"
    >
      <img
        src="@/assets/images/dataScreen/底部组件/3.png"
        alt="CT视窗"
        class="nav-icon"
      />
      <span>CT视窗</span>
    </div>
  </footer>
</template>

<style scoped lang="less">
  .footer {
    position: fixed;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    width: 80vh;
    height: 7vh;
    background: transparent;
    display: flex;
    justify-content: center;
    gap: 120px;
    align-items: flex-end;
    z-index: 100;

    &::before {
      content: '';
      position: absolute;
      bottom: -60px;
      left: 0;
      width: 80vh;
      height: 7vh;
      background-image: url('@/assets/images/dataScreen/标题组件/底部.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      pointer-events: none;
      z-index: -1;
    }

    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      opacity: 1;
      transition: all 0.3s;
      transform: translateY(30px);
      font-weight: 600;

      &.active {
        opacity: 1;

        .nav-icon {
          filter: brightness(1.2) drop-shadow(0 0 8px rgba(126, 185, 255, 1));
        }

        span {
          color: #ffffff;
          font-size: 16px;
          filter: brightness(0) invert(1) opacity(0.9)
            drop-shadow(0 0 8px rgba(173, 233, 255, 0.8))
            drop-shadow(0 0 15px rgba(173, 233, 255, 0.4));
        }
      }

      &:hover {
        opacity: 1;

        .nav-icon {
          filter: brightness(1.2) drop-shadow(0 0 8px rgba(126, 185, 255, 1));
        }
      }

      .nav-icon {
        width: 68px;
        height: 68px;
        transition: all 0.3s;
        filter: brightness(1);
      }

      span {
        font-size: 14px;
        color: #8adeff;
        opacity: 1;
        filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
      }
    }
  }
</style>
