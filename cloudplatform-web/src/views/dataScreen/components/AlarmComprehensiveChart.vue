<script lang="ts">
  /**
   * 报警综合分析组件
   * 功能：展示报警数据的柱状图和3D环形图分析
   * 上方：柱状图展示各类型报警数量
   * 下方：3D环形图展示报警综合评估
   */
  import { defineComponent, ref, onMounted, onUnmounted, watch } from 'vue';
  import * as echarts from 'echarts';
  import * as THREE from 'three';
  import { Card } from '@arco-design/web-vue';
  import { IconCommon } from '@arco-design/web-vue/es/icon';
    import cardTitleBg from '@/assets/images/dataScreen/卡片组件/标题渐变背景.png';

  interface AlarmComprehensiveData {
    categories: string[];
    barData: number[];
    radarData: {
      name: string;
      value: number[];
    }[];
    indicators: {
      name: string;
      max: number;
    }[];
  }

  export default defineComponent({
    name: 'AlarmComprehensiveChart',
    components: {
      ACard: Card,
      IconCommon,
    },
    props: {
      /**
       * 报警综合分析数据
       */
      alarmComprehensiveData: {
        type: Object as () => AlarmComprehensiveData,
        required: true,
        default: () => ({
          categories: [],
          barData: [],
          radarData: [],
          indicators: [],
        }),
      },
      /**
       * 屏幕适配函数
       */
      adaptSize: {
        type: Function,
        required: true,
      },
    },
    setup(props) {
      let barChart: echarts.ECharts | null = null;
      let threeCleanup: (() => void) | null = null;

      // 假数据
      const fakeBarData = ref([120, 80, 50]); // 柱状图假数据
      
      // 3D环状图数据
      const chartData = ref([
        { label: '氧浓度报警', percentage: 50, count: 120, color: 0xffd700 },
        { label: '温度报警', percentage: 30, count: 80, color: 0x00bfff },
        { label: '湿度报警', percentage: 20, count: 50, color: 0x90ee90 },
      ]);

      /**
       * 初始化柱状图
       */
      const initBarChart = () => {
        const chartDom = document.getElementById('barChart');
        if (!chartDom) return;
        
        barChart = echarts.init(chartDom);
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: 'rgba(0, 201, 242, 0.5)',
            borderWidth: 1,
            textStyle: {
              color: '#fff',
              fontSize: props.adaptSize(12),
            },
          },
          grid: {
            top: '15%',    // 增加顶部空间
            left: '10%',   // 增加左边距
            right: '10%',  // 增加右边距，使柱状图更居中
            bottom: '10%', // 增加底部空间
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: ['氧浓度报警', '温度报警', '湿度报警'], // 使用假数据类别
            axisLabel: {
              color: '#fff',
              fontSize: props.adaptSize(11),
              interval: 0,
              rotate: 0,
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(0, 201, 242, 0.3)',
              },
            },
            axisTick: {
              show: false,
            },
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(0, 201, 242, 0.1)',
                type: 'dashed',
              },
            },
          },
          series: [
            {
              name: '报警数量',
              type: 'bar',
              data: fakeBarData.value, // 使用假数据
              barWidth: '40%', // 调整柱状图宽度
              barGap: '30%', // 设置柱间距
              barCategoryGap: '20%', // 设置类目间距
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(0, 212, 255, 0.8)' },
                  { offset: 1, color: 'rgba(0, 212, 255, 0.3)' },
                ]),
                borderRadius: [6, 6, 0, 0], // 增加圆角效果
                shadowColor: 'rgba(0, 212, 255, 0.4)',
                shadowBlur: 10,
                shadowOffsetY: 2,
              },
              emphasis: {
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(0, 212, 255, 1)' },
                    { offset: 1, color: 'rgba(0, 212, 255, 0.5)' },
                  ]),
                },
              },
              label: {
                show: true,
                position: 'top',
                color: '#fff',
                fontSize: props.adaptSize(11),
                fontWeight: 'bold',
              },
              animationDelay: (idx: number) => idx * 100,
            },
          ],
          animation: true,
          animationEasing: 'cubicOut' as const,
          animationDuration: 1000,
        };
        
        barChart.setOption(option);
      };


      /**
       * 初始化3D环状图
       */
      const init3DRingChart = () => {
        const chartDom = document.getElementById('radarChart');
        if (!chartDom) return;

        // 清除之前的内容
        chartDom.innerHTML = '';

        // 场景设置
        const scene = new THREE.Scene();
        const frustumSize = 10;
        const aspect = chartDom.clientWidth / chartDom.clientHeight;
        const camera = new THREE.OrthographicCamera(
          (frustumSize * aspect) / -2,
          (frustumSize * aspect) / 2,
          frustumSize / 2,
          frustumSize / -2,
          0.1,
          1000
        );
        const renderer = new THREE.WebGLRenderer({
          antialias: true,
          alpha: true,
          powerPreference: 'high-performance',
        });

        renderer.setSize(chartDom.clientWidth, chartDom.clientHeight);
        renderer.setClearColor(0x000000, 0);
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        renderer.sortObjects = true;
        // renderer.outputEncoding = THREE.sRGBEncoding; // 较新版本的Three.js已移除此属性
        renderer.toneMapping = THREE.ACESFilmicToneMapping;
        renderer.toneMappingExposure = 1.2;

        chartDom.appendChild(renderer.domElement);

        // 光照系统
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
        scene.add(ambientLight);

        const mainLight = new THREE.DirectionalLight(0xffffff, 0.4);
        mainLight.position.set(5, 8, 8);
        mainLight.castShadow = true;
        mainLight.shadow.mapSize.width = 2048;
        mainLight.shadow.mapSize.height = 2048;
        scene.add(mainLight);

        const fillLight = new THREE.DirectionalLight(0x4dd0e1, 0.4);
        fillLight.position.set(-8, 5, 5);
        scene.add(fillLight);

        const rimLight = new THREE.DirectionalLight(0xffffff, 0.6);
        rimLight.position.set(5, -5, 10);
        scene.add(rimLight);

        // 前方主要光源 - 照亮圆环侧面
        const frontLight = new THREE.DirectionalLight(0xffffff, 1.0);
        frontLight.position.set(0, 0, 10);
        scene.add(frontLight);

        // 创建3D环状图
        const group = new THREE.Group();
        const innerRadius = 2.88; // 扩大10px后的内径：2.667 × 1.08 ≈ 2.88
        const outerRadius = 3.456; // 扩大10px后的外径：3.2 × 1.08 ≈ 3.456
        const height = 0.6;

        let currentAngle = -Math.PI / 2; // 从顶部开始
        const segments: THREE.Mesh[] = [];

        chartData.value.forEach((item, index) => {
          const angleSpan = (item.percentage / 100) * Math.PI * 2;

          // 计算起始和结束角度
          const startAngle = currentAngle;
          const endAngle = currentAngle + angleSpan;

          // 创建环形形状
          const shape = new THREE.Shape();

          // 外弧
          shape.moveTo(
            Math.cos(startAngle) * outerRadius,
            Math.sin(startAngle) * outerRadius
          );
          shape.absarc(0, 0, outerRadius, startAngle, endAngle, false);

          // 连接到内圆
          shape.lineTo(
            Math.cos(endAngle) * innerRadius,
            Math.sin(endAngle) * innerRadius
          );

          // 内弧
          shape.absarc(0, 0, innerRadius, endAngle, startAngle, true);

          // 闭合形状
          shape.lineTo(
            Math.cos(startAngle) * outerRadius,
            Math.sin(startAngle) * outerRadius
          );

          const extrudeSettings = {
            depth: height,
            bevelEnabled: false,
            steps: 1,
          };

          const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);

          // 材质设置 - 水晶效果
          const material = new THREE.MeshPhysicalMaterial({
            color: item.color,
            transparent: true,
            opacity: 0.85,
            roughness: 0.1,
            metalness: 0.0,
            clearcoat: 1.0,
            clearcoatRoughness: 0.1,
            transmission: 0.3,
            thickness: 0.5,
            ior: 1.5,
            side: THREE.DoubleSide,
            polygonOffset: true,
            polygonOffsetFactor: index * 0.01,
            polygonOffsetUnits: 0.1,
          });

          const segment = new THREE.Mesh(geometry, material);
          segment.position.z = -height / 2 - index * 0.0001;
          segment.castShadow = true;
          segment.receiveShadow = true;

          segment.userData = {
            originalZ: -height / 2 - index * 0.0001,
            isHovered: false,
            data: item,
            originalColor: item.color,
            targetScaleZ: 1,
            currentScaleZ: 1,
          };

          group.add(segment);
          segments.push(segment);

          currentAngle = endAngle;
        });

        scene.add(group);

        // 创建科技特效装饰（位于环状图下方）
        const decorationsGroup = new THREE.Group();

        // 创建内部装饰组
        const innerDecorationsGroup = new THREE.Group();

        // 创建特效函数
        const createTechEffects = () => {
          // 科技环 - 作为底座
          const techRingGroup = new THREE.Group();

          const ringGeometry = new THREE.RingGeometry(4.0, 4.05, 64);
          const ringMaterial = new THREE.MeshBasicMaterial({
            color: 0x00ffff,
            transparent: true,
            opacity: 0.3,
            side: THREE.DoubleSide,
          });
          const ring = new THREE.Mesh(ringGeometry, ringMaterial);
          ring.position.z = -0.5; // 放置在环状图下方
          techRingGroup.add(ring);

          // 创建刻度
          const tickGeometry = new THREE.BoxGeometry(0.02, 0.08, 0.02);
          const mainTickGeometry = new THREE.BoxGeometry(0.02, 0.15, 0.02);

          for (let i = 0; i < 60; i++) {
            const angle = (i / 60) * Math.PI * 2;
            const isMainTick = i % 5 === 0;

            const material = new THREE.MeshBasicMaterial({
              color: isMainTick ? 0x00ffff : 0x4169e1,
              transparent: true,
              opacity: isMainTick ? 0.8 : 0.4,
            });

            const tick = new THREE.Mesh(
              isMainTick ? mainTickGeometry : tickGeometry,
              material
            );

            const radius = 4.1;
            tick.position.set(
              Math.cos(angle) * radius,
              Math.sin(angle) * radius,
              -0.5
            );
            tick.rotation.z = angle;

            techRingGroup.add(tick);
          }

          decorationsGroup.add(techRingGroup);

          // 网格效果
          const gridGroup = new THREE.Group();

          const gridRingMaterial = new THREE.MeshBasicMaterial({
            color: 0x1e3a5f,
            transparent: true,
            opacity: 0.2,
            side: THREE.DoubleSide,
          });

          for (let r = 1.0; r <= 4.5; r += 0.3) {
            const geometry = new THREE.RingGeometry(r - 0.005, r + 0.005, 64);
            const ring = new THREE.Mesh(geometry, gridRingMaterial);
            ring.position.z = -0.6;
            gridGroup.add(ring);
          }

          const lineMaterial = new THREE.LineBasicMaterial({
            color: 0x1e3a5f,
            transparent: true,
            opacity: 0.2,
          });

          for (let i = 0; i < 12; i++) {
            const angle = (i / 12) * Math.PI * 2;
            const geometry = new THREE.BufferGeometry();
            const vertices = new Float32Array([
              Math.cos(angle) * 1.0,
              Math.sin(angle) * 1.0,
              -0.6,
              Math.cos(angle) * 4.5,
              Math.sin(angle) * 4.5,
              -0.6,
            ]);
            geometry.setAttribute(
              'position',
              new THREE.BufferAttribute(vertices, 3)
            );

            const line = new THREE.Line(geometry, lineMaterial);
            gridGroup.add(line);
          }

          decorationsGroup.add(gridGroup);

          // 发光环
          const glowGeometry = new THREE.RingGeometry(3.7, 3.75, 64);
          const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0x49b1e0,
            transparent: true,
            opacity: 0.4,
            side: THREE.DoubleSide,
          });
          const glowRing = new THREE.Mesh(glowGeometry, glowMaterial);
          glowRing.position.z = -0.45;
          decorationsGroup.add(glowRing);

          // 粒子系统
          const particlesGroup = new THREE.Group();
          const particleGeometry = new THREE.SphereGeometry(0.02, 6, 4);

          for (let i = 0; i < 60; i++) {
            const material = new THREE.MeshBasicMaterial({
              color: Math.random() > 0.5 ? 0x76ffff : 0x49b1e0,
              opacity: Math.random() * 0.4 + 0.6,
              transparent: true,
            });

            const particle = new THREE.Mesh(particleGeometry, material);
            const radius = 4.2 + Math.random() * 0.6;
            const angle = Math.random() * Math.PI * 2;

            particle.position.set(
              Math.cos(angle) * radius,
              Math.sin(angle) * radius,
              -0.5 + (Math.random() - 0.5) * 0.2
            );

            particle.userData = {
              speed: 0.003 + Math.random() * 0.006,
              radius: radius,
              angle: angle,
              originalOpacity: material.opacity,
            };

            particlesGroup.add(particle);
          }

          decorationsGroup.add(particlesGroup);

          // 动态环
          const dynamicRingsGroup = new THREE.Group();

          for (let i = 0; i < 3; i++) {
            const radius = 4.3 + i * 0.15;
            const geometry = new THREE.RingGeometry(
              radius - 0.01,
              radius + 0.01,
              64,
              1,
              0,
              Math.PI * 0.5
            );
            const material = new THREE.MeshBasicMaterial({
              color: i === 0 ? 0x00ffff : i === 1 ? 0x49b1e0 : 0x76ffff,
              transparent: true,
              opacity: 0.3 - i * 0.1,
              side: THREE.DoubleSide,
            });
            const arc = new THREE.Mesh(geometry, material);
            arc.position.z = -0.5;
            arc.userData = {
              speed: 0.008 + i * 0.004,
              direction: i % 2 === 0 ? 1 : -1,
            };
            dynamicRingsGroup.add(arc);
          }

          decorationsGroup.add(dynamicRingsGroup);

          // 创建内部装饰元素
          // 1. 中心能量核心（位于文字后方）
          const coreGeometry = new THREE.SphereGeometry(0.8, 32, 32);
          const coreMaterial = new THREE.MeshPhysicalMaterial({
            color: 0x00ffff,
            emissive: 0x00ffff,
            emissiveIntensity: 0.5,
            transparent: true,
            opacity: 0.15,
            roughness: 0.1,
            metalness: 0.8,
            clearcoat: 1.0,
            clearcoatRoughness: 0.1,
          });
          const energyCore = new THREE.Mesh(coreGeometry, coreMaterial);
          energyCore.position.z = -0.8; // 放在文字后方
          innerDecorationsGroup.add(energyCore);

          // 2. 内部光环（多层）
          const innerRingColors = [0x00ffff, 0x49b1e0, 0x76ffff];
          for (let i = 0; i < 3; i++) {
            const radius = 1.2 + i * 0.3;
            const geometry = new THREE.RingGeometry(radius, radius + 0.02, 64);
            const material = new THREE.MeshBasicMaterial({
              color: innerRingColors[i],
              transparent: true,
              opacity: 0.3 - i * 0.08,
              side: THREE.DoubleSide,
            });
            const ring = new THREE.Mesh(geometry, material);
            ring.position.z = -0.3 - i * 0.1;
            ring.userData = {
              rotationSpeed: 0.01 + i * 0.005,
              pulseSpeed: 2 + i * 0.5,
            };
            innerDecorationsGroup.add(ring);
          }

          // 3. 浮动数据点（围绕中心）
          const dataPointGroup = new THREE.Group();
          const pointGeometry = new THREE.OctahedronGeometry(0.05, 0);

          for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const radius = 1.8;

            const material = new THREE.MeshPhysicalMaterial({
              color: i % 2 === 0 ? 0x00ffff : 0x49b1e0,
              emissive: i % 2 === 0 ? 0x00ffff : 0x49b1e0,
              emissiveIntensity: 0.8,
              transparent: true,
              opacity: 0.9,
              metalness: 0.5,
              roughness: 0.2,
            });

            const point = new THREE.Mesh(pointGeometry, material);
            point.position.x = Math.cos(angle) * radius;
            point.position.y = Math.sin(angle) * radius;
            point.position.z = 0;

            point.userData = {
              angle: angle,
              radius: radius,
              floatSpeed: 0.002 + Math.random() * 0.002,
              floatAmplitude: 0.1 + Math.random() * 0.05,
              rotationSpeed: 0.02 + Math.random() * 0.01,
            };

            dataPointGroup.add(point);
          }
          innerDecorationsGroup.add(dataPointGroup);

          // 4. 连接线（从数据点到中心）
          const innerLineMaterial = new THREE.LineBasicMaterial({
            color: 0x00ffff,
            transparent: true,
            opacity: 0.2,
          });

          dataPointGroup.children.forEach((point, index) => {
            const geometry = new THREE.BufferGeometry();
            const vertices = new Float32Array([
              0,
              0,
              0,
              point.position.x * 0.8,
              point.position.y * 0.8,
              0,
            ]);
            geometry.setAttribute(
              'position',
              new THREE.BufferAttribute(vertices, 3)
            );

            const line = new THREE.Line(geometry, innerLineMaterial.clone());
            line.userData = {
              targetPoint: point,
              index: index,
            };
            innerDecorationsGroup.add(line);
          });

          // 5. 内部扫描网格
          const scanGridGroup = new THREE.Group();
          const scanMaterial = new THREE.MeshBasicMaterial({
            color: 0x1e3a5f,
            transparent: true,
            opacity: 0.15,
            wireframe: true,
          });

          const scanGeometry = new THREE.CylinderGeometry(1.5, 1.5, 0.1, 16, 1);
          const scanMesh = new THREE.Mesh(scanGeometry, scanMaterial);
          scanMesh.rotation.x = Math.PI / 2;
          scanGridGroup.add(scanMesh);

          innerDecorationsGroup.add(scanGridGroup);

          // 6. 光晕效果（中心发光）
          const centralGlowGeometry = new THREE.PlaneGeometry(3, 3);
          const centralGlowMaterial = new THREE.MeshBasicMaterial({
            map: createGlowTexture(),
            transparent: true,
            opacity: 0.4,
            blending: THREE.AdditiveBlending,
            depthWrite: false,
          });
          const centralGlow = new THREE.Mesh(
            centralGlowGeometry,
            centralGlowMaterial
          );
          centralGlow.position.z = -0.9; // 在最后方
          innerDecorationsGroup.add(centralGlow);

          decorationsGroup.add(innerDecorationsGroup);

          // 创建光晕纹理的辅助函数
          function createGlowTexture() {
            const canvas = document.createElement('canvas');
            canvas.width = 256;
            canvas.height = 256;
            const context = canvas.getContext('2d');

            const gradient = context.createRadialGradient(
              128,
              128,
              0,
              128,
              128,
              128
            );
            gradient.addColorStop(0, 'rgba(0, 255, 255, 1)');
            gradient.addColorStop(0.3, 'rgba(0, 255, 255, 0.5)');
            gradient.addColorStop(0.5, 'rgba(73, 177, 224, 0.3)');
            gradient.addColorStop(0.7, 'rgba(73, 177, 224, 0.1)');
            gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

            context.fillStyle = gradient;
            context.fillRect(0, 0, 256, 256);

            const texture = new THREE.CanvasTexture(canvas);
            texture.needsUpdate = true;
            return texture;
          }

          // 返回需要动画的元素
          return {
            techRingGroup,
            glowRing,
            particlesGroup,
            dynamicRingsGroup,
            innerDecorationsGroup,
            energyCore,
            dataPointGroup,
            scanGridGroup,
            centralGlow,
          };
        };

        const techEffects = createTechEffects();
        scene.add(decorationsGroup);
        scene.add(innerDecorationsGroup);

        // 鼠标交互
        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2();
        let hoveredSegment: THREE.Mesh | null = null;
        let isMouseInteracting = false;

        const onMouseMove = (event: MouseEvent) => {
          const rect = chartDom.getBoundingClientRect();
          mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
          mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

          raycaster.setFromCamera(mouse, camera);
          const intersects = raycaster.intersectObjects(segments);

          if (intersects.length > 0) {
            const segment = intersects[0].object as THREE.Mesh;
            const segmentIndex = segments.indexOf(segment);

            if (!isMouseInteracting) {
              isMouseInteracting = true;

              segments.forEach((seg) => {
                (seg.material as THREE.MeshPhongMaterial).color.setHex(
                  seg.userData.originalColor
                );
                seg.userData.targetScaleZ = 1;
              });
            }

            if (hoveredSegment !== segment) {
              if (hoveredSegment) {
                (
                  hoveredSegment.material as THREE.MeshPhongMaterial
                ).color.setHex(hoveredSegment.userData.originalColor);
                hoveredSegment.userData.targetScaleZ = 1;
              }

              hoveredSegment = segment;
              (segment.material as THREE.MeshPhongMaterial).color
                .setHex(segment.userData.originalColor)
                .multiplyScalar(1.3);
              segment.userData.targetScaleZ = 1.8;

              // 更新中心标签
              const centerPercentage =
                document.getElementById('center-percentage');
              const centerLabelText =
                document.getElementById('center-label-text');
              if (centerPercentage && centerLabelText) {
                centerPercentage.innerHTML = `${Math.round(
                  segment.userData.data.percentage
                )}<span style="font-size: 0.9rem; font-weight: normal;">%</span>`;
                centerPercentage.style.color = ['#FFD700', '#00BFFF', '#90EE90'][segmentIndex];
                centerLabelText.textContent = segment.userData.data.label;
              }
            }

            chartDom.style.cursor = 'pointer';
          } else {
            if (isMouseInteracting) {
              isMouseInteracting = false;
              hoveredSegment = null;

              segments.forEach((seg) => {
                (seg.material as THREE.MeshPhongMaterial).color.setHex(
                  seg.userData.originalColor
                );
                seg.userData.targetScaleZ = 1;
              });

              currentActiveIndex = -1;
            }

            chartDom.style.cursor = 'default';
          }
        };

        chartDom.addEventListener('mousemove', onMouseMove);

        // 相机位置
        camera.position.set(0, -10, 6);
        camera.lookAt(0, 0, 0);

        // 动画循环
        let animationTime = 0;
        let currentActiveIndex = -1;
        const cycleDuration = 12;
        let animationId: number;

        const animate = () => {
          animationId = requestAnimationFrame(animate);

          animationTime += 0.016;

          // 高度动画
          segments.forEach((segment) => {
            const userData = segment.userData;
            const lerpFactor = 0.1;

            userData.currentScaleZ +=
              (userData.targetScaleZ - userData.currentScaleZ) * lerpFactor;
            segment.scale.z = userData.currentScaleZ;
          });

          // 特效动画
          // 科技环旋转
          techEffects.techRingGroup.rotation.z -= 0.002;

          // 发光环脉冲
          techEffects.glowRing.scale.x = 1 + Math.sin(animationTime * 3) * 0.02;
          techEffects.glowRing.scale.y = 1 + Math.sin(animationTime * 3) * 0.02;
          (techEffects.glowRing.material as THREE.MeshBasicMaterial).opacity =
            0.3 + Math.sin(animationTime * 2) * 0.1;

          // 粒子动画
          techEffects.particlesGroup.children.forEach((particle) => {
            particle.userData.angle += particle.userData.speed;
            particle.position.x =
              Math.cos(particle.userData.angle) * particle.userData.radius;
            particle.position.y =
              Math.sin(particle.userData.angle) * particle.userData.radius;

            const flicker =
              Math.sin(animationTime * 4 + particle.userData.angle * 10) * 0.3 +
              0.7;
            (
              (particle as THREE.Mesh).material as THREE.MeshBasicMaterial
            ).opacity = particle.userData.originalOpacity * flicker;
          });

          // 动态环旋转
          techEffects.dynamicRingsGroup.children.forEach((ring) => {
            ring.rotation.z += ring.userData.speed * ring.userData.direction;
            ((ring as THREE.Mesh).material as THREE.MeshBasicMaterial).opacity =
              0.2 + Math.sin(animationTime * 2 + ring.rotation.z) * 0.1;
          });

          // 内部装饰动画
          // 能量核心脉冲
          if (techEffects.energyCore) {
            const pulseFactor = Math.sin(animationTime * 3) * 0.1 + 0.9;
            techEffects.energyCore.scale.setScalar(pulseFactor);
            (
              techEffects.energyCore.material as THREE.MeshPhysicalMaterial
            ).emissiveIntensity = 0.5 + Math.sin(animationTime * 4) * 0.3;
          }

          // 内部光环旋转和脉冲
          techEffects.innerDecorationsGroup.children.forEach((child) => {
            if (child.userData.rotationSpeed) {
              child.rotation.z += child.userData.rotationSpeed;
              const opacity =
                0.3 + Math.sin(animationTime * child.userData.pulseSpeed) * 0.1;
              (child.material as THREE.MeshBasicMaterial).opacity = opacity;
            }
          });

          // 数据点浮动和旋转
          if (techEffects.dataPointGroup) {
            techEffects.dataPointGroup.children.forEach((point) => {
              if (point.userData.angle !== undefined) {
                // 环绕运动
                point.userData.angle += point.userData.floatSpeed;
                const radius =
                  point.userData.radius +
                  Math.sin(animationTime * 2 + point.userData.angle * 3) *
                    point.userData.floatAmplitude;

                point.position.x = Math.cos(point.userData.angle) * radius;
                point.position.y = Math.sin(point.userData.angle) * radius;
                point.position.z =
                  Math.sin(animationTime * 3 + point.userData.angle) * 0.1;

                // 自转
                point.rotation.x += point.userData.rotationSpeed;
                point.rotation.y += point.userData.rotationSpeed * 0.7;

                // 发光强度变化
                const intensity =
                  0.5 +
                  Math.sin(animationTime * 4 + point.userData.angle * 2) * 0.5;
                (
                  point.material as THREE.MeshPhysicalMaterial
                ).emissiveIntensity = intensity;
              }
            });
          }

          // 更新连接线
          techEffects.innerDecorationsGroup.children.forEach((child) => {
            if (child.userData.targetPoint) {
              const point = child.userData.targetPoint;
              const positions = (child.geometry as THREE.BufferGeometry)
                .attributes.position;
              positions.setXYZ(
                1,
                point.position.x * 0.8,
                point.position.y * 0.8,
                point.position.z
              );
              positions.needsUpdate = true;

              // 连接线闪烁
              const opacity =
                0.1 + Math.sin(animationTime * 6 + child.userData.index) * 0.1;
              (child.material as THREE.LineBasicMaterial).opacity = opacity;
            }
          });

          // 扫描网格旋转
          if (techEffects.scanGridGroup) {
            techEffects.scanGridGroup.rotation.z += 0.003;
            techEffects.scanGridGroup.scale.x =
              1 + Math.sin(animationTime * 2) * 0.05;
            techEffects.scanGridGroup.scale.y =
              1 + Math.sin(animationTime * 2) * 0.05;
          }

          // 中心光晕呼吸
          if (techEffects.centralGlow) {
            const scale = 1 + Math.sin(animationTime * 2.5) * 0.15;
            techEffects.centralGlow.scale.setScalar(scale);
            techEffects.centralGlow.rotation.z += 0.002;
            (
              techEffects.centralGlow.material as THREE.MeshBasicMaterial
            ).opacity = 0.3 + Math.sin(animationTime * 3) * 0.1;
          }

          // 自动循环效果
          if (!isMouseInteracting) {
            const cycleProgress =
              (animationTime % cycleDuration) / cycleDuration;
            const progressIndex = Math.floor(
              cycleProgress * chartData.value.length
            );
            const newActiveIndex =
              (chartData.value.length - 1 - progressIndex) %
              chartData.value.length;

            if (newActiveIndex !== currentActiveIndex) {
              if (
                currentActiveIndex >= 0 &&
                currentActiveIndex < segments.length
              ) {
                const prevSegment = segments[currentActiveIndex];
                (prevSegment.material as THREE.MeshPhongMaterial).color.setHex(
                  prevSegment.userData.originalColor
                );
                prevSegment.userData.targetScaleZ = 1;
              }

              if (newActiveIndex >= 0 && newActiveIndex < segments.length) {
                const segment = segments[newActiveIndex];
                (segment.material as THREE.MeshPhongMaterial).color
                  .setHex(segment.userData.originalColor)
                  .multiplyScalar(1.3);
                segment.userData.targetScaleZ = 1.8;

                // 更新中心标签
                const centerPercentage =
                  document.getElementById('center-percentage');
                const centerLabelText =
                  document.getElementById('center-label-text');
                if (centerPercentage && centerLabelText) {
                  centerPercentage.innerHTML = `${Math.round(
                  segment.userData.data.percentage
                )}<span style="font-size: 0.9rem; font-weight: normal;">%</span>`;
                centerPercentage.style.color = ['#FFD700', '#00BFFF', '#90EE90'][newActiveIndex];
                  centerLabelText.textContent = segment.userData.data.label;
                }
              }

              currentActiveIndex = newActiveIndex;
            }
          }

          renderer.render(scene, camera);
        };

        // 响应式处理
        const handleResize = () => {
          const width = chartDom.clientWidth;
          const height = chartDom.clientHeight;
          const aspect = width / height;
          camera.left = (frustumSize * aspect) / -2;
          camera.right = (frustumSize * aspect) / 2;
          camera.top = frustumSize / 2;
          camera.bottom = frustumSize / -2;
          camera.updateProjectionMatrix();
          renderer.setSize(width, height);
        };

        window.addEventListener('resize', handleResize);
        animate();

        // 清理函数
        const cleanup = () => {
          if (animationId) cancelAnimationFrame(animationId);
          chartDom.removeEventListener('mousemove', onMouseMove);
          window.removeEventListener('resize', handleResize);

          segments.forEach((segment) => {
            if (segment.geometry) segment.geometry.dispose();
            if (segment.material)
              (segment.material as THREE.Material).dispose();
          });

          // 清理特效装饰
          decorationsGroup.traverse((child) => {
            if ((child as THREE.Mesh).isMesh) {
              const mesh = child as THREE.Mesh;
              if (mesh.geometry) mesh.geometry.dispose();
              if (mesh.material) {
                if (Array.isArray(mesh.material)) {
                  mesh.material.forEach((m) => m.dispose());
                } else {
                  mesh.material.dispose();
                }
              }
            }
          });

          // 清理内部装饰
          innerDecorationsGroup.traverse((child) => {
            if ((child as THREE.Mesh).isMesh) {
              const mesh = child as THREE.Mesh;
              if (mesh.geometry) mesh.geometry.dispose();
              if (mesh.material) {
                if (Array.isArray(mesh.material)) {
                  mesh.material.forEach((m) => m.dispose());
                } else {
                  mesh.material.dispose();
                }
              }
            }
            if ((child as THREE.Line).isLine) {
              const line = child as THREE.Line;
              if (line.geometry) line.geometry.dispose();
              if (line.material) {
                if (Array.isArray(line.material)) {
                  line.material.forEach((m) => m.dispose());
                } else {
                  line.material.dispose();
                }
              }
            }
          });

          scene.clear();
          renderer.dispose();

          chartDom.innerHTML = '';
        };

        return cleanup;
      };

      /**
       * 初始化所有图表
       */
      const initCharts = () => {
        initBarChart();
        threeCleanup = init3DRingChart() || null;
      };

      /**
       * 调整图表大小
       */
      const resizeCharts = () => {
        if (barChart) {
          barChart.resize();
          barChart.setOption({
            xAxis: {
              axisLabel: {
                fontSize: props.adaptSize(11),
              },
            },
            yAxis: {
              axisLabel: {
                show: false,
              },
            },
            series: [
              {
                label: {
                  fontSize: props.adaptSize(11),
                },
              },
            ],
          });
        }
        // 3D图表会自动处理resize
      };

      // 监听数据变化并更新图表
      // 注释掉props监听，因为现在使用假数据
      // watch(
      //   () => props.alarmComprehensiveData,
      //   (newValue) => {
      //     if (barChart) {
      //       barChart.setOption({
      //         xAxis: {
      //           data: newValue.categories,
      //         },
      //         series: [
      //           {
      //             data: newValue.barData,
      //           },
      //         ],
      //       });
      //     }
          
      //     // 更新环形图数据
      //     const total = newValue.barData.reduce((sum, val) => sum + val, 0);
      //     if (total > 0) {
      //       chartData.value = [
      //         { 
      //           label: '氧浓度报警', 
      //           percentage: (newValue.barData[0] / total) * 100,
      //           count: newValue.barData[0],
      //           color: 0xffd700 
      //         },
      //         { 
      //           label: '温度报警', 
      //           percentage: (newValue.barData[1] / total) * 100,
      //           count: newValue.barData[1],
      //           color: 0x00bfff 
      //         },
      //         { 
      //           label: '湿度报警', 
      //           percentage: (newValue.barData[2] / total) * 100,
      //           count: newValue.barData[2],
      //           color: 0x90ee90 
      //         },
      //       ];
      //     }
      //   },
      //   { deep: true, immediate: true }
      // );

      onMounted(() => {
        initCharts();
        window.addEventListener('resize', resizeCharts);
      });

      onUnmounted(() => {
        window.removeEventListener('resize', resizeCharts);
        if (barChart) {
          barChart.dispose();
        }
        if (threeCleanup) {
          threeCleanup();
        }
      });

      return {
        cardTitleBg,
        chartData,
        fakeBarData,
      };
    },
  });
</script>

<template>
  <a-card
    class="alarm-comprehensive-card"
    :bordered="false"
    :header-style="{
      border: 'none',
      padding: 0,
      height: 'auto',
      minHeight: '40px',
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      backgroundImage: `url(${cardTitleBg})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      borderRadius: '0 0 0 0',
    }"
    :body-style="{
      padding: 0,
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      borderRadius: '0 0 10px 10px',
      minHeight: '100px',
    }"
  >
    <template #title>
      <div class="title-content">
        <icon-common class="card-icon" />
        <span class="title-text">报警综合分析</span>
      </div>
    </template>
    <div class="card-content">
      <!-- 柱状图 -->
      <div class="chart-section">
        <div id="barChart" class="bar-chart"></div>
      </div>

      <!-- 3D环形图 -->
      <div class="chart-section">
        <div class="chart-container">
          <div id="radarChart" class="radar-chart"></div>
          <!-- 中心标签 -->
          <div class="chart-center">
            <div id="center-percentage" class="center-percentage">50<span style="font-size: 0.9rem; font-weight: normal;">%</span></div>
            <div id="center-label-text" class="center-label">氧浓度报警</div>
          </div>
          <!-- 右侧图例 -->
          <div class="chart-legend">
            <div
              class="legend-item"
              v-for="item in chartData"
              :key="item.label"
            >
              <div
                class="legend-color"
                :style="{
                  backgroundColor: `#${item.color
                    .toString(16)
                    .padStart(6, '0')}`,
                }"
              ></div>
              <span class="legend-text">{{ item.label }}</span>
              <span class="legend-value">{{ item.count }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<style scoped lang="less">
  .alarm-comprehensive-card {
    background: transparent;
    border: none;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.arco-card-header) {
      border: none;
      background: transparent;
      flex: 0 0 auto;
      padding: 8px 0;
    }

    :deep(.arco-card-body) {
      position: relative;
      background: rgba(13, 45, 76, 0.15);
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 201, 242, 0.1);
      flex: 1;
      min-height: 0;
      padding: 0;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(0, 201, 242, 0.05) 0%,
          rgba(0, 201, 242, 0.02) 50%,
          rgba(0, 201, 242, 0) 100%
        );
        border-radius: 8px;
        pointer-events: none;
      }
    }

    .title-content {
      padding: 8px 15px;
      min-height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      flex-wrap: nowrap;
      white-space: nowrap;

      .card-icon {
        margin-right: 10px;
        font-size: 18px;
        color: #fff;
      }

      .title-text {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }
    }

    .card-content {
      padding: 20px;
      display: flex;
      flex-direction: column;
      height: 100%;

      .chart-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;

        &:first-child {
          // 柱状图区域
          flex: 0 0 120px;
          max-height: 120px;
        }

        &:last-child {
          // 3D环状图区域
          flex: 1;
          min-height: 180px;
        }

        .chart-container {
          position: relative;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding-left: 10px;
        }

        .bar-chart {
          width: 100%;
          height: 100%;
        }

        .radar-chart {
          width: 270px; // 扩大10px: 250px + 20px = 270px
          height: 270px;
          position: relative;
          margin-left: -15px; // 向左微调5px，总共向右移动35px
        }

        // 3D环形图中心标签样式
        .chart-center {
          position: absolute;
          top: calc(50% - 5px);
          left: calc(
            10px - 15px + 135px
          ); // 容器padding-left(10px) + radar-chart的margin-left(-15px) + 圆环宽度的一半(135px)
          transform: translate(-50%, -50%);
          text-align: center;
          pointer-events: none;
          z-index: 100; // 提高z-index确保在所有装饰之上

          .center-percentage {
            font-size: 1.8rem;
            font-weight: bold;
            line-height: 1;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            
            .percentage-number {
              color: #4dd0e1;
            }
            
            .percentage-symbol {
              font-size: 0.9rem !important; // 数字大小的一半
              vertical-align: top;
              font-weight: normal;
              display: inline-block;
              margin-left: 2px;
              color: #4dd0e1;
            }
          }

          .center-label {
            font-size: 0.8rem;
            color: #ffffff;
            margin-top: 6px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          }
        }

        // 右侧图例样式
        .chart-legend {
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translateY(-50%);
          display: flex;
          flex-direction: column;
          gap: 12px;

          .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #ffffff;

            .legend-color {
              width: 12px;
              height: 12px;
              border-radius: 2px;
            }

            .legend-text {
              min-width: 70px;
              font-size: 0.85rem;
            }

            .legend-value {
              font-weight: bold;
              color: #4dd0e1;
              font-size: 0.85rem;
            }
          }
        }

        // 响应式媒体查询
        @media (max-width: 1400px) {
          .radar-chart {
            width: 240px; // 在较小屏幕上减小尺寸
            height: 240px;
          }

          .chart-center {
            left: calc(10px - 15px + 120px); // 调整中心位置
          }
        }

        @media (max-width: 1200px) {
          .chart-legend {
            position: static;
            transform: none;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 10px;
            gap: 12px;
          }

          .chart-container {
            flex-direction: column;
          }

          .radar-chart {
            width: 220px; // 进一步减小尺寸
            height: 220px;
          }

          .chart-center {
            left: calc(10px - 15px + 110px); // 再次调整中心位置
          }
        }

        // 针对超宽屏的优化
        @media (min-width: 2560px) {
          .radar-chart {
            width: 320px; // 在超宽屏上增大尺寸
            height: 320px;
          }

          .chart-center {
            left: calc(10px - 15px + 160px); // 调整超宽屏的中心位置
          }
        }
      }
    }
  }
</style>
