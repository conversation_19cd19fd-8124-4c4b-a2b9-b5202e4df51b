<script lang="ts">
  import { defineComponent, onMounted, watch, nextTick } from 'vue';
  import { Card } from '@arco-design/web-vue';
  import { IconFullscreen, IconDesktop } from '@arco-design/web-vue/es/icon';
  import cardTitleBg from '@/assets/images/dataScreen/卡片组件/标题渐变背景.png';

  interface LocationMonitoringStatus {
    locationId: number;
    locationName: string;
    monitoringStatus: string;
    rtspUrls: string[];
  }

  export default defineComponent({
    name: 'MonitoringStatusList',
    components: {
      ACard: Card,
      IconDesktop,
      IconFullscreen,
    },
    props: {
      /**
       * 监控状态列表数据
       */
      locationMonitoringList: {
        type: Array as () => LocationMonitoringStatus[],
        required: true,
        default: () => [],
      },
      /**
       * 当前视窗
       */
      currentView: {
        type: String,
        required: true,
        default: 'MR',
      },
      /**
       * 是否需要滚动
       */
      needScroll: {
        type: Object,
        required: true,
        default: () => ({
          mr: false,
          dr: false,
          ct: false,
          dsa: false,
        }),
      },
    },
    emits: ['item-click', 'overview-click'],
    setup(props, { emit }) {
      /**
       * 获取状态样式类
       */
      const getStatusClass = (status: string) => {
        switch (status) {
          case '在线':
            return 'online';
          case '离线':
            return 'offline';
          case '部分在线':
            return 'partial-online';
          case '无设备':
            return 'no-device';
          case '配置错误':
            return 'config-error';
          default:
            return '';
        }
      };

      /**
       * 处理监控项点击事件
       */
      const handleMonitoringItemClick = (item: LocationMonitoringStatus) => {
        emit('item-click', item);
      };

      /**
       * 处理总览按钮点击事件
       */
      const handleOverviewClick = () => {
        emit('overview-click');
      };

      /**
       * 监控滚动逻辑
       */
      const monitoringScroll = () => {
        const container = document.querySelector('.monitoring-list');
        const listInner = document.querySelector('.monitoring-list-inner');

        if (container && listInner) {
          // 先移除滚动类，强制重置动画状态
          listInner.classList.remove('scroll-enabled');

          // 强制重新计算布局
          void (container as HTMLElement).offsetHeight;

          // 计算实际内容高度（不包括复制的部分）
          const originalContent = listInner.querySelectorAll(
            '.monitoring-item:not([key^="clone-"])'
          );
          const contentHeight = Array.from(originalContent).reduce(
            (total, item) => {
              return total + (item as HTMLElement).offsetHeight;
            },
            0
          );

          // 计算容器高度
          const containerHeight = container.clientHeight;

          // 更新对应视窗的滚动状态
          const viewKey =
            props.currentView.toLowerCase() as keyof typeof props.needScroll;
          const needScrollValue = contentHeight > containerHeight;

          if (needScrollValue) {
            // 计算滚动距离（使用实际内容高度）
            const scrollDistance = contentHeight;
            const scrollDuration = `${scrollDistance * 0.03}s`;

            (listInner as HTMLElement).style.setProperty(
              '--scroll-distance',
              `${scrollDistance}px`
            );
            (listInner as HTMLElement).style.setProperty(
              '--scroll-duration',
              scrollDuration
            );

            // 使用 requestAnimationFrame 确保在下一帧添加滚动类
            requestAnimationFrame(() => {
              listInner.classList.add('scroll-enabled');
            });
          } else {
            listInner.classList.remove('scroll-enabled');
          }
        }
      };

      // 监听数据变化，重新计算滚动
      watch(
        () => props.locationMonitoringList,
        () => {
          nextTick(() => {
            setTimeout(() => {
              monitoringScroll();
            }, 100);
          });
        },
        { deep: true }
      );

      // 监听视窗变化，重新计算滚动
      watch(
        () => props.currentView,
        () => {
          nextTick(() => {
            setTimeout(() => {
              monitoringScroll();
            }, 100);
          });
        }
      );

      onMounted(() => {
        setTimeout(() => {
          monitoringScroll();
        }, 100);
      });

      return {
        cardTitleBg,
        getStatusClass,
        handleMonitoringItemClick,
        handleOverviewClick,
      };
    },
  });
</script>

<template>
  <a-card
    class="data-card monitoring-status-card"
    :bordered="false"
    :header-style="{
      border: 'none',
      padding: 0,
      height: 'auto',
      minHeight: '40px',
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      backgroundImage: `url(${cardTitleBg})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      borderRadius: '0 0 0 0',
    }"
    :body-style="{
      padding: 0,
      backgroundColor: 'rgba(13, 45, 76, 0.5)',
      borderRadius: '0 0 10px 10px',
      minHeight: currentView === 'MR' ? '304px' : '670px',
    }"
  >
    <template #title>
      <div class="title-content">
        <icon-desktop class="card-icon" />
        <span class="title-text">监控实况</span>
        <div class="overview-btn" @click="handleOverviewClick">
          <icon-fullscreen size="20px" />
        </div>
      </div>
    </template>
    <div
      class="card-content monitoring-content"
      :class="{
        'mr-monitoring': currentView === 'MR',
        'dr-monitoring': currentView === 'DR',
        'ct-monitoring': currentView === 'CT',
        'dsa-monitoring': currentView === 'DSA',
      }"
    >
      <div class="monitoring-list">
        <div class="monitoring-list-inner">
          <div
            v-for="item in locationMonitoringList"
            :key="String(item.locationId) + '-' + currentView.toLowerCase()"
            class="monitoring-item"
            @click="handleMonitoringItemClick(item)"
          >
            <div class="item-content">
              <div class="left-content">
                <div
                  :class="['status-dot', getStatusClass(item.monitoringStatus)]"
                ></div>
                <div class="text-content">
                  <div
                    :class="[
                      'status-text',
                      getStatusClass(item.monitoringStatus),
                    ]"
                  >
                    {{ item.monitoringStatus }}
                  </div>
                  <div class="title">{{ item.locationName }}</div>
                </div>
              </div>
              <div class="right-content">
                <div class="icon-wrapper">
                  <img
                    src="@/assets/images/dataScreen/监控实况组件/核磁共振.png"
                    :alt="currentView"
                    class="mri-icon"
                  />
                </div>
              </div>
            </div>
          </div>
          <!-- 复制一份列表用于无缝滚动 -->
          <template v-if="needScroll[currentView.toLowerCase()]">
            <div
              v-for="item in locationMonitoringList"
              :key="
                String(item.locationId) +
                '-' +
                currentView.toLowerCase() +
                '-clone'
              "
              class="monitoring-item"
              @click="handleMonitoringItemClick(item)"
            >
              <div class="item-content">
                <div class="left-content">
                  <div
                    :class="[
                      'status-dot',
                      getStatusClass(item.monitoringStatus),
                    ]"
                  ></div>
                  <div class="text-content">
                    <div
                      :class="[
                        'status-text',
                        getStatusClass(item.monitoringStatus),
                      ]"
                    >
                      {{ item.monitoringStatus }}
                    </div>
                    <div class="title">{{ item.locationName }}</div>
                  </div>
                </div>
                <div class="right-content">
                  <div class="icon-wrapper">
                    <img
                      src="@/assets/images/dataScreen/监控实况组件/核磁共振.png"
                      :alt="currentView"
                      class="mri-icon"
                    />
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </a-card>
</template>

<style scoped lang="less">
  .monitoring-status-card {
    background: transparent;
    border: none;
    overflow: hidden;

    :deep(.arco-card-header) {
      border: none;
      background: transparent;
    }

    :deep(.arco-card-body) {
      position: relative;
      background: rgba(13, 45, 76, 0.15);
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 201, 242, 0.1);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(0, 201, 242, 0.05) 0%,
          rgba(0, 201, 242, 0.02) 50%,
          rgba(0, 201, 242, 0) 100%
        );
        border-radius: 8px;
        pointer-events: none;
      }
    }

    .title-content {
      padding: 8px 15px;
      min-height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      flex-wrap: nowrap;
      white-space: nowrap;

      .card-icon {
        margin-right: 10px;
        font-size: 18px;
        color: #fff;
      }

      .title-text {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }

      .overview-btn {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.1);
        transition: all 0.3s;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
        }

        :deep(.arco-icon) {
          color: #fff;
        }
      }
    }
  }

  .monitoring-content {
    height: calc(284px);
    overflow: hidden;
    padding: 20px !important;
    position: relative;

    &.mr-monitoring {
      height: calc(284px);
    }

    &.dr-monitoring {
      height: calc(790px);
    }

    &.ct-monitoring {
      height: calc(790px);
    }

    &.dsa-monitoring {
      height: calc(790px);
    }

    .monitoring-list {
      position: absolute;
      top: 20px;
      left: 20px;
      right: 24px;
      bottom: 20px;
      height: auto;
      overflow: hidden;

      .monitoring-list-inner {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        will-change: transform;
        transform: translateZ(0);
        backface-visibility: hidden;
        perspective: 1000px;
        contain: layout style paint;
        transform-style: preserve-3d;

        &.scroll-enabled {
          animation: scrollY var(--scroll-duration) linear infinite;
          animation-timing-function: linear;
          animation-fill-mode: forwards;
        }

        &:hover {
          animation-play-state: paused;
        }
      }
    }
  }

  @keyframes scrollY {
    0% {
      transform: translate3d(0, 0, 0);
    }

    100% {
      transform: translate3d(0, -50%, 0);
    }
  }

  .monitoring-item {
    width: 100%;
    min-height: 90px;
    padding: 16px;
    border-radius: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    background: rgba(13, 45, 76, 0.9);
    border: 1px solid rgba(0, 201, 242, 0.06);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(0);
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    .mri-icon {
      width: 100%;
      height: 100%;
      object-fit: contain;
      filter: brightness(0) invert(1) opacity(0.9)
        drop-shadow(0 0 8px rgba(173, 233, 255, 0.8))
        drop-shadow(0 0 15px rgba(173, 233, 255, 0.4));
      pointer-events: none;
      user-drag: none;
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
      user-select: none;
      -moz-user-select: none;
      -webkit-user-select: none;
      -ms-user-select: none;
    }

    &:hover {
      background: rgba(13, 45, 76, 0.9);
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 201, 242, 0.15),
        inset 0 0 20px rgba(0, 201, 242, 0.05);

      .icon-wrapper {
        opacity: 0.9;
      }
    }

    &:active {
      transform: translateY(1px);
      box-shadow: 0 2px 8px rgba(0, 201, 242, 0.1),
        inset 0 0 10px rgba(0, 201, 242, 0.03);
      transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .item-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 100%;
      position: relative;
      z-index: 1;

      .left-content {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 12px;

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 50%;
            opacity: 0.4;
            filter: blur(2px);
          }

          &.online {
            background: #52c41a;
            box-shadow: 0 0 12px rgba(82, 196, 26, 0.8);

            &::after {
              background: #52c41a;
            }
          }

          &.offline {
            background: #8c8c8c;
            box-shadow: 0 0 12px rgba(140, 140, 140, 0.8);

            &::after {
              background: #8c8c8c;
            }
          }

          &.partial-online {
            background: #faad14;
            box-shadow: 0 0 12px rgba(250, 173, 20, 0.8);

            &::after {
              background: #faad14;
            }
          }

          &.no-device {
            background: #d9d9d9;
            box-shadow: 0 0 12px rgba(217, 217, 217, 0.8);

            &::after {
              background: #d9d9d9;
            }
          }

          &.config-error {
            background: #ff4d4f;
            box-shadow: 0 0 12px rgba(255, 77, 79, 0.8);

            &::after {
              background: #ff4d4f;
            }
          }
        }

        .text-content {
          display: flex;
          flex-direction: column;
          gap: 6px;

          .status-text {
            font-size: 12px;
            font-weight: 500;
            text-shadow: 0 0 8px rgba(0, 201, 242, 0.3);

            &.online {
              color: #52c41a;
            }

            &.offline {
              color: #8c8c8c;
            }

            &.partial-online {
              color: #faad14;
            }

            &.no-device {
              color: #d9d9d9;
            }

            &.config-error {
              color: #ff4d4f;
            }
          }

          .title {
            color: #fff;
            font-size: 15px;
            font-weight: 500;
            letter-spacing: 0.5px;
            text-shadow: 0 0 10px rgba(0, 201, 242, 0.5);
          }
        }
      }

      .right-content {
        margin-left: 16px;

        .icon-wrapper {
          width: 36px;
          height: 36px;
          display: flex;
          justify-content: center;
          align-items: center;
          transition: all 0.3s ease;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: -8px;
            left: -8px;
            right: -8px;
            bottom: -8px;
            background: radial-gradient(
              circle,
              rgba(0, 201, 242, 0.2) 0%,
              rgba(0, 201, 242, 0) 70%
            );
            border-radius: 50%;
            opacity: 0.7;
          }

          .mri-icon {
            width: 100%;
            height: 100%;
            object-fit: contain;
            filter: brightness(0) invert(1) opacity(0.9)
              drop-shadow(0 0 8px rgba(173, 233, 255, 0.8))
              drop-shadow(0 0 15px rgba(173, 233, 255, 0.4));
          }
        }
      }
    }
  }
</style>
