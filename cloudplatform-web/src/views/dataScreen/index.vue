<script lang="ts">
  import { defineComponent, ref, onMounted, onUnmounted, Ref } from 'vue';
  // 直接引入背景图片
  import bgImage from '@/assets/images/dataScreen/bg.jpg';
  import footerDecoration2 from '@/assets/images/dataScreen/下装饰/下装饰2.png';
  import xiangya2logo from '@/assets/images/dataScreen/xiangya2logo.png';

  // 引入组件
  import HeaderPanel from './components/HeaderPanel.vue';
  import EnvironmentDataCard from './components/EnvironmentDataCard.vue';
  import AlarmAnalysisChart from './components/AlarmAnalysisChart.vue';
  import TempHumidityChart from './components/TempHumidityChart.vue';
  import AlarmStatistics from './components/AlarmStatistics.vue';
  import DepartmentAlarmChart from './components/DepartmentAlarmChart.vue';
  import MonitoringStatusList from './components/MonitoringStatusList.vue';
  import VideoMonitorPanel from './components/VideoMonitorPanel.vue';
  import NavigationFooter from './components/NavigationFooter.vue';
  import AlarmComprehensiveChart from './components/AlarmComprehensiveChart.vue';
import DepartmentCarousel from './components/DepartmentCarousel.vue';
  import { useRouter } from 'vue-router';

  // Import API function
  import {
    getAverageLatestEnvironmentalData,
    getTempHumidityTrend,
    getEnvironmentalAlarmCount,
  } from '@/api/environmental';
  import {
    getMagneticAlarmStats,
    getMagneticAlarmCount,
    getLocationAlarmCounts,
    LocationAlarmCountDTO,
  } from '@/api/magnetic';
  import {
    getLocationMonitoringStatusList,
    LocationMonitoringStatus,
  } from '@/api/monitoring'; // 新增导入

  import { useServerTime } from '@/utils/real-time';

  interface PieDataItem {
    value: number;
    name: string;
  }

  interface TempHumidityData {
    times: string[];
    temperatures: number[];
    humidities: number[];
    oxygenConcentrations: number[];
  }

  export default defineComponent({
    name: 'DataScreen',
    components: {
      HeaderPanel,
      EnvironmentDataCard,
      AlarmAnalysisChart,
      TempHumidityChart,
      AlarmStatistics,
      DepartmentAlarmChart,
      MonitoringStatusList,
      VideoMonitorPanel,
      NavigationFooter,
      AlarmComprehensiveChart,
      DepartmentCarousel,
    },
    setup() {
      const serverTime = useServerTime();

      const weather = ref({
        type: '晴',
        temperature: '17~28℃',
      });

      // 添加当前选中的视窗状态
      const currentView = ref('MR');
      const showMonitoringCard = ref(false);
      const selectedCamera = ref<{
        title: string;
        rtspUrls: string[];
        currentUrlIndex?: number;
        locationId?: number;
      } | null>(null);

      // 添加是否需要滚动的状态
      const needScroll = ref({
        mr: false,
        dr: false,
        ct: false,
        dsa: false,
      });

      /**
       * 视窗切换方法
       */
      const switchView = (view: string) => {
        currentView.value = view;
      };

      const environmentData = ref({
        airQuality: 0,
        temperature: 0,
        humidity: 0,
      });

      const monitoringData = ref({
        cameraCount: 0,
        recordCount: 0,
      });

      // 用于存储点位监控状态列表的新 ref
      const locationMonitoringList: Ref<LocationMonitoringStatus[]> = ref([]);

      const tempHumidityData = ref<TempHumidityData>({
        times: [],
        temperatures: [],
        humidities: [],
        oxygenConcentrations: [],
      });

      const alarmPieData: Ref<PieDataItem[]> = ref([]);

      // 添加科室报警数据
      const departmentData = ref<{ departments: string[]; values: number[] }>({
        departments: [],
        values: [],
      });

      // 添加报警综合分析数据
      const alarmComprehensiveData = ref({
        categories: ['氧气报警', '温度报警', '湿度报警'],
        barData: [0, 0, 0],
        radarData: [
          {
            name: '报警综合评估',
            value: [0, 0, 0, 0, 0, 0],
          },
        ],
        indicators: [
          { name: '频率', max: 100 },
          { name: '严重程度', max: 100 },
          { name: '影响范围', max: 100 },
          { name: '响应时间', max: 100 },
          { name: '处理效率', max: 100 },
          { name: '预防效果', max: 100 },
        ],
      });

      // 添加报警综合分析数据
      const handleClose = () => {
        showMonitoringCard.value = false;
        selectedCamera.value = null;
      };

      // 跳转到外部系统
      const handleJumpToExternalSystem = () => {
        if (selectedCamera.value && selectedCamera.value.locationId) {
          const url = `http://*************/CloudMagnetWeb/Equipment/PointHospital/HospitalMain?Point=${selectedCamera.value.locationId}`;
          window.location.href = url;
        } else {
          console.warn('无法跳转：未选择点位或点位编号不存在');
        }
      };

      const getStatusClass = (status: string) => {
        switch (status) {
          case '在线':
            return 'online';
          case '离线':
            return 'offline';
          case '部分在线':
            return 'partial-online';
          case '无设备':
            return 'no-device';
          case '配置错误':
            return 'config-error';
          default:
            return '';
        }
      };

      const handleMonitoringItemClick = (item: LocationMonitoringStatus) => {
        selectedCamera.value = {
          title: item.locationName, // 使用 locationName 赋值给 title
          rtspUrls: item.rtspUrls,
          currentUrlIndex: 0, // 默认显示第一个视频源
          locationId: item.locationId, // 添加点位编号
        };
        showMonitoringCard.value = true;
      };

      const router = useRouter();

      // 屏幕分辨率自适应计算函数
      const getScreenScale = () => {
        const baseWidth = 1920;
        const baseHeight = 1080;
        const currentWidth = window.innerWidth;
        const currentHeight = window.innerHeight;

        // 计算统一缩放比例，取宽高缩放比例的最小值，确保内容完整显示
        const scale = Math.min(
          currentWidth / baseWidth,
          currentHeight / baseHeight
        );

        return {
          scaleW: scale, // 使用统一的缩放比例
          scaleH: scale, // 使用统一的缩放比例
          scale,
        };
      };

      // 尺寸适配函数（所有尺寸统一缩放）
      const adaptSize = (size: number) => {
        const { scale } = getScreenScale();
        return Math.floor(size * scale);
      };

      // 跳转到监控总揽页面
      const goToMonitorOverview = () => {
        router.push('/monitor');
      };

      /**
       * 获取磁力报警数据
       * 包含低、中、高三个风险等级的统计
       */
      const fetchMagnetAlarmData = async () => {
        try {
          const { data } = await getMagneticAlarmStats('month');
          console.log('Magnetic Alarm Stats:', data);
          // 根据响应数据更新图表数据
          alarmPieData.value = [
            // { value: data.data.safeCount || 0, name: '无风险' },
            { value: data.data.lowRiskCount || 0, name: '低风险' },
            { value: data.data.mediumRiskCount || 0, name: '中风险' },
            { value: data.data.highRiskCount || 0, name: '高风险' },
          ];
        } catch (err) {
          console.error('Error fetching magnetic alarm stats:', err);
        }
      };

      // 获取环境传感器报警次数
      const fetchEnvironmentalAlarmCount = async () => {
        try {
          const response = await getEnvironmentalAlarmCount('month');
          if (response.data && response.data.success) {
            const totalEnvCount = response.data.data.alarmCount || 0;
            monitoringData.value.recordCount = totalEnvCount;

            // TODO: 需要后端API支持分类统计，目前氧气、温度、湿度报警数据暂时设为0
          }
        } catch (error) {
          console.error('获取环境传感器报警次数失败:', error);
        }
      };

      // 获取铁磁设备报警次数
      const fetchMagneticAlarmCount = async () => {
        try {
          const response = await getMagneticAlarmCount('month');
          if (response.data && response.data.success) {
            const magneticCount = response.data.data.alarmCount || 0;
            monitoringData.value.cameraCount = magneticCount;

            // 铁磁报警数据已统一到barData中处理，无需单独更新
          }
        } catch (error) {
          console.error('获取铁磁设备报警次数失败:', error);
        }
      };

      // 获取环境数据
      const fetchEnvironmentalData = () => {
        getAverageLatestEnvironmentalData()
          .then((response) => {
            if (response.data.statusCode === 200) {
              const data = response.data.data;
              environmentData.value = {
                airQuality: Number(
                  (data.averageOxygenConcentration || 0).toFixed(1)
                ),
                temperature: Number((data.averageTemperature || 0).toFixed(1)),
                humidity: Number((data.averageHumidity || 0).toFixed(1)),
              };
            }
          })
          .catch((error) => {
            console.error('获取环境数据失败:', error);
          });
      };

      // 合并数据获取方法
      const monitoringScroll = () => {
        const container = document.querySelector('.monitoring-list');
        const listInner = document.querySelector('.monitoring-list-inner');

        if (container && listInner) {
          // 先移除滚动类，强制重置动画状态
          listInner.classList.remove('scroll-enabled');

          // 强制重新计算布局
          void (container as HTMLElement).offsetHeight;

          // 计算实际内容高度（不包括复制的部分）
          const originalContent = listInner.querySelectorAll(
            '.monitoring-item:not([key^="clone-"])'
          );
          const contentHeight = Array.from(originalContent).reduce(
            (total, item) => {
              return total + (item as HTMLElement).offsetHeight;
            },
            0
          );

          // 计算容器高度
          const containerHeight = container.clientHeight;

          // 更新对应视窗的滚动状态
          needScroll.value[
            currentView.value.toLowerCase() as keyof typeof needScroll.value
          ] = contentHeight > containerHeight;

          if (contentHeight > containerHeight) {
            // 计算滚动距离（使用实际内容高度）
            const scrollDistance = contentHeight;
            const scrollDuration = `${scrollDistance * 0.03}s`; // 速度调整系数从 0.05 改为 0.03，使滚动更快

            (listInner as HTMLElement).style.setProperty(
              '--scroll-distance',
              `${scrollDistance}px`
            );
            (listInner as HTMLElement).style.setProperty(
              '--scroll-duration',
              scrollDuration
            );

            // 使用 requestAnimationFrame 确保在下一帧添加滚动类
            requestAnimationFrame(() => {
              listInner.classList.add('scroll-enabled');
            });
          } else {
            listInner.classList.remove('scroll-enabled');
          }
        }
      };

      const fetchAllData = async () => {
        try {
          fetchMagnetAlarmData(); // 调用获取铁磁警报数据
          fetchEnvironmentalData(); // 调用获取环境数据
          await fetchTempHumidityTrend(); // 调用获取温湿度趋势数据
          fetchEnvironmentalAlarmCount(); // 新增调用
          fetchMagneticAlarmCount(); // 新增调用
          await fetchLocationAlarmCounts(); // 新增调用获取点位报警次数

          // 新增：获取点位监控状态列表
          const response = await getLocationMonitoringStatusList();
          if (response.success && response.data) {
            locationMonitoringList.value = response.data;
          } else {
            console.error('获取点位监控状态列表失败:', response.message);
            locationMonitoringList.value = []; // 可选：清空或保持旧数据
          }

          // 在数据获取完成后，计算综合报警数据
          calculateAlarmComprehensiveData();

          // 添加一个短暂延时，等待DOM更新
          setTimeout(() => {
            monitoringScroll();
          }, 100);
        } catch (error) {
          console.error('获取数据失败:', error);
          locationMonitoringList.value = []; // 可选：清空或保持旧数据
        }
      };

      // 获取温湿度趋势数据
      const fetchTempHumidityTrend = async () => {
        try {
          const response = await getTempHumidityTrend();
          if (response.data && response.data.success) {
            const hourlyData = response.data.data.hourlyData || [];
            tempHumidityData.value = {
              times: hourlyData.map((item: any) => `${item.hour}:00`),
              temperatures: hourlyData.map((item: any) =>
                item.temperature === null ? 0 : item.temperature
              ),
              humidities: hourlyData.map((item: any) =>
                item.humidity === null ? 0 : item.humidity
              ),
              oxygenConcentrations: hourlyData.map((item: any) =>
                item.oxygenConcentration === null ? 0 : item.oxygenConcentration
              ),
            };
          }
        } catch (error) {
          console.error('获取温湿度趋势数据失败:', error);
        }
      };

      // 获取各点位报警次数数据
      const fetchLocationAlarmCounts = async () => {
        try {
          // 假设我们按月统计
          const response = await getLocationAlarmCounts('month');
          if (response.data && response.data.success && response.data.data) {
            // 根据返回的数据更新 departmentData
            // 过滤掉 locationName 为空或 null 的数据
            const validData = response.data.data.filter(
              (item: LocationAlarmCountDTO) => item.locationName
            ); // 添加类型注解

            departmentData.value = {
              departments: validData.map(
                (item: LocationAlarmCountDTO) => item.locationName
              ), // 添加类型注解
              values: validData.map(
                (item: LocationAlarmCountDTO) => item.alarmCount || 0
              ), // 添加类型注解
            };
          }
        } catch (error) {
          console.error('获取各点位报警次数失败:', error);
          // 可以选择清空数据或保持旧数据
          departmentData.value = { departments: [], values: [] };
        }
      };

      // 计算报警综合分析数据
      const calculateAlarmComprehensiveData = () => {
        // 更新柱状图数据 - 使用真实的报警数据
        alarmComprehensiveData.value.barData = [
          0, // 氧气报警 - 待接入真实数据
          0, // 温度报警 - 待接入真实数据
          0, // 湿度报警 - 待接入真实数据
        ];

        // 计算雷达图数据
        const totalAlarms = alarmComprehensiveData.value.barData.reduce(
          (sum, val) => sum + val,
          0
        );
        const envAlarms =
          alarmComprehensiveData.value.barData[0] +
          alarmComprehensiveData.value.barData[1] +
          alarmComprehensiveData.value.barData[2];

        // 基于实际数据计算雷达图指标
        const frequency = Math.min(totalAlarms * 2, 100); // 频率：总报警数的2倍，最大100
        const severity = Math.min(envAlarms * 3, 100); // 严重程度
        const impact = Math.min(totalAlarms * 1.5, 100); // 影响范围
        const responseTime = Math.max(100 - totalAlarms * 2, 20); // 响应时间（报警越多响应越慢）
        const efficiency = Math.max(80 - totalAlarms * 1.2, 30); // 处理效率
        const prevention = Math.max(90 - totalAlarms * 1.8, 40); // 预防效果

        alarmComprehensiveData.value.radarData = [
          {
            name: '报警综合评估',
            value: [
              frequency,
              severity,
              impact,
              responseTime,
              efficiency,
              prevention,
            ],
          },
        ];
      };

      onMounted(() => {
        // 获取数据
        fetchAllData();

        // 设置每10秒获取一次数据的定时器 (合并后的)
        const dataFetchIntervalId = setInterval(fetchAllData, 60000); // 修改为60000毫秒，即1分钟

        // 组件卸载时清除定时器
        onUnmounted(() => {
          clearInterval(dataFetchIntervalId);
        });

        // 初始获取数据 (现在通过合并后的函数调用)
        fetchAllData();
      });

      return {
        serverTime,
        weather,
        currentView,
        switchView,
        showMonitoringCard,
        selectedCamera,
        handleMonitoringItemClick,
        handleClose,
        handleJumpToExternalSystem,
        goToMonitorOverview,
        adaptSize,
        environmentData,
        monitoringData,
        needScroll,
        bgImage,
        footerDecoration2,
        xiangya2logo,
        departmentData,
        tempHumidityData,
        locationMonitoringList,
        alarmPieData,
        alarmComprehensiveData,
        getStatusClass,
      };
    },
  });
</script>

<template>
  <div class="data-screen" :style="{ backgroundImage: `url(${bgImage})` }">
    <!-- 添加四个方向的渐变阴影 -->
    <div class="right-shadow"></div>
    <div class="top-shadow"></div>
    <div class="bottom-shadow"></div>

    <!-- 底部装饰图片 -->
    <div class="bottom-decoration">
      <img :src="footerDecoration2" alt="bottom decoration" />
    </div>

    <!-- 顶部信息栏 -->
    <HeaderPanel
      :weather="weather"
      :server-time="serverTime || new Date()"
      title="云磁安全云平台"
      :logo="xiangya2logo"
    />

    <!-- 主要内容区 -->
    <main class="main-content">
      <!-- 左侧面板 -->
      <section class="left-panel">
        <EnvironmentDataCard :environment-data="environmentData" />
        <TempHumidityChart
          :temp-humidity-data="tempHumidityData"
          :adapt-size="adaptSize"
        />
        <!-- 铁磁报警分析 -->
        <AlarmAnalysisChart
          :alarm-pie-data="alarmPieData"
          :adapt-size="adaptSize"
          :current-view="currentView"
        />
      </section>

      <!-- 中央内容区 -->
      <section class="center-panel">
        <VideoMonitorPanel
          :show-monitoring-card="showMonitoringCard"
          :selected-camera="selectedCamera"
          @close="handleClose"
          @jump-to-external="handleJumpToExternalSystem"
        />
      </section>

      <!-- 右侧面板 -->
      <section class="right-panel">
        <!-- 报警综合分析卡片 -->
        <div class="alarm-comprehensive-container">
          <AlarmComprehensiveChart
            :alarm-comprehensive-data="alarmComprehensiveData"
            :adapt-size="adaptSize"
          />
        </div>
        
        <DepartmentCarousel />
        <!-- 报警统计卡片 -->
        <!-- <AlarmStatistics
          v-if="currentView === 'MR'"
          :monitoring-data="monitoringData"
        /> -->

        <!-- 科室报警情况跟踪卡片 -->
        <!-- <DepartmentAlarmChart
          v-if="currentView === 'MR'"
          :department-data="departmentData"
          :current-view="currentView"
          :adapt-size="adaptSize"
        /> -->

        <!-- 监控实况卡片 -->
        <MonitoringStatusList
          :location-monitoring-list="locationMonitoringList"
          :current-view="currentView"
          :need-scroll="needScroll"
          @item-click="handleMonitoringItemClick"
          @overview-click="goToMonitorOverview"
        />
      </section>
    </main>

    <!-- 底部导航 -->
    <NavigationFooter :current-view="currentView" @switch-view="switchView" />
  </div>
</template>

<style scoped lang="less">
  .data-screen {
    width: 100vw;
    height: 100vh;
    background-color: #0a2b4a;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #fff;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;

    // 添加四个方向的渐变阴影
    &::after {
      content: '';
      position: fixed;
      pointer-events: none;
      z-index: 1;
    }

    // 底部装饰图片
    .bottom-decoration {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      z-index: 3;
      pointer-events: none;

      img {
        width: 100%;
        display: block;
      }
    }

    // 左侧阴影
    &::after {
      left: 0;
      top: 0;
      width: 33vh;
      height: 100vh;
      background: linear-gradient(to right, rgba(9, 44, 72, 0.8), transparent);
    }

    // 右侧阴影
    .right-shadow {
      position: fixed;
      right: 0;
      top: 0;
      width: 33vh;
      height: 100vh;
      background: linear-gradient(to left, rgba(9, 44, 72, 0.8), transparent);
      pointer-events: none;
      z-index: 1;
    }

    // 上方阴影
    .top-shadow {
      position: fixed;
      left: 0;
      top: 0;
      width: 100vw;
      height: 12vh;
      background: linear-gradient(to bottom, #092c48, transparent);
      pointer-events: none;
      z-index: 1;
    }

    // 下方阴影
    .bottom-shadow {
      position: fixed;
      left: 0;
      bottom: 0;
      width: 100vw;
      height: 12vh;
      background: linear-gradient(to top, #092c48, transparent);
      pointer-events: none;
      z-index: 1;
    }

    // 确保所有内容在阴影上层
    .header,
    .main-content,
    .footer {
      position: relative;
      z-index: 2;
    }

    .header {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 110px;
      z-index: 100;
      overflow: hidden;

      .header-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: center;
      }

      .header-bg-1 {
        z-index: 3;
        filter: contrast(117%) saturate(111%);
      }

      .header-bg-2 {
        z-index: 4;
        filter: contrast(105%) saturate(105%) brightness(109%);
      }

      .header-content {
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 40px;
        position: relative;
        z-index: 5;
      }

      .weather-info {
        font-size: 28px;
        color: #ade9ff;
        display: flex;
        align-items: center;
        gap: 24px;
        margin-top: 20px;
        margin-left: 50px;
        position: relative;
        z-index: 1;
        filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
          drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
          drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));
        font-weight: 500;

        :deep(.arco-icon-spin) {
          animation: weather-icon-spin 8s infinite linear;
        }
      }

      @keyframes weather-icon-spin {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }

      .title {
        font-size: 40px;
        font-weight: bold;
        background: linear-gradient(180deg, #fff 0%, #bcedff 100%);
        -webkit-background-clip: text;
        color: transparent;
        font-family: 'Bank Gothic', 'Orbitron', 'Rajdhani', 'Quantico',
          'Audiowide', 'Oxanium', 'Transparent', system-ui;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: 45%;
        transform: translate(-50%, -50%);
        margin: 0;
        z-index: 2;
        font-weight: 600;
        letter-spacing: 4px;
        filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
      }

      .time {
        font-size: 28px;
        color: #ade9ff;
        position: relative;
        z-index: 1;
        margin-top: 20px;
        margin-right: 50px;
        filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
          drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
          drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));
        font-weight: 500;
      }
    }

    .main-content {
      flex: 1;
      display: flex;
      padding: 106px 60px 140px;
      gap: 20px;

      .left-panel,
      .right-panel {
        width: calc(22% + 50px);
        display: flex;
        flex-direction: column;
        gap: 15px;

        .alarm-comprehensive-container {
          // AlarmComprehensiveChart组件使用固定高度
          flex: 0 0 auto;
          height: 350px; // 设置固定高度
          min-height: 300px;
          overflow: hidden;
        }
      }

      .center-panel {
        flex: 1;
        position: relative;

        .data-card {
          background: transparent;
          border: none;
          overflow: hidden;

          :deep(.arco-card-header) {
            border: none;
            background: transparent;
          }

          :deep(.arco-card-body) {
            position: relative;
            background: rgba(13, 45, 76, 0.15);
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(0, 201, 242, 0.1);

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(
                135deg,
                rgba(0, 201, 242, 0.05) 0%,
                rgba(0, 201, 242, 0.02) 50%,
                rgba(0, 201, 242, 0) 100%
              );
              border-radius: 8px;
              pointer-events: none;
            }
          }

          .title-content {
            padding: 0 10px;
            height: 100%;
            display: flex;
            align-items: center;
            position: relative;

            .card-icon {
              margin-right: 10px;
              font-size: 18px;
              color: #fff;
            }

            .title-text {
              color: #fff;
              font-size: 16px;
              font-weight: 600;
              filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
            }

            .jump-btn {
              position: absolute;
              right: 45px;
              top: 50%;
              transform: translateY(-50%);
              width: 20px;
              height: 20px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              color: #fff;
              opacity: 0.8;
              transition: all 0.3s;

              &:hover {
                opacity: 1;
                transform: translateY(-50%) scale(1.1);
                color: #00d4ff;
              }

              :deep(.arco-icon) {
                font-size: 16px;
              }
            }

            .close-btn {
              position: absolute;
              right: 10px;
              top: 50%;
              transform: translateY(-50%);
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              color: #fff;
              opacity: 0.8;
              transition: all 0.3s;

              &:hover {
                opacity: 1;
                transform: translateY(-50%) scale(1.1);
              }

              :deep(.arco-icon) {
                font-size: 18px;
              }
            }
          }

          .card-content {
            padding: 20px;
          }
        }
      }
    }

    .panel-box {
      position: relative;
      background: rgba(0, 146, 255, 0.1);
      padding: 20px;
      height: calc(50% - 10px);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('@/assets/images/dataScreen/modal2.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        pointer-events: none;
      }

      h3 {
        position: relative;
        color: #7eb9ff;
        font-size: 18px;
        margin-bottom: 20px;
        z-index: 2;
      }

      .data-item,
      .stat-item {
        position: relative;
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        z-index: 2;

        .value {
          color: #00ff9d;
        }
      }
    }

    .footer {
      position: fixed;
      bottom: 60px;
      left: 50%;
      transform: translateX(-50%);
      width: 80vh;
      height: 7vh;
      background: transparent;
      display: flex;
      justify-content: center;
      gap: 48px;
      align-items: flex-end;
      z-index: 100;

      &::before {
        content: '';
        position: absolute;
        bottom: -60px;
        left: 0;
        width: 80vh;
        height: 7vh;
        background-image: url('@/assets/images/dataScreen/标题组件/底部.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        pointer-events: none;
        z-index: -1;
      }

      .nav-item {
        // display: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        opacity: 1;
        transition: all 0.3s;
        transform: translateY(30px);
        font-weight: 600;

        // 第二个导航项 (DR视窗)
        &:nth-child(2) {
          transform: translateY(calc(30px - 1vh));
        }

        // 第三个导航项 (CT视窗)
        &:nth-child(3) {
          transform: translateY(calc(30px - 1vh));
        }

        &.active {
          opacity: 1;

          .nav-icon {
            filter: brightness(1.2) drop-shadow(0 0 8px rgba(126, 185, 255, 1));
          }

          span {
            color: #ffffff;
            font-size: 16px;
            filter: brightness(0) invert(1) opacity(0.9)
              drop-shadow(0 0 8px rgba(173, 233, 255, 0.8))
              drop-shadow(0 0 15px rgba(173, 233, 255, 0.4));
          }
        }

        &:hover {
          opacity: 1;

          .nav-icon {
            filter: brightness(1.2) drop-shadow(0 0 8px rgba(126, 185, 255, 1));
          }
        }

        .nav-icon {
          width: 68px;
          height: 68px;
          transition: all 0.3s;
          filter: brightness(1);
        }

        span {
          font-size: 14px;
          color: #8adeff;
          opacity: 1;
          filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
        }
      }
    }

    .left-panel {
      width: 22%;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .data-card {
        background: transparent;
        border: none;
        overflow: hidden;

        :deep(.arco-card-header) {
          border: none;
          background: transparent;
        }

        :deep(.arco-card-body) {
          position: relative;
          background: rgba(13, 45, 76, 0.15);
          border-radius: 8px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
          border: 1px solid rgba(0, 201, 242, 0.1);

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
              135deg,
              rgba(0, 201, 242, 0.05) 0%,
              rgba(0, 201, 242, 0.02) 50%,
              rgba(0, 201, 242, 0) 100%
            );
            border-radius: 8px;
            pointer-events: none;
          }
        }

        .title-content {
          padding: 0 10px;
          height: 100%;
          display: flex;
          align-items: center;
          position: relative;

          .card-icon {
            margin-right: 10px;
            font-size: 18px;
            color: #fff;
          }

          .title-text {
            color: #fff;
            font-size: 16px;
            font-weight: 600;
            filter: drop-shadow(0 0 14px rgba(0, 0, 0, 0.65));
          }
        }

        .card-content {
          padding: 20px;

          .data-item {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-bottom: 15px;

            .label {
              color: #fafeff;
              font-size: 14px;
              margin-bottom: 4px;
            }

            .value {
              color: #fafeff;
              font-size: 28px;
              font-weight: 500;

              .unit {
                font-size: 14px;
                margin-left: 4px;
                opacity: 0.8;
              }
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    .environment-data {
      height: 100%;
      min-height: 160px;
      display: flex;
      align-items: center;

      .icon-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        padding-left: 20px;

        .oxygen-component {
          position: relative;
          width: 100px;
          height: 100px;
          display: flex;
          justify-content: center;
          align-items: center;
          transform: translateY(20%);

          img,
          video {
            position: absolute;
            width: 100%;
            height: 100%;
            object-fit: contain;
          }

          .particle-effect {
            z-index: 3;
            opacity: 0.8;
            mix-blend-mode: screen;
            pointer-events: none;
            bottom: 40%;
          }

          .base {
            z-index: 1;
            filter: hue-rotate(350deg);
          }

          .icon {
            z-index: 2;
            width: 80%;
            height: 80%;
            right: 25%;
            top: 15%;
            filter: saturate(144%) contrast(150%);
          }

          .light {
            z-index: 3;
            bottom: 10%;
            opacity: 0.7;
            filter: hue-rotate(350deg);
          }

          .ring {
            z-index: 4;
            animation: ringRise 3s infinite linear;
            transform-origin: center bottom;
            filter: brightness(120%) hue-rotate(350deg);
          }

          .reflection {
            z-index: 3;
            bottom: -10%;
            opacity: 0.3;
            transform: scaleY(-1);
            filter: brightness(120%) hue-rotate(350deg) blur(2.5px);
          }
        }
      }

      .data-container {
        padding: 10px 20px;

        .data-row {
          margin-bottom: 15px;
          width: 100%;
          display: flex;
          justify-content: flex-end;
          position: relative;
          padding: 5px;
          border-radius: 0px;

          // 氧气浓度组的渐变背景
          &:first-child {
            background: linear-gradient(
              to left,
              rgba(138, 222, 255, 0.15),
              transparent
            );
          }

          // 环境温湿度组的渐变背景
          &.two-columns {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            background: linear-gradient(
              to left,
              rgba(138, 222, 255, 0.15),
              transparent
            );

            .data-item {
              flex: 1;
            }
          }

          &:last-child {
            margin-bottom: 0;
          }
        }

        .data-item {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          width: calc(50% - 10px); // 减去gap的一半，保持与下方对齐

          &.oxygen {
            .value {
              font-size: 24px;
            }
          }

          .label {
            color: #ffffff;
            font-size: 14px;
            margin-bottom: 4px;
            filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
              drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
              drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));
          }

          .value {
            color: #ffffff;
            font-size: 24px;
            font-weight: 500;
            filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
              drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
              drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));

            .unit {
              font-size: 14px;
              margin-left: 4px;
              opacity: 0.8;
            }
          }
        }
      }
    }

    @keyframes ringRise {
      0% {
        transform: translateY(0px);
        opacity: 0;
      }

      20% {
        opacity: 1;
      }

      80% {
        opacity: 1;
      }

      100% {
        transform: translateY(-50px);
        opacity: 0;
      }
    }

    .alarm-statistics {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 20px;
      height: 100%;
      gap: 40px;

      .alarm-item {
        display: flex;
        align-items: center;
        gap: 12px;

        .icon-box {
          position: relative;
          width: 60px;
          height: 60px;

          .icon-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
          }

          .icon-front {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 46px;
            height: 46px;
            filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
              drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
              drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));
          }
        }

        .alarm-content {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .label {
            color: #ffffff;
            font-size: 14px;
            margin-bottom: 4px;
            filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
              drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
              drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));
          }

          .value {
            color: #ffffff;
            font-size: 24px;
            font-weight: 500;
            filter: drop-shadow(0 0 10px rgba(173, 233, 255, 0.7))
              drop-shadow(0 0 20px rgba(173, 233, 255, 0.5))
              drop-shadow(0 0 30px rgba(173, 233, 255, 0.3));

            .unit {
              font-size: 14px;
              margin-left: 4px;
              opacity: 0.8;
            }
          }
        }
      }
    }

    .monitoring-content {
      height: calc(450px);
      overflow: hidden;
      padding: 20px !important;
      position: relative;

      &.mr-monitoring {
        height: calc(450px);
      }

      &.dr-monitoring {
        height: calc(840px);
      }

      &.ct-monitoring {
        height: calc(840px);
      }

      &.dsa-monitoring {
        height: calc(840px);
      }

      .monitoring-list {
        position: absolute;
        top: 20px;
        left: 20px;
        right: 24px;
        bottom: 20px;
        height: auto;
        overflow: hidden;

        .monitoring-list-inner {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 20px;
          will-change: transform;
          transform: translateZ(0);
          backface-visibility: hidden;
          perspective: 1000px;
          contain: layout style paint;
          transform-style: preserve-3d;

          &.scroll-enabled {
            animation: scrollY var(--scroll-duration) linear infinite;
            animation-timing-function: linear;
            animation-fill-mode: forwards;
          }

          &:hover {
            animation-play-state: paused;
          }
        }
      }
    }

    @keyframes scrollY {
      0% {
        transform: translate3d(0, 0, 0);
      }

      100% {
        transform: translate3d(0, -50%, 0);
      }
    }

    .monitoring-item {
      width: 100%;
      min-height: 80px;
      padding: 16px;
      border-radius: 12px;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      background: rgba(13, 45, 76, 0.9);
      border: 1px solid rgba(0, 201, 242, 0.06);
      position: relative;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(0);
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;

      .mri-icon {
        width: 100%;
        height: 100%;
        object-fit: contain;
        filter: brightness(0) invert(1) opacity(0.9)
          drop-shadow(0 0 8px rgba(173, 233, 255, 0.8))
          drop-shadow(0 0 15px rgba(173, 233, 255, 0.4));
        pointer-events: none;
        user-drag: none;
        -webkit-user-drag: none;
        -khtml-user-drag: none;
        -moz-user-drag: none;
        -o-user-drag: none;
        user-select: none;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
      }

      &:hover {
        background: rgba(13, 45, 76, 0.9);
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 201, 242, 0.15),
          inset 0 0 20px rgba(0, 201, 242, 0.05);

        .icon-wrapper {
          opacity: 0.9;
        }
      }

      &:active {
        transform: translateY(1px);
        box-shadow: 0 2px 8px rgba(0, 201, 242, 0.1),
          inset 0 0 10px rgba(0, 201, 242, 0.03);
        transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .item-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 100%;
        position: relative;
        z-index: 1;

        .left-content {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 12px;

          .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              top: -2px;
              left: -2px;
              right: -2px;
              bottom: -2px;
              border-radius: 50%;
              opacity: 0.4;
              filter: blur(2px);
            }

            &.online {
              background: #52c41a;
              box-shadow: 0 0 12px rgba(82, 196, 26, 0.8);

              &::after {
                background: #52c41a;
              }
            }

            &.offline {
              background: #8c8c8c;
              box-shadow: 0 0 12px rgba(140, 140, 140, 0.8);

              &::after {
                background: #8c8c8c;
              }
            }

            &.partial-online {
              background: #faad14;
              box-shadow: 0 0 12px rgba(250, 173, 20, 0.8);

              &::after {
                background: #faad14;
              }
            }

            &.no-device {
              background: #d9d9d9;
              box-shadow: 0 0 12px rgba(217, 217, 217, 0.8);

              &::after {
                background: #d9d9d9;
              }
            }

            &.config-error {
              background: #ff4d4f;
              box-shadow: 0 0 12px rgba(255, 77, 79, 0.8);

              &::after {
                background: #ff4d4f;
              }
            }
          }

          .text-content {
            display: flex;
            flex-direction: column;
            gap: 6px;

            .status-text {
              font-size: 12px;
              font-weight: 500;
              text-shadow: 0 0 8px rgba(0, 201, 242, 0.3);

              &.online {
                color: #52c41a;
              }

              &.offline {
                color: #8c8c8c;
              }

              &.partial-online {
                color: #faad14;
              }

              &.no-device {
                color: #d9d9d9;
              }

              &.config-error {
                color: #ff4d4f;
              }
            }

            .title {
              color: #fff;
              font-size: 15px;
              font-weight: 500;
              letter-spacing: 0.5px;
              text-shadow: 0 0 10px rgba(0, 201, 242, 0.5);
            }
          }
        }

        .right-content {
          margin-left: 16px;

          .icon-wrapper {
            width: 36px;
            height: 36px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all 0.3s ease;
            position: relative;

            &::before {
              content: '';
              position: absolute;
              top: -8px;
              left: -8px;
              right: -8px;
              bottom: -8px;
              background: radial-gradient(
                circle,
                rgba(0, 201, 242, 0.2) 0%,
                rgba(0, 201, 242, 0) 70%
              );
              border-radius: 50%;
              opacity: 0.7;
            }

            .mri-icon {
              width: 100%;
              height: 100%;
              object-fit: contain;
              filter: brightness(0) invert(1) opacity(0.9)
                drop-shadow(0 0 8px rgba(173, 233, 255, 0.8))
                drop-shadow(0 0 15px rgba(173, 233, 255, 0.4));
            }
          }
        }
      }
    }

    .video-container {
      width: 100%;
      background-color: #000;
    }

    .no-camera-selected {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 600px;
      color: #fff;
      font-size: 16px;
    }

    .card-content {
      padding: 16px;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 16px;

      .main-video-container {
        height: 65%;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 4px;
        overflow: hidden;
      }

      .no-camera-selected {
        height: 65%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 16px;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 4px;
      }

      .sub-videos-container {
        height: 30%;
        display: flex;
        gap: 16px;

        .sub-video {
          flex: 1;
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 4px;
          overflow: hidden;
        }
      }
    }
  }

  .title-content {
    display: flex;
    align-items: center;
    height: 100%;
    position: relative;
    padding: 0 10px;
  }

  .card-icon {
    width: 20px;
    height: 20px;
    margin-right: 5px;
  }

  .title-text {
    font-size: 14px;
    font-weight: 500;
    color: #fff;
  }

  .overview-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s;
  }

  .overview-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .overview-btn :deep(.arco-icon) {
    color: #fff;
  }

  .arrow-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    :deep(.arco-icon) {
      color: #fff;
      font-size: 16px;
    }
  }
</style>
