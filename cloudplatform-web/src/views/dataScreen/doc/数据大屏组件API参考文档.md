# CloudPlatform 数据大屏组件 API 参考文档

## 📋 文档说明

本文档提供 CloudPlatform 数据大屏组件的详细 API 参考，包括完整的类型定义、接口规范和使用示例。

**文档版本**: v2.0  
**更新日期**: 2025-06-23  
**技术栈**: Vue 3 + TypeScript + Composition API

## 🔧 类型定义

### 基础数据类型

```typescript
// 天气信息类型
interface WeatherInfo {
  type: string; // 天气类型：'晴' | '多云' | '雨' | '雪'
  temperature: string; // 温度范围：'17~28℃'
}

// 环境数据类型
interface EnvironmentData {
  airQuality: number; // 氧气浓度 (%)
  temperature: number; // 环境温度 (℃)
  humidity: number; // 环境湿度 (%RH)
}

// 饼图数据项类型
interface PieDataItem {
  value: number; // 数值
  name: string; // 名称
  color?: string; // 可选颜色
}

// 温湿度趋势数据类型
interface TempHumidityData {
  times: string[]; // 时间数组 ['00:00', '01:00', ...]
  temperatures: number[]; // 温度数组
  humidities: number[]; // 湿度数组
}

// 监控数据类型
interface MonitoringData {
  magneticAlarmCount: number; // 铁磁系统报警次数
  sensorAlarmCount: number; // 传感器报警次数
}

// 监控状态类型
interface LocationMonitoringStatus {
  id: string; // 唯一标识
  name: string; // 位置名称
  status: 'online' | 'offline' | 'partial'; // 状态
  type: 'MR' | 'DR' | 'CT' | 'DSA'; // 设备类型
  lastUpdate: string; // 最后更新时间
}

// 摄像头信息类型
interface CameraInfo {
  id: string; // 摄像头ID
  name: string; // 摄像头名称
  url: string; // 视频流URL
  status: boolean; // 在线状态
}

// 科室报警数据类型
interface DepartmentAlarmData {
  [viewType: string]: {
    departments: string[]; // 科室名称数组
    counts: number[]; // 对应报警次数
  };
}

// 滚动状态类型
interface ScrollState {
  enabled: boolean; // 是否启用滚动
  speed: number; // 滚动速度
  direction: 'up' | 'down'; // 滚动方向
}
```

## 📦 组件 API 详细规范

### 1. HeaderPanel API

```typescript
interface HeaderPanelProps {
  weather: WeatherInfo; // 天气信息
  serverTime: Date | string; // 服务器时间
  title?: string; // 页面标题
}

// 使用示例
const headerProps: HeaderPanelProps = {
  weather: {
    type: '晴',
    temperature: '17~28℃',
  },
  serverTime: new Date(),
  title: '云磁安全云平台',
};
```

### 2. EnvironmentDataCard API

```typescript
interface EnvironmentDataCardProps {
  environmentData: EnvironmentData; // 环境数据
  onCardClick?: () => void; // 点击回调
}

interface EnvironmentDataCardEmits {
  click: () => void; // 点击事件
}

// 使用示例
const environmentProps: EnvironmentDataCardProps = {
  environmentData: {
    airQuality: 19.4,
    temperature: 22.2,
    humidity: 41.3,
  },
};
```

### 3. AlarmAnalysisChart API

```typescript
interface AlarmAnalysisChartProps {
  alarmPieData: PieDataItem[]; // 饼图数据
  adaptSize: (size: number) => number; // 尺寸适配函数
}

// 使用示例
const chartProps: AlarmAnalysisChartProps = {
  alarmPieData: [
    { value: 335, name: '正常' },
    { value: 310, name: '警告' },
    { value: 234, name: '危险' },
  ],
  adaptSize: (size) => size * window.devicePixelRatio,
};
```

### 4. TempHumidityChart API

```typescript
interface TempHumidityChartProps {
  tempHumidityData: TempHumidityData; // 温湿度数据
  adaptSize: (size: number) => number; // 尺寸适配函数
}

// 使用示例
const tempHumidityProps: TempHumidityChartProps = {
  tempHumidityData: {
    times: ['00:00', '01:00', '02:00'],
    temperatures: [22.1, 22.3, 22.0],
    humidities: [41.2, 41.5, 41.1],
  },
  adaptSize: (size) => size * 0.8,
};
```

### 5. AlarmStatistics API

```typescript
interface AlarmStatisticsProps {
  monitoringData: MonitoringData; // 监控数据
}

// 使用示例
const statisticsProps: AlarmStatisticsProps = {
  monitoringData: {
    magneticAlarmCount: 5,
    sensorAlarmCount: 1,
  },
};
```

### 6. DepartmentAlarmChart API

```typescript
interface DepartmentAlarmChartProps {
  departmentData: DepartmentAlarmData; // 科室数据
  currentView: string; // 当前视窗
  adaptSize: (size: number) => number; // 尺寸适配函数
}

// 使用示例
const departmentProps: DepartmentAlarmChartProps = {
  departmentData: {
    MR: {
      departments: ['磁共振一室', '磁共振二室'],
      counts: [3, 2],
    },
  },
  currentView: 'MR',
  adaptSize: (size) => size,
};
```

### 7. MonitoringStatusList API

```typescript
interface MonitoringStatusListProps {
  locationMonitoringList: LocationMonitoringStatus[]; // 监控状态列表
  currentView: string; // 当前视窗
  needScroll: ScrollState; // 滚动状态
}

interface MonitoringStatusListEmits {
  'item-click': (item: LocationMonitoringStatus) => void; // 项目点击
  'overview-click': () => void; // 总览点击
}

// 使用示例
const monitoringProps: MonitoringStatusListProps = {
  locationMonitoringList: [
    {
      id: '1',
      name: '磁共振一室',
      status: 'online',
      type: 'MR',
      lastUpdate: '2025-06-23 23:50:00',
    },
  ],
  currentView: 'MR',
  needScroll: {
    enabled: true,
    speed: 50,
    direction: 'up',
  },
};
```

### 8. VideoMonitorPanel API

```typescript
interface VideoMonitorPanelProps {
  showMonitoringCard: boolean; // 是否显示监控卡片
  selectedCamera?: CameraInfo; // 选中的摄像头
}

interface VideoMonitorPanelEmits {
  'close': () => void; // 关闭事件
  'jump-to-external': () => void; // 跳转外部系统
}

// 使用示例
const videoProps: VideoMonitorPanelProps = {
  showMonitoringCard: true,
  selectedCamera: {
    id: 'cam001',
    name: '磁共振一室摄像头',
    url: 'http://example.com/stream.m3u8',
    status: true,
  },
};
```

### 9. NavigationFooter API

```typescript
interface NavigationFooterProps {
  currentView: string; // 当前视窗：'MR' | 'DR' | 'CT' | 'DSA'
}

interface NavigationFooterEmits {
  'switch-view': (viewType: string) => void; // 视窗切换
}

// 使用示例
const navigationProps: NavigationFooterProps = {
  currentView: 'MR',
};
```

## 🔄 数据流管理

### Props 数据流

```typescript
// 父组件 -> 子组件
interface DataFlow {
  // 1. 静态配置数据
  config: {
    title: string;
    theme: string;
  };

  // 2. 动态业务数据
  data: {
    environmentData: EnvironmentData;
    alarmData: PieDataItem[];
    monitoringList: LocationMonitoringStatus[];
  };

  // 3. 交互状态数据
  state: {
    currentView: string;
    showPanel: boolean;
    selectedItem: any;
  };

  // 4. 工具函数
  utils: {
    adaptSize: (size: number) => number;
    formatTime: (time: Date) => string;
  };
}
```

### Events 事件流

```typescript
// 子组件 -> 父组件
interface EventFlow {
  // 1. 用户交互事件
  userInteraction: {
    'item-click': (item: any) => void;
    'view-switch': (view: string) => void;
    'panel-close': () => void;
  };

  // 2. 状态变更事件
  stateChange: {
    'data-update': (data: any) => void;
    'error-occurred': (error: Error) => void;
  };

  // 3. 生命周期事件
  lifecycle: {
    'component-ready': () => void;
    'component-destroyed': () => void;
  };
}
```

## 🎨 样式变量

### CSS 自定义属性

```css
:root {
  /* 主色调 */
  --primary-color: #00c9f2;
  --secondary-color: #0d2d4c;

  /* 文字颜色 */
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);

  /* 背景颜色 */
  --bg-primary: rgba(13, 45, 76, 0.5);
  --bg-secondary: rgba(13, 45, 76, 0.15);

  /* 边框颜色 */
  --border-color: rgba(0, 201, 242, 0.1);

  /* 阴影效果 */
  --shadow-primary: 0 10px 30px rgba(0, 0, 0, 0.15);
  --shadow-glow: 0 0 14px rgba(173, 233, 255, 0.7);

  /* 动画时长 */
  --animation-duration: 0.3s;
  --transition-timing: ease-in-out;
}
```

### Less 变量

```less
// 尺寸变量
@header-height: 110px;
@card-border-radius: 10px;
@icon-size: 18px;

// 层级变量
@z-index-header: 100;
@z-index-modal: 1000;
@z-index-tooltip: 2000;

// 响应式断点
@breakpoint-sm: 768px;
@breakpoint-md: 1024px;
@breakpoint-lg: 1366px;
@breakpoint-xl: 1920px;
```

## 🔧 工具函数

### 尺寸适配函数

```typescript
/**
 * 屏幕尺寸适配函数
 * @param size 原始尺寸
 * @returns 适配后的尺寸
 */
function adaptSize(size: number): number {
  const baseWidth = 1920;
  const currentWidth = window.innerWidth;
  const scale = currentWidth / baseWidth;
  return Math.round(size * scale);
}
```

### 时间格式化函数

```typescript
/**
 * 时间格式化函数
 * @param time 时间对象或字符串
 * @returns 格式化后的时间字符串
 */
function formatTime(time: Date | string): string {
  const date = typeof time === 'string' ? new Date(time) : time;
  return date
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
    .replace(/\//g, ':');
}
```

---

**文档维护**: Chen Yu  
**最后更新**: 2025-06-23  
**版本**: v2.0
