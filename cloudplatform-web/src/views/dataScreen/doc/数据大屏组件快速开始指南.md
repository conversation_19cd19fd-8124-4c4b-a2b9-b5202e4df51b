# CloudPlatform 数据大屏组件快速开始指南

## 🚀 快速开始

本指南将帮助您快速上手 CloudPlatform 数据大屏组件的使用和开发。

## 📋 前置要求

### 环境要求

- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0 或 **pnpm**: >= 7.0.0
- **Vue**: 3.x
- **TypeScript**: >= 4.5.0

### 技术栈

- Vue 3 + Composition API
- TypeScript
- Arco Design Vue
- ECharts 5.x
- Less

## 🛠️ 安装与配置

### 1. 项目初始化

```bash
# 克隆项目
git clone <repository-url>
cd CloudPlatform-web

# 安装依赖
npm install
# 或
pnpm install
```

### 2. 开发环境启动

```bash
# 启动开发服务器
npm run dev
# 或
pnpm dev

# 访问地址
http://localhost:5173
```

### 3. 数据大屏页面访问

```bash
# 直接访问数据大屏
http://localhost:5173/#/dataScreen
```

## 📦 组件使用示例

### 基础使用模板

```vue
<template>
  <div class="data-screen">
    <!-- 页面头部 -->
    <HeaderPanel
      :weather="weatherData"
      :server-time="currentTime"
      title="云磁安全云平台"
    />

    <!-- 主要内容区 -->
    <main class="main-content">
      <!-- 左侧面板 -->
      <section class="left-panel">
        <EnvironmentDataCard
          :environment-data="environmentData"
          @click="handleEnvironmentClick"
        />
        <AlarmAnalysisChart
          :alarm-pie-data="alarmPieData"
          :adapt-size="adaptSize"
        />
        <TempHumidityChart
          :temp-humidity-data="tempHumidityData"
          :adapt-size="adaptSize"
        />
      </section>

      <!-- 右侧面板 -->
      <section class="right-panel">
        <AlarmStatistics :monitoring-data="monitoringData" />
        <DepartmentAlarmChart
          :department-data="departmentData"
          :current-view="currentView"
          :adapt-size="adaptSize"
        />
        <MonitoringStatusList
          :location-monitoring-list="locationMonitoringList"
          :current-view="currentView"
          :need-scroll="needScroll"
          @item-click="handleMonitoringItemClick"
          @overview-click="handleOverviewClick"
        />
      </section>

      <!-- 中央视频面板 -->
      <VideoMonitorPanel
        :show-monitoring-card="showMonitoringCard"
        :selected-camera="selectedCamera"
        @close="handleVideoClose"
        @jump-to-external="handleJumpToExternal"
      />
    </main>

    <!-- 底部导航 -->
    <NavigationFooter
      :current-view="currentView"
      @switch-view="handleViewSwitch"
    />
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted } from 'vue';

  // 导入组件
  import HeaderPanel from './components/HeaderPanel.vue';
  import EnvironmentDataCard from './components/EnvironmentDataCard.vue';
  import AlarmAnalysisChart from './components/AlarmAnalysisChart.vue';
  import TempHumidityChart from './components/TempHumidityChart.vue';
  import AlarmStatistics from './components/AlarmStatistics.vue';
  import DepartmentAlarmChart from './components/DepartmentAlarmChart.vue';
  import MonitoringStatusList from './components/MonitoringStatusList.vue';
  import VideoMonitorPanel from './components/VideoMonitorPanel.vue';
  import NavigationFooter from './components/NavigationFooter.vue';

  export default defineComponent({
    name: 'DataScreen',
    components: {
      HeaderPanel,
      EnvironmentDataCard,
      AlarmAnalysisChart,
      TempHumidityChart,
      AlarmStatistics,
      DepartmentAlarmChart,
      MonitoringStatusList,
      VideoMonitorPanel,
      NavigationFooter,
    },
    setup() {
      // 响应式数据
      const currentView = ref('MR');
      const showMonitoringCard = ref(false);
      const selectedCamera = ref(null);

      // 模拟数据
      const weatherData = ref({
        type: '晴',
        temperature: '17~28℃',
      });

      const environmentData = ref({
        airQuality: 19.4,
        temperature: 22.2,
        humidity: 41.3,
      });

      const alarmPieData = ref([
        { value: 335, name: '正常' },
        { value: 310, name: '警告' },
        { value: 234, name: '危险' },
      ]);

      // 事件处理函数
      const handleEnvironmentClick = () => {
        console.log('环境数据卡片被点击');
      };

      const handleMonitoringItemClick = (item: any) => {
        selectedCamera.value = item;
        showMonitoringCard.value = true;
      };

      const handleVideoClose = () => {
        showMonitoringCard.value = false;
        selectedCamera.value = null;
      };

      const handleViewSwitch = (viewType: string) => {
        currentView.value = viewType;
        // 切换视窗时更新相关数据
        fetchDataByView(viewType);
      };

      // 尺寸适配函数
      const adaptSize = (size: number) => {
        const baseWidth = 1920;
        const currentWidth = window.innerWidth;
        return Math.round(size * (currentWidth / baseWidth));
      };

      // 数据获取函数
      const fetchDataByView = async (viewType: string) => {
        try {
          // 根据视窗类型获取对应数据
          console.log(`获取${viewType}视窗数据`);
        } catch (error) {
          console.error('数据获取失败:', error);
        }
      };

      // 生命周期
      onMounted(() => {
        fetchDataByView(currentView.value);
      });

      return {
        currentView,
        showMonitoringCard,
        selectedCamera,
        weatherData,
        environmentData,
        alarmPieData,
        adaptSize,
        handleEnvironmentClick,
        handleMonitoringItemClick,
        handleVideoClose,
        handleViewSwitch,
      };
    },
  });
</script>

<style scoped lang="less">
  .data-screen {
    width: 100vw;
    height: 100vh;
    background: url('@/assets/images/dataScreen/bg.jpg') no-repeat center center;
    background-size: cover;
    overflow: hidden;
  }

  .main-content {
    display: flex;
    height: calc(100vh - 110px - 80px); // 减去头部和底部高度
    padding: 20px;
    gap: 20px;
  }

  .left-panel,
  .right-panel {
    flex: 0 0 300px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .center-panel {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
```

## 🎯 常用场景示例

### 1. 单独使用环境数据卡片

```vue
<template>
  <EnvironmentDataCard
    :environment-data="environmentData"
    @click="handleCardClick"
  />
</template>

<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import EnvironmentDataCard from '@/components/EnvironmentDataCard.vue';

  export default defineComponent({
    components: { EnvironmentDataCard },
    setup() {
      const environmentData = ref({
        airQuality: 19.4,
        temperature: 22.2,
        humidity: 41.3,
      });

      const handleCardClick = () => {
        console.log('卡片被点击');
      };

      return {
        environmentData,
        handleCardClick,
      };
    },
  });
</script>
```

### 2. 图表组件使用

```vue
<template>
  <div class="chart-container">
    <AlarmAnalysisChart :alarm-pie-data="chartData" :adapt-size="adaptSize" />
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import AlarmAnalysisChart from '@/components/AlarmAnalysisChart.vue';

  export default defineComponent({
    components: { AlarmAnalysisChart },
    setup() {
      const chartData = ref([
        { value: 335, name: '正常' },
        { value: 310, name: '警告' },
        { value: 234, name: '危险' },
      ]);

      const adaptSize = (size: number) => {
        return size * (window.innerWidth / 1920);
      };

      return {
        chartData,
        adaptSize,
      };
    },
  });
</script>

<style scoped>
  .chart-container {
    width: 400px;
    height: 300px;
  }
</style>
```

### 3. 监控列表组件使用

```vue
<template>
  <MonitoringStatusList
    :location-monitoring-list="monitoringList"
    :current-view="currentView"
    :need-scroll="scrollConfig"
    @item-click="handleItemClick"
    @overview-click="handleOverviewClick"
  />
</template>

<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import MonitoringStatusList from '@/components/MonitoringStatusList.vue';

  export default defineComponent({
    components: { MonitoringStatusList },
    setup() {
      const currentView = ref('MR');
      const monitoringList = ref([
        {
          id: '1',
          name: '磁共振一室',
          status: 'online',
          type: 'MR',
          lastUpdate: '2025-06-23 23:50:00',
        },
      ]);

      const scrollConfig = ref({
        enabled: true,
        speed: 50,
        direction: 'up',
      });

      const handleItemClick = (item: any) => {
        console.log('监控项被点击:', item);
      };

      const handleOverviewClick = () => {
        console.log('总览按钮被点击');
      };

      return {
        currentView,
        monitoringList,
        scrollConfig,
        handleItemClick,
        handleOverviewClick,
      };
    },
  });
</script>
```

## 🔧 自定义配置

### 主题定制

```less
// 自定义主题变量
:root {
  --primary-color: #00c9f2; // 主色调
  --secondary-color: #0d2d4c; // 次要色调
  --text-primary: #ffffff; // 主要文字颜色
  --bg-primary: rgba(13, 45, 76, 0.5); // 主要背景色
}
```

### 响应式配置

```typescript
// 自定义适配函数
const customAdaptSize = (size: number) => {
  const breakpoints = {
    sm: 768,
    md: 1024,
    lg: 1366,
    xl: 1920,
  };

  const currentWidth = window.innerWidth;
  let scale = 1;

  if (currentWidth <= breakpoints.sm) {
    scale = 0.6;
  } else if (currentWidth <= breakpoints.md) {
    scale = 0.8;
  } else if (currentWidth <= breakpoints.lg) {
    scale = 0.9;
  } else {
    scale = currentWidth / breakpoints.xl;
  }

  return Math.round(size * scale);
};
```

## 🚨 注意事项

### 1. 性能优化

- 大型图表组件建议使用懒加载
- 频繁更新的数据使用防抖处理
- 组件销毁时清理定时器和事件监听器

### 2. 数据格式

- 确保传入的数据格式符合组件要求
- 使用 TypeScript 类型检查避免类型错误
- 对异步数据进行空值检查

### 3. 事件处理

- 正确声明组件的 emits
- 避免在模板中直接调用复杂函数
- 使用防抖处理高频事件

## 📚 下一步

- 查看 [组件使用说明文档](./数据大屏组件使用说明文档.md) 了解详细功能
- 参考 [API 参考文档](./数据大屏组件API参考文档.md) 获取完整 API 信息
- 阅读 [重构文档](./数据大屏组件化重构文档.md) 了解架构设计

---

**文档维护**: Chen Yu  
**最后更新**: 2025-06-23
