# CloudPlatform 数据大屏组件使用说明文档

## 📋 文档概述

本文档详细介绍了 CloudPlatform 数据大屏页面的 9 个核心组件，包括组件功能、Props 参数、Events 事件、使用方法和最佳实践。

**文档版本**: v2.0  
**更新日期**: 2025-06-23  
**适用版本**: CloudPlatform v1.0+

## 🏗️ 组件架构概览

### 组件层次结构

```
DataScreen (主页面)
├── HeaderPanel (页面头部)
├── EnvironmentDataCard (环境数据卡片)
├── AlarmAnalysisChart (铁磁报警分析图表)
├── TempHumidityChart (温湿度趋势图表)
├── AlarmStatistics (报警统计)
├── DepartmentAlarmChart (科室报警情况图表)
├── MonitoringStatusList (监控实况列表)
├── VideoMonitorPanel (视频监控面板)
└── NavigationFooter (底部导航)
```

### 技术栈

- **框架**: Vue 3 + Composition API
- **UI 库**: Arco Design Vue
- **图表库**: ECharts 5.x
- **样式**: Less + CSS3
- **类型**: TypeScript

## 📦 组件详细说明

### 1. HeaderPanel - 页面头部组件

#### 功能描述

显示页面顶部信息，包括天气信息、平台标题和服务器时间。

#### Props 参数

| 参数名     | 类型         | 必填 | 默认值                                | 说明         |
| ---------- | ------------ | ---- | ------------------------------------- | ------------ |
| weather    | Object       | ✅   | `{type: '晴', temperature: '17~28℃'}` | 天气信息对象 |
| serverTime | Date\|String | ✅   | -                                     | 服务器时间   |
| title      | String       | ❌   | '云磁安全云平台'                      | 页面标题     |

#### 使用示例

```vue
<HeaderPanel
  :weather="weatherData"
  :server-time="currentTime"
  title="云磁安全云平台"
/>
```

#### 特性

- ✨ 太阳图标旋转动画
- 🎨 双层背景图片叠加效果
- ⏰ 实时时间显示 (YYYY:MM:DD HH:mm:ss)
- 📱 响应式设计

---

### 2. EnvironmentDataCard - 环境数据卡片组件

#### 功能描述

展示科室环境数据总览，包括氧气浓度、环境温度和湿度。

#### Props 参数

| 参数名          | 类型     | 必填 | 默认值                                         | 说明                 |
| --------------- | -------- | ---- | ---------------------------------------------- | -------------------- |
| environmentData | Object   | ✅   | `{airQuality: 0, temperature: 0, humidity: 0}` | 环境数据对象         |
| onCardClick     | Function | ❌   | null                                           | 卡片点击事件处理函数 |

#### Events 事件

| 事件名 | 参数 | 说明         |
| ------ | ---- | ------------ |
| click  | -    | 卡片点击事件 |

#### 使用示例

```vue
<EnvironmentDataCard
  :environment-data="environmentData"
  @click="handleCardClick"
/>
```

#### 特性

- 🌟 3D 氧气组件动画效果
- 🎬 粒子特效视频播放
- 💧 渐变背景和发光文字效果
- 🖱️ 静态悬浮状态（已修复动画问题）

---

### 3. AlarmAnalysisChart - 铁磁报警分析图表组件

#### 功能描述

使用 ECharts 饼图展示铁磁报警数据分析。

#### Props 参数

| 参数名       | 类型     | 必填 | 默认值 | 说明         |
| ------------ | -------- | ---- | ------ | ------------ |
| alarmPieData | Array    | ✅   | []     | 饼图数据数组 |
| adaptSize    | Function | ✅   | -      | 屏幕适配函数 |

#### 数据格式

```typescript
interface PieDataItem {
  value: number; // 数值
  name: string; // 名称
}
```

#### 使用示例

```vue
<AlarmAnalysisChart :alarm-pie-data="alarmPieData" :adapt-size="adaptSize" />
```

#### 特性

- 📊 ECharts 饼图渲染
- 🎯 自动高亮动画（3 秒循环）
- 🔄 响应式尺寸适配
- 🎨 自定义颜色主题
- 👆 鼠标交互暂停自动高亮

---

### 4. TempHumidityChart - 温湿度趋势图表组件

#### 功能描述

显示 24 小时温湿度变化趋势的折线图。

#### Props 参数

| 参数名           | 类型     | 必填 | 默认值 | 说明           |
| ---------------- | -------- | ---- | ------ | -------------- |
| tempHumidityData | Object   | ✅   | -      | 温湿度数据对象 |
| adaptSize        | Function | ✅   | -      | 屏幕适配函数   |

#### 数据格式

```typescript
interface TempHumidityData {
  times: string[]; // 时间数组
  temperatures: number[]; // 温度数组
  humidities: number[]; // 湿度数组
}
```

#### 使用示例

```vue
<TempHumidityChart
  :temp-humidity-data="tempHumidityData"
  :adapt-size="adaptSize"
/>
```

#### 特性

- 📈 双 Y 轴折线图
- 🌊 流动动画效果
- 📱 自动滚动显示
- 🎨 渐变填充区域
- 🔄 实时数据更新

---

### 5. AlarmStatistics - 报警统计组件

#### 功能描述

展示铁磁系统和传感器报警统计数据。

#### Props 参数

| 参数名         | 类型   | 必填 | 默认值 | 说明         |
| -------------- | ------ | ---- | ------ | ------------ |
| monitoringData | Object | ✅   | -      | 监控数据对象 |

#### 数据格式

```typescript
interface MonitoringData {
  magneticAlarmCount: number; // 铁磁系统报警次数
  sensorAlarmCount: number; // 传感器报警次数
}
```

#### 使用示例

```vue
<AlarmStatistics :monitoring-data="monitoringData" />
```

#### 特性

- 🔢 数字统计显示
- 🎨 图标化展示
- 💫 发光文字效果
- 📊 实时数据更新

---

### 6. DepartmentAlarmChart - 科室报警情况图表组件

#### 功能描述

显示各科室报警情况的柱状图，支持多视窗切换。

#### Props 参数

| 参数名         | 类型     | 必填 | 默认值 | 说明         |
| -------------- | -------- | ---- | ------ | ------------ |
| departmentData | Object   | ✅   | -      | 科室数据对象 |
| currentView    | String   | ✅   | -      | 当前视窗     |
| adaptSize      | Function | ✅   | -      | 屏幕适配函数 |

#### 使用示例

```vue
<DepartmentAlarmChart
  :department-data="departmentData"
  :current-view="currentView"
  :adapt-size="adaptSize"
/>
```

#### 特性

- 📊 ECharts 柱状图
- 🔄 自动滚动显示
- 🎨 条纹纹理效果
- 🖼️ 多视窗数据切换

---

### 7. MonitoringStatusList - 监控实况列表组件

#### 功能描述

显示监控设备状态列表，支持无缝滚动和交互。

#### Props 参数

| 参数名                 | 类型   | 必填 | 默认值 | 说明         |
| ---------------------- | ------ | ---- | ------ | ------------ |
| locationMonitoringList | Array  | ✅   | []     | 监控状态列表 |
| currentView            | String | ✅   | -      | 当前视窗     |
| needScroll             | Object | ✅   | -      | 滚动状态对象 |

#### Events 事件

| 事件名         | 参数 | 说明             |
| -------------- | ---- | ---------------- |
| item-click     | item | 监控项点击事件   |
| overview-click | -    | 总览按钮点击事件 |

#### 使用示例

```vue
<MonitoringStatusList
  :location-monitoring-list="locationMonitoringList"
  :current-view="currentView"
  :need-scroll="needScroll"
  @item-click="handleItemClick"
  @overview-click="handleOverviewClick"
/>
```

#### 特性

- 🔄 无缝滚动动画
- 🚦 状态指示器（在线/离线/部分在线）
- 🖱️ 点击交互
- 📱 响应式设计

---

### 8. VideoMonitorPanel - 视频监控面板组件

#### 功能描述

显示视频监控画面和控制面板。

#### Props 参数

| 参数名             | 类型    | 必填 | 默认值 | 说明             |
| ------------------ | ------- | ---- | ------ | ---------------- |
| showMonitoringCard | Boolean | ✅   | false  | 是否显示监控卡片 |
| selectedCamera     | Object  | ❌   | null   | 选中的摄像头对象 |

#### Events 事件

| 事件名           | 参数 | 说明             |
| ---------------- | ---- | ---------------- |
| close            | -    | 关闭事件         |
| jump-to-external | -    | 跳转外部系统事件 |

#### 使用示例

```vue
<VideoMonitorPanel
  :show-monitoring-card="showMonitoringCard"
  :selected-camera="selectedCamera"
  @close="handleClose"
  @jump-to-external="handleJumpToExternal"
/>
```

#### 特性

- 🎥 多视频源支持
- 🔗 外部系统跳转
- 🎛️ 控制面板
- 📱 响应式布局

---

### 9. NavigationFooter - 底部导航组件

#### 功能描述

提供视窗切换导航功能。

#### Props 参数

| 参数名      | 类型   | 必填 | 默认值 | 说明     |
| ----------- | ------ | ---- | ------ | -------- |
| currentView | String | ✅   | -      | 当前视窗 |

#### Events 事件

| 事件名      | 参数     | 说明         |
| ----------- | -------- | ------------ |
| switch-view | viewType | 视窗切换事件 |

#### 使用示例

```vue
<NavigationFooter :current-view="currentView" @switch-view="handleSwitchView" />
```

#### 特性

- 🔄 视窗切换动画
- 🎯 活动状态指示
- 🎨 图标化导航
- 📱 响应式设计

## 🎯 最佳实践

### 1. 组件使用原则

- **单一职责**: 每个组件只负责特定功能
- **数据驱动**: 通过 Props 传递数据，避免直接操作 DOM
- **事件通信**: 使用 Events 进行组件间通信
- **响应式设计**: 使用 adaptSize 函数确保屏幕适配

### 2. 性能优化

- **懒加载**: 大型图表组件支持懒加载
- **防抖节流**: 频繁更新的数据使用防抖处理
- **内存管理**: 组件销毁时清理定时器和事件监听器

### 3. 错误处理

- **数据验证**: 对传入的 Props 进行类型和格式验证
- **异常捕获**: 使用 try-catch 处理可能的运行时错误
- **降级方案**: 提供数据加载失败时的默认显示

## 🔧 开发指南

### 组件开发规范

#### 1. 文件命名

- 组件文件使用 PascalCase 命名：`ComponentName.vue`
- 组件名称与文件名保持一致
- 存放在 `src/views/dataScreen/components/` 目录下

#### 2. 组件结构

```vue
<script lang="ts">
  import { defineComponent } from 'vue';

  export default defineComponent({
    name: 'ComponentName',
    components: {},
    props: {
      // Props定义
    },
    emits: ['event-name'],
    setup(props, { emit }) {
      // 组件逻辑
      return {};
    },
  });
</script>

<template>
  <!-- 模板内容 -->
</template>

<style scoped lang="less">
  /* 样式定义 */
</style>
```

#### 3. Props 定义规范

```typescript
props: {
  /**
   * 属性描述
   */
  propName: {
    type: [String, Number, Object, Array],
    required: true, // 或 false
    default: () => ({}), // 对象/数组使用函数返回
    validator: (value) => {
      // 自定义验证逻辑
      return true;
    }
  }
}
```

### 样式开发规范

#### 1. CSS 类命名

- 使用 BEM 命名规范
- 组件根元素使用组件名作为类名
- 子元素使用 `__` 连接
- 修饰符使用 `--` 连接

```less
.component-name {
  &__element {
    // 元素样式
  }

  &--modifier {
    // 修饰符样式
  }
}
```

#### 2. 响应式设计

```less
// 使用adaptSize函数进行尺寸适配
.chart-container {
  width: adaptSize(400px);
  height: adaptSize(300px);
}

// 媒体查询
@media (max-width: 1366px) {
  .component {
    font-size: 12px;
  }
}
```

## 🧪 测试指南

### 单元测试

```typescript
import { mount } from '@vue/test-utils';
import ComponentName from '@/components/ComponentName.vue';

describe('ComponentName', () => {
  it('should render correctly', () => {
    const wrapper = mount(ComponentName, {
      props: {
        // 测试props
      },
    });

    expect(wrapper.exists()).toBe(true);
  });

  it('should emit events correctly', async () => {
    const wrapper = mount(ComponentName);

    await wrapper.find('.clickable-element').trigger('click');

    expect(wrapper.emitted('event-name')).toBeTruthy();
  });
});
```

### E2E 测试

```typescript
// Playwright测试示例
test('数据大屏组件交互测试', async ({ page }) => {
  await page.goto('/dataScreen');

  // 测试组件渲染
  await expect(page.locator('.environment-card')).toBeVisible();

  // 测试交互功能
  await page.locator('.environment-card').hover();
  await page.locator('.environment-card').click();

  // 验证结果
  await expect(page.locator('.video-monitor-panel')).toBeVisible();
});
```

## 🚀 部署指南

### 构建配置

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          echarts: ['echarts'],
          arco: ['@arco-design/web-vue'],
        },
      },
    },
  },
});
```

### 环境变量

```bash
# .env.production
VITE_API_BASE_URL=https://api.cloudplatform.com
VITE_ENABLE_MOCK=false
```

## 🔍 故障排除

### 常见问题

#### 1. 图表不显示

**问题**: ECharts 图表组件渲染空白
**解决方案**:

- 检查容器元素是否有明确的宽高
- 确认数据格式是否正确
- 验证 adaptSize 函数是否正常工作

#### 2. 组件样式错误

**问题**: 组件样式显示异常
**解决方案**:

- 检查 CSS 作用域是否正确
- 确认 Less 编译是否成功
- 验证响应式断点设置

#### 3. 事件不触发

**问题**: 组件事件监听失效
**解决方案**:

- 检查事件名称是否正确
- 确认 emits 声明是否完整
- 验证事件处理函数绑定

### 调试技巧

#### 1. Vue DevTools

- 使用 Vue DevTools 查看组件状态
- 监控 Props 和 Events 传递
- 检查组件生命周期

#### 2. 控制台调试

```typescript
// 在组件中添加调试信息
setup(props) {
  console.log('Component props:', props);

  watch(() => props.data, (newVal) => {
    console.log('Data changed:', newVal);
  });
}
```

## 📚 相关文档

- [数据大屏组件化重构文档](./数据大屏组件化重构文档.md)
- [重构验证清单](./重构验证清单.md)
- [Playwright 自动化测试报告](./Playwright自动化测试报告.md)
- [EnvironmentDataCard 悬浮行为修复报告](./EnvironmentDataCard悬浮行为修复报告.md)

## 📞 技术支持

如有问题或建议，请联系：

- **技术负责人**: Chen Yu
- **文档维护**: 开发团队
- **更新频率**: 随版本发布更新

---

**文档版本**: v2.0
**文档维护**: Chen Yu
**最后更新**: 2025-06-23
**下次更新**: 随功能迭代
