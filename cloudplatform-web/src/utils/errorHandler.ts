/**
 * 统一错误处理工具类
 * 提供前端应用的错误处理、日志记录和用户提示功能
 */

import { Message, Modal } from '@arco-design/web-vue';
import type { AxiosError } from 'axios';
import { useUserStore } from '@/store';

export interface ErrorInfo {
  code?: string | number;
  message: string;
  details?: any;
  timestamp: number;
  url?: string;
}

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTH = 'AUTH',
  BUSINESS = 'BUSINESS',
  VALIDATION = 'VALIDATION',
  UNKNOWN = 'UNKNOWN',
}

/**
 * 认证相关错误码
 */
export const AUTH_ERROR_CODES = [401, 403, 50008, 50012, 50014];

/**
 * 统一错误处理类
 */
export class ErrorHandler {
  /**
   * 处理 API 请求错误
   */
  static handleApiError(error: AxiosError): void {
    const errorInfo = this.parseError(error);

    // 记录错误日志
    this.logError(errorInfo);

    // 根据错误类型处理
    switch (this.getErrorType(errorInfo)) {
      case ErrorType.AUTH:
        this.handleAuthError(errorInfo);
        break;
      case ErrorType.NETWORK:
        this.handleNetworkError(errorInfo);
        break;
      case ErrorType.BUSINESS:
        this.handleBusinessError(errorInfo);
        break;
      default:
        this.handleUnknownError(errorInfo);
    }
  }

  /**
   * 解析错误信息
   */
  private static parseError(error: AxiosError): ErrorInfo {
    const response = error.response;
    const request = error.request;

    return {
      code: response?.status || error.code,
      message: this.extractErrorMessage(error),
      details: response?.data,
      timestamp: Date.now(),
      url: error.config?.url,
    };
  }

  /**
   * 提取错误消息
   */
  private static extractErrorMessage(error: AxiosError): string {
    const response = error.response;

    // 优先使用后端返回的错误消息
    if (response?.data) {
      const data = response.data as any;
      if (data.message) return data.message;
      if (data.error) return data.error;
      if (data.msg) return data.msg;
    }

    // 使用 HTTP 状态码对应的消息
    if (response?.status) {
      const statusMessages: Record<number, string> = {
        400: '请求参数错误',
        401: '未授权，请重新登录',
        403: '拒绝访问',
        404: '请求的资源不存在',
        408: '请求超时',
        500: '服务器内部错误',
        502: '网关错误',
        503: '服务不可用',
        504: '网关超时',
      };

      return statusMessages[response.status] || `请求失败 (${response.status})`;
    }

    // 网络错误
    if (error.code === 'NETWORK_ERROR') {
      return '网络连接失败，请检查网络设置';
    }

    if (error.code === 'TIMEOUT') {
      return '请求超时，请稍后重试';
    }

    return error.message || '未知错误';
  }

  /**
   * 判断错误类型
   */
  private static getErrorType(errorInfo: ErrorInfo): ErrorType {
    const code = errorInfo.code;

    if (typeof code === 'number' && AUTH_ERROR_CODES.includes(code)) {
      return ErrorType.AUTH;
    }

    if (code === 'NETWORK_ERROR' || code === 'TIMEOUT') {
      return ErrorType.NETWORK;
    }

    if (typeof code === 'number' && code >= 400 && code < 500) {
      return ErrorType.VALIDATION;
    }

    if (typeof code === 'number' && code >= 500) {
      return ErrorType.BUSINESS;
    }

    return ErrorType.UNKNOWN;
  }

  /**
   * 处理认证错误
   */
  private static handleAuthError(errorInfo: ErrorInfo): void {
    Modal.error({
      title: '认证失败',
      content: '您的登录状态已过期，请重新登录',
      okText: '重新登录',
      async onOk() {
        const userStore = useUserStore();
        await userStore.logout();
        window.location.reload();
      },
    });
  }

  /**
   * 处理网络错误
   */
  private static handleNetworkError(errorInfo: ErrorInfo): void {
    Message.error({
      content: errorInfo.message,
      duration: 5000,
    });
  }

  /**
   * 处理业务错误
   */
  private static handleBusinessError(errorInfo: ErrorInfo): void {
    Message.error({
      content: errorInfo.message,
      duration: 3000,
    });
  }

  /**
   * 处理未知错误
   */
  private static handleUnknownError(errorInfo: ErrorInfo): void {
    Message.error({
      content: '系统异常，请稍后重试',
      duration: 3000,
    });
  }

  /**
   * 记录错误日志
   */
  private static logError(errorInfo: ErrorInfo): void {
    console.error('API Error:', {
      ...errorInfo,
      userAgent: navigator.userAgent,
      timestamp: new Date(errorInfo.timestamp).toISOString(),
    });

    // 在生产环境中，可以将错误发送到监控服务
    if (import.meta.env.PROD && import.meta.env.VITE_ENABLE_ERROR_MONITORING) {
      this.reportError(errorInfo);
    }
  }

  /**
   * 上报错误到监控服务
   */
  private static reportError(errorInfo: ErrorInfo): void {
    // 这里可以集成第三方错误监控服务
    // 例如：Sentry, LogRocket, Bugsnag 等
    try {
      // 示例：发送到自定义错误收集接口
      fetch('/api/errors/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorInfo),
      }).catch(() => {
        // 忽略错误上报失败
      });
    } catch (error) {
      // 忽略错误上报异常
    }
  }
}

/**
 * 全局错误处理函数
 */
export function setupGlobalErrorHandler(): void {
  // 处理未捕获的 Promise 错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled Promise Rejection:', event.reason);

    // 阻止默认的错误处理
    event.preventDefault();

    // 显示用户友好的错误消息
    Message.error({
      content: '系统出现异常，请刷新页面重试',
      duration: 5000,
    });
  });

  // 处理全局 JavaScript 错误
  window.addEventListener('error', (event) => {
    console.error('Global Error:', event.error);

    Message.error({
      content: '页面出现错误，请刷新页面重试',
      duration: 5000,
    });
  });
}
