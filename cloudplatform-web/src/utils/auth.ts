const TOKEN_KEY = 'token';

const isLogin = () => {
  // 注释原始逻辑，始终返回 true，表示已登录
  // return !!localStorage.getItem(TOKEN_KEY);
  return true;
};

const getToken = () => {
  return localStorage.getItem(TOKEN_KEY);
};

const setToken = (token: string) => {
  localStorage.setItem(TOKEN_KEY, token);
};

const clearToken = () => {
  localStorage.removeItem(TOKEN_KEY);
};

export { isLogin, getToken, setToken, clearToken };
