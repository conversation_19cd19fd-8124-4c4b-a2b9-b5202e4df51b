import { ref, onMounted, onUnmounted } from 'vue';
import { getCurrentTime } from '../api/system'; // 假设 getCurrentTime 接口函数已在 ../api/system 中定义
// 假设服务器返回的时间数据结构是 { time: string }，并且字符串可以被 new Date() 解析
// 如果 CurrentTimeDTO 结构不同，需要调整这里的类型和解析逻辑
import type { ApiResponse } from '@/types/api';
import type { CurrentTimeDTO } from '@/types/system'; // 假设 CurrentTimeDTO 定义在 @/types/system 中

/**
 * 使用服务器实时时间的 Composition API Hook
 * 定时从服务器获取最新时间，并在两次获取之间模拟走时。
 *
 * @returns 一个响应式的 Date 对象，表示当前的模拟时间。
 */
export function useServerTime() {
  // 存储当前的模拟时间，初始为 null 或一个默认值
  const currentTime = ref<Date | null>(null);

  // 存储上次从服务器获取的时间点和获取时对应的本地时间戳
  let lastServerTime: Date | null = null;
  let lastFetchTimestamp: number | null = null;

  // 定时器ID
  let fetchIntervalId: number | null = null; // 用于定时获取服务器时间
  let simulationIntervalId: number | null = null; // 用于模拟时间走时

  /**
   * 从服务器获取最新时间并更新状态
   */
  const fetchServerTime = async () => {
    try {
      const response = await getCurrentTime();
      if (
        response.data &&
        response.data.statusCode === 200 &&
        response.data.data
      ) {
        // 假设 CurrentTimeDTO 中有一个 time 字段，类型为字符串，且可被 Date 构造函数解析
        // 例如：{ time: "2023-10-27T10:00:00Z" } 或 { time: "2023/10/27 10:00:00" }
        // 请根据实际 CurrentTimeDTO 结构调整 timeString 的获取方式
        const serverTimeStr = (response.data.data as CurrentTimeDTO)
          .currentTime;
        const fetchedTime = new Date(serverTimeStr);

        if (!isNaN(fetchedTime.getTime())) {
          lastServerTime = fetchedTime;
          lastFetchTimestamp = Date.now();

          // 清除旧的模拟定时器
          if (simulationIntervalId !== null) {
            clearInterval(simulationIntervalId);
          }

          // 设置当前时间为获取到的服务器时间
          currentTime.value = new Date(fetchedTime);

          // 启动新的模拟定时器，每秒更新一次显示时间
          simulationIntervalId = setInterval(() => {
            if (lastServerTime && lastFetchTimestamp !== null) {
              const elapsed = Date.now() - lastFetchTimestamp;
              currentTime.value = new Date(lastServerTime.getTime() + elapsed);
            }
          }, 1000); // 每秒更新一次模拟时间
        } else {
          console.error('获取到的服务器时间格式无效:', serverTimeStr);
        }
      } else {
        console.error('获取服务器时间接口返回异常:', response.data);
      }
    } catch (error) {
      console.error('获取服务器时间接口调用失败:', error);
      // 可以在这里增加错误重试逻辑或使用上次成功获取的时间继续模拟
    }
  };

  // 在组件挂载后立即获取时间并启动定时器
  onMounted(() => {
    fetchServerTime(); // 立即获取一次
    // 每隔 10 秒获取一次服务器时间（10000毫秒）
    fetchIntervalId = setInterval(fetchServerTime, 10000);
  });

  // 在组件卸载前清除所有定时器，避免内存泄漏
  onUnmounted(() => {
    if (fetchIntervalId !== null) {
      clearInterval(fetchIntervalId);
    }
    if (simulationIntervalId !== null) {
      clearInterval(simulationIntervalId);
    }
  });

  // 返回响应式的当前时间
  return currentTime;
}
