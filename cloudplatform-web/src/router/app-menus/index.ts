import { appRoutes } from '../routes';
// import { appRoutes, appExternalRoutes } from '../routes';

// const mixinRoutes = [...appRoutes, ...appExternalRoutes];
const mixinRoutes = [...appRoutes];

const appClientMenus = mixinRoutes.map((el) => {
  const { name, path, meta, redirect, children } = el;
  return {
    name,
    path,
    meta,
    redirect,
    children,
  };
});

export default appClientMenus;
