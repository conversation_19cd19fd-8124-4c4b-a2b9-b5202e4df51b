<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="15px" height="12px" viewBox="0 0 15 12" version="1.1">
    <title>编组 8</title>
    <defs>
        <polygon id="path-1" points="5.32907052e-15 3 2 3 2 5 5.32907052e-15 5"/>
        <filter x="-300.0%" y="-300.0%" width="700.0%" height="700.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
            <feColorMatrix values="0 0 0 0 0.394721799   0 0 0 0 0.779245741   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"/>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-8" transform="translate(4.000000, 1.000000)">
            <g id="编组-5备份-4">
                <polygon id="矩形" fill-opacity="0.6" fill="#49A099" points="1 3 8.19973702 3 11 7.18263119 8.19973702 11 1 11"/>
                <polygon id="矩形备份-7" fill="#71DDFF" points="0 0 7.19973702 0 10 4.18263119 7.19973702 8 0 8"/>
            </g>
            <g id="矩形备份-63">
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"/>
                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"/>
            </g>
        </g>
    </g>
</svg>