<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="92px" height="91px" viewBox="0 0 92 91" version="1.1">
    <title>安防监控</title>
    <defs>
        <filter x="-33.3%" y="-33.3%" width="167.7%" height="167.1%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="0" dy="4" in="SourceAlpha" result="shadowOffsetOuter1"/>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
        <radialGradient cx="68.0201775%" cy="6.06915427%" fx="68.0201775%" fy="6.06915427%" r="65.0158965%" id="radialGradient-2">
            <stop stop-color="#71C5FF" stop-opacity="0.5" offset="0%"/>
            <stop stop-color="#88D5FE" stop-opacity="0" offset="100%"/>
        </radialGradient>
        <radialGradient cx="50%" cy="100%" fx="50%" fy="100%" r="36.8307297%" gradientTransform="translate(0.500000,1.000000),rotate(90.000000),scale(1.000000,1.875164),translate(-0.500000,-1.000000)" id="radialGradient-3">
            <stop stop-color="#DDF1FF" stop-opacity="0.5" offset="0%"/>
            <stop stop-color="#5CB5FF" stop-opacity="0" offset="100%"/>
        </radialGradient>
        <linearGradient x1="16.0029794%" y1="16.4561081%" x2="69.1312514%" y2="93.6817786%" id="linearGradient-4">
            <stop stop-color="#4DBAFF" offset="0%"/>
            <stop stop-color="#C5DFEF" offset="45.208044%"/>
            <stop stop-color="#8ACDF0" offset="100%"/>
        </linearGradient>
        <circle id="path-5" cx="30.4703438" cy="30.1339286" r="30.1339286"/>
        <linearGradient x1="50%" y1="38.0010738%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#FFFFFF" offset="0%"/>
            <stop stop-color="#9ED3FF" offset="100%"/>
        </linearGradient>
    </defs>
    <g id="页面-2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="智慧医院数字孪生备份" transform="translate(-1331.000000, -949.000000)">
            <g id="安防监控" filter="url(#filter-1)" transform="translate(1346.692990, 964.000000)">
                <g id="椭圆形">
                    <use fill-opacity="0.3" fill="#3D9FF4" xlink:href="#path-5"/>
                    <use fill="url(#radialGradient-2)" xlink:href="#path-5"/>
                    <circle stroke="url(#linearGradient-4)" stroke-width="2" stroke-linejoin="square" fill="url(#radialGradient-3)" cx="30.4703438" cy="30.1339286" r="29.1339286"/>
                </g>
                <circle id="椭圆形" fill-opacity="0.2" fill="#EDF7FF" cx="30.4703438" cy="30.1339286" r="24.6550325"/>
                <path d="M22.1357319,33.5608152 L16.9297236,28.4521308 C16.5558923,28.085696 16.3458411,27.5884588 16.3458411,27.0699532 C16.3458411,26.5514476 16.5558923,26.0542105 16.9297236,25.6877757 L26.892809,15.912899 C27.670226,15.1502458 28.930419,15.1502458 29.707836,15.912899 L42.6312787,28.5927905 C43.408461,29.3556741 43.408461,30.5923084 42.6312787,31.355192 L40.8355543,33.118323 L45.6812231,34.7193046 C46.0077342,34.8271399 46.2542114,35.0929828 46.332846,35.4221252 C46.4114805,35.7512676 46.3110916,36.09691 46.0674433,36.3359151 L37.6044448,44.640702 C37.3608864,44.8797954 37.0086594,44.9783076 36.6732466,44.9011431 C36.3378337,44.8239786 36.0669264,44.5821091 35.9570368,44.2617021 L34.32456,39.5066206 L32.6681934,41.1320223 C31.8907763,41.8946756 30.6305833,41.8946756 29.8531663,41.1320223 L25.0911121,36.4599692 L23.4785436,39.1813449 C23.3846093,39.3401505 23.268329,39.4851678 23.1331354,39.6121154 C22.9574384,39.7843492 22.7468443,39.91846 22.5149841,40.0057674 C22.1604282,40.149106 21.7712847,40.1891242 21.3941494,40.1210303 L18.5602096,40.1210303 L18.5602096,42.3784241 C18.5360196,43.4395506 17.6524895,44.2873851 16.5708773,44.2873851 C15.4892652,44.2873851 14.6057351,43.4395506 14.5815451,42.3784241 L14.5815451,34.7495856 C14.6057351,33.6884591 15.4892652,32.8406246 16.5708773,32.8406246 C17.6524895,32.8406246 18.5360196,33.6884591 18.5602096,34.7495856 L18.5602096,36.6416547 L20.3111405,36.6416547 L22.1377227,33.5608152 L22.1357319,33.5608152 Z M35.0920232,30.5620272 C35.4684646,30.934603 35.9803257,31.1451542 36.5149799,31.1473699 C37.0496342,31.1495508 37.5632753,30.943216 37.9428851,30.5737489 C38.3196535,30.2014942 38.5302762,29.6976011 38.5284217,29.1729424 C38.5265426,28.6482837 38.3123392,28.1458463 37.932931,27.7761825 C37.5563175,27.4034384 37.0441694,27.1928734 36.5092714,27.1908435 C35.9743735,27.1888428 35.4605928,27.3955424 35.0810737,27.7654376 C34.7044366,28.1378215 34.4939925,28.6417875 34.4960311,29.1664464 C34.4980995,29.6911053 34.7124823,30.1934701 35.0920232,30.563004 L35.0920232,30.5620272 Z" id="形状" fill="url(#linearGradient-6)" fill-rule="nonzero"/>
            </g>
        </g>
    </g>
</svg>