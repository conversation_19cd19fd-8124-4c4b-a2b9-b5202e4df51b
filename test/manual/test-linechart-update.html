<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LineChart 组件更新测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #0a1628;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #00d4ff;
            margin-bottom: 30px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: rgba(13, 45, 76, 0.5);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(0, 201, 242, 0.2);
        }
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            color: #00d4ff;
        }
        .feature-list {
            background-color: rgba(13, 45, 76, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        .feature-list h2 {
            color: #00d4ff;
            margin-bottom: 15px;
        }
        .feature-list ul {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(0, 201, 242, 0.1);
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list .new {
            color: #4d85ff;
            font-weight: bold;
        }
        .demo-image {
            width: 100%;
            border-radius: 8px;
            border: 2px solid rgba(0, 201, 242, 0.3);
        }
        .code-block {
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            overflow-x: auto;
        }
        .code-block pre {
            margin: 0;
            color: #00feff;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>LineChart 组件样式更新对比</h1>

        <div class="comparison">
            <div class="chart-container">
                <div class="chart-title">原 LineChart 样式</div>
                <p style="text-align: center; color: #999;">
                    基础折线图样式，包含简单的渐变和流动效果
                </p>
            </div>
            <div class="chart-container">
                <div class="chart-title">新 LineChart 样式（与 TempHumidityChart 一致）</div>
                <p style="text-align: center; color: #999;">
                    完全复制 TempHumidityChart 的样式，包含所有动画效果
                </p>
            </div>
        </div>

        <div class="feature-list">
            <h2>新增特性</h2>
            <ul>
                <li><span class="new">✓</span> 流动线条动画效果：3条流动线叠加在基础线条上，动画更流畅</li>
                <li><span class="new">✓</span> 自动滚动动画：通过 dataZoom 实现数据自动滚动展示</li>
                <li><span class="new">✓</span> 双Y轴配置：默认左轴-20到60（温度），右轴0到100（湿度/氧气）</li>
                <li><span class="new">✓</span> 默认颜色方案：温度#4d85ff，湿度#00feff，氧气#ffa500</li>
                <li><span class="new">✓</span> 统一的网格样式：与 TempHumidityChart 完全一致</li>
                <li><span class="new">✓</span> 改进的tooltip样式：背景、边框、文字颜色统一</li>
                <li><span class="new">✓</span> 图例样式优化：居中显示，圆角矩形图标</li>
                <li><span class="new">✓</span> 渐变区域填充：每条线下方都有渐变填充效果</li>
                <li><span class="new">✓</span> 自适应尺寸：完整实现 adaptSize 功能</li>
                <li><span class="new">✓</span> 可配置的Y轴范围：通过 props 可以自定义Y轴配置</li>
            </ul>
        </div>

        <div class="feature-list">
            <h2>新增 Props</h2>
            <ul>
                <li><strong>enableAutoScroll</strong>: Boolean (默认: true) - 是否启用自动滚动</li>
                <li><strong>scrollInterval</strong>: Number (默认: 4000) - 滚动间隔时间（毫秒）</li>
                <li><strong>leftYAxis</strong>: Object - 左侧Y轴配置 { min, max, interval, name }</li>
                <li><strong>rightYAxis</strong>: Object - 右侧Y轴配置 { min, max, interval, name }</li>
            </ul>
        </div>

        <div class="code-block">
            <h2 style="color: #00d4ff; margin-bottom: 15px;">使用示例</h2>
            <pre>&lt;LineChart 
  :data="lineChartData" 
  :height="'190px'"
  :adapt-size="adaptSize"
  :enable-auto-scroll="true"
  :scroll-interval="4000"
  :left-y-axis="{ min: -20, max: 60, interval: 20 }"
  :right-y-axis="{ min: 0, max: 100, interval: 20 }"
/&gt;

// 数据格式
const lineChartData = {
  xAxisData: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
  series: [
    {
      name: '环境温度',
      data: [22, 21, 23, 25, 26, 24, 22],
      unit: '℃',
      color: '#4d85ff',
      yAxisIndex: 0,  // 使用左侧Y轴
    },
    {
      name: '环境湿度',
      data: [65, 68, 70, 72, 69, 67, 66],
      unit: '%RH',
      color: '#00feff',
      yAxisIndex: 1,  // 使用右侧Y轴
    },
    {
      name: '氧气浓度',
      data: [20.9, 20.8, 20.7, 20.8, 20.9, 20.8, 20.9],
      unit: '%',
      color: '#ffa500',
      yAxisIndex: 1,  // 使用右侧Y轴
    },
  ],
};</pre>
        </div>

        <div class="feature-list">
            <h2>兼容性说明</h2>
            <ul>
                <li>✓ 完全向后兼容：现有使用 LineChart 的组件无需修改</li>
                <li>✓ 新增的 props 都有默认值，不会影响现有功能</li>
                <li>✓ 保持了组件的通用性，可以通过配置适应不同场景</li>
                <li>✓ VideoMonitorPanel.vue 中的使用已经兼容新版本</li>
            </ul>
        </div>
    </div>
</body>
</html>