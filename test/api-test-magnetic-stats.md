# 磁力报警统计接口测试说明

## 接口修改说明
已为 `/api/magnetic/alarm/stats` 接口添加了可选的 `locationIds` 参数，支持按点位过滤统计数据。

## 接口调用示例

### 1. 获取所有点位的统计数据（不传点位参数）
```bash
GET http://localhost:8081/api/magnetic/alarm/stats?timeSpanType=year
```

### 2. 获取指定点位的统计数据（传递点位列表）
```bash
# 获取点位1的数据
GET http://localhost:8081/api/magnetic/alarm/stats?timeSpanType=year&locationIds=1

# 获取点位1和3的数据总和
GET http://localhost:8081/api/magnetic/alarm/stats?timeSpanType=year&locationIds=1&locationIds=3

# 或使用逗号分隔（取决于前端实现）
GET http://localhost:8081/api/magnetic/alarm/stats?timeSpanType=year&locationIds=1,3
```

## 返回数据格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "safeCount": 150,      // 无风险报警数量
    "lowRiskCount": 80,    // 低风险报警数量
    "mediumRiskCount": 45, // 中风险报警数量
    "highRiskCount": 12    // 高风险报警数量
  }
}
```

## 测试步骤
1. 启动后端服务: `cd cloudplatform-srv && mvn spring-boot:run`
2. 使用 Postman 或 curl 测试上述接口
3. 验证两种使用场景：
   - 不传 locationIds：返回所有点位的汇总数据
   - 传递 locationIds：返回指定点位的汇总数据

## 实现细节
- 当不传递 `locationIds` 参数时，查询所有设备的报警数据
- 当传递 `locationIds` 参数时，只查询指定点位下的设备报警数据
- 多个点位的数据会自动求和，例如传递 [1,3] 会返回点位1和点位3的数据总和