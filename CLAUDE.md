# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 开发规范

### 环境配置
- **前端服务端口**：统一使用 `5173` 端口
- **服务启动权限**：AI助手仅提供配置和代码，不执行服务启动命令。所有服务启动操作由用户自行执行

## 常用开发命令

### 前端项目 (cloudplatform-web)
```bash
cd cloudplatform-web

# Development
pnpm dev                    # Start development server on port 3000
pnpm install               # Install dependencies

# Build & Deploy
pnpm build                 # Build for production
pnpm preview              # Preview production build
pnpm type:check           # Run TypeScript type checking

# Code Quality
pnpm format               # Format code with Prettier
npm run lint-staged       # Run lint-staged (triggered by husky)

# Docker Build
./build.sh --platform linux/amd64 --version 1.0.x
```

### 后端项目 (cloudplatform-srv)
```bash
cd cloudplatform-srv

# Development
mvn spring-boot:run       # Start development server on port 8081
mvn clean compile        # Compile source code

# Build & Test
mvn clean package        # Build JAR package
mvn test                 # Run all tests
mvn test -Dtest=ClassName # Run specific test

# Docker Build  
./build.sh --platform linux/amd64 --version 1.0.x
```

## 架构概述

这是一个全栈云监控平台，采用 Vue.js 前端和 Spring Boot 后端：

### 系统架构
- **前端**: Vue 3 + TypeScript + Arco Design + Vite
- **后端**: Spring Boot 3 + MyBatis-Plus + MySQL + Redis
- **视频流**: ZLMediaKit 用于 RTSP/HLS 流媒体
- **部署**: Docker 容器化多平台构建

### 核心组件

#### 前端架构 (cloudplatform-web)
- **框架**: Vue 3 组合式 API + TypeScript
- **UI 组件库**: Arco Design Vue 组件
- **状态管理**: Pinia 状态存储 (app, user, tab-bar)
- **路由**: Vue Router 带路由守卫和认证
- **构建系统**: Vite 自定义开发/生产环境配置
- **数据可视化**: ECharts 集成监控仪表板
- **视频播放**: Video.js 用于 RTSP/HLS 流播放

#### 后端架构 (cloudplatform-srv)
- **框架**: Spring Boot 3.4.5 + Java 21
- **数据库**: MyBatis-Plus ORM + MySQL 8.0+
- **缓存**: Redis 用于天气数据和性能优化
- **API 文档**: Springdoc OpenAPI (Swagger UI)
- **监控**: 环境传感器、磁力报警、天气集成
- **定时任务**: 自动天气数据更新

#### 数据大屏功能
应用包含专门的数据可视化仪表板 (`/dataScreen`)，具备：
- 实时环境监控（温度、湿度、氧气含量）
- 磁力报警分析和统计
- 视频监控集成
- 天气数据展示
- 基于位置的监控状态

### API 集成模式
- **前端基础 URL**: 配置时不包含 `/api` 后缀（如 `https://cloudplatform.cloudmagnet.lab`）
- **后端控制器**: 所有端点在控制器级别统一添加 `/api` 前缀
- **CORS**: 预配置前端域名集成

### 数据库架构
系统管理几个关键实体组：
- **设备管理**: `EqpEquipment`, `EqpLocation`, `EqpResult` 用于设备管理
- **磁力监测**: `MagResult`, `Mag4Result` 用于磁场检测
- **环境数据**: `WostResult` 用于温度/湿度/氧气数据
- **访问控制**: `AcrEmployee`, `AcrRole`, `AcrAccredit` 用于用户管理

### 开发流程
1. 前端运行在 5173 端口，通过 Vite 提供热更新
2. 后端在 8081 端口提供 API 服务，使用 Spring Boot DevTools
3. 两个组件可以独立容器化
4. 前端使用 pnpm 包管理，后端使用 Maven
5. 前端构建时强制执行 TypeScript 类型检查

### 重要配置说明
- 前端 API 基础 URL 不应包含 `/api` 路径以避免重复
- Docker 构建使用构建参数进行环境特定配置
- Redis 用于缓存天气 API 响应以避免速率限制
- ZLMediaKit 处理视频流，需要正确的网络配置

## 项目结构

### 目录组织结构
```
CloudPlatform/
├── cloudplatform-web/          # Vue.js 前端应用 (@cloudplatform-web)
├── cloudplatform-srv/          # Spring Boot 后端应用 (@cloudplatform-srv)
├── deploy/                     # 部署配置
│   ├── docker/                 # Docker Compose 文件
│   └── nginx/                  # Nginx 反向代理配置
├── doc/                        # 项目文档
│   ├── architecture/           # 架构分析文档
│   ├── design/                 # 功能设计文档
│   ├── prototypes/             # 设计原型和预览
│   └── tasks/                  # 开发任务记录
├── scripts/                    # 运维脚本
├── test/                       # 测试文件
│   └── manual/                 # 手动测试资源
├── tools/                      # 开发工具和实用程序
└── CLAUDE.md                   # 开发指南
```

### 工作目录配置
- **项目根目录**: `/Users/<USER>/Projects/CloudPlatform`
- **前端目录**: `@cloudplatform-web` (指向 `cloudplatform-web/`)
- **后端目录**: `@cloudplatform-srv` (指向 `cloudplatform-srv/`)
- **设计目的**: Claude 从前后端项目的父目录启动，以便进行全栈开发和集成工作。

### 开发工具配置
- **PromptX 配置**: 从项目根目录读取（工作区根目录）
  - 位置: `/Users/<USER>/Projects/CloudPlatform/.promptx/`
  - 用于角色激活和 PromptX 相关操作
- **Claude Code**: 从项目根目录启动进行全栈开发
  - 工作目录: `/Users/<USER>/Projects/CloudPlatform`
  - 访问前后端项目
  - 遵循项目特定的开发准则

### 部署配置
- **Docker Compose**: `deploy/docker/docker-compose.yml` - 容器编排
- **Nginx 配置**: `deploy/nginx/nginx-cloudplatform.conf` - 反向代理设置
- **运维脚本**: `scripts/` - 维护和部署脚本

### 开发文档
- **架构文档**: `doc/architecture/` - 系统架构文档
- **设计文档**: `doc/design/` - 功能设计和规范
- **任务记录**: `doc/tasks/` - 开发任务历史和跟踪

## 常用操作

### 部署命令
```bash
# Deploy with Docker Compose
cd deploy/docker
docker-compose up -d

# Update nginx configuration
sudo cp deploy/nginx/nginx-cloudplatform.conf /etc/nginx/sites-available/cloudplatform
sudo nginx -t && sudo systemctl reload nginx

# Run maintenance scripts
./scripts/fix-video-streaming.sh
```

### 文档管理
```bash
# View architecture documentation
ls doc/architecture/

# Access design specifications
ls doc/design/dataScreen/

# Check task history
ls doc/tasks/completed/
```

### 测试和工具
```bash
# Run manual tests
open test/manual/test-architecture-page.html

# Use development tools
python tools/company_crawler.py
```