# CloudPlatform-srv

[![Java](https://img.shields.io/badge/Java-21-orange.svg)](https://openjdk.java.net/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.4.5-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![MyBatis-Plus](https://img.shields.io/badge/MyBatis--Plus-3.5.7-blue.svg)](https://baomidou.com/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-blue.svg)](https://www.mysql.com/)
[![Redis](https://img.shields.io/badge/Redis-Latest-red.svg)](https://redis.io/)

CloudPlatform-srv 是一个全面的云端监控和管理平台后端服务。它为环境监控、磁性报警系统、天气数据集成和视频监控管理提供 RESTful API。该系统专为工业物联网应用设计，具备实时数据处理和监控功能。

## 🚀 功能特性

- **环境监控**: 实时跟踪氧气浓度、温度和湿度
- **磁性报警系统**: 全面的磁性设备报警管理和统计
- **天气集成**: 外部天气 API 集成，支持 Redis 缓存
- **视频监控**: 集成 ZLMediaKit 的 RTSP 摄像头监控
- **实时数据**: 定时数据更新的实时监控
- **RESTful API**: 完整的 Swagger/OpenAPI 文档
- **数据库集成**: 使用 MyBatis-Plus ORM 的 MySQL 数据库
- **缓存**: Redis 性能优化
- **监控仪表板**: 系统状态和设备监控

## 📋 系统要求

运行此应用程序之前，请确保已安装以下软件：

- **Java 21** 或更高版本
- **Maven 3.6+**
- **MySQL 8.0+**
- **Redis 服务器**
- **Git**

## 🛠️ 安装步骤

### 1. 克隆仓库

```bash
git clone ssh://**************************:2289/cm_project/cloud_platform/cy/cloudplatform-srv.git
cd cloudplatform-srv
```

### 2. 数据库设置

创建 MySQL 数据库并配置连接：

```sql
CREATE DATABASE cloudmagnet CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. Redis 设置

确保 Redis 在您的系统上运行：

```bash
# Ubuntu/Debian 系统
sudo systemctl start redis-server

# macOS 使用 Homebrew
brew services start redis

# Windows 启动 Redis 服务
```

### 4. 配置

更新 `src/main/resources/application.yml` 中的配置：

```yaml
spring:
  datasource:
    url: *************************************************************************************************************************
    username: your_username
    password: your_password
  data:
    redis:
      host: localhost
      port: 6379
      # password: your_redis_password  # 如果 Redis 有密码请取消注释

weather:
  api:
    appcode: your_weather_api_appcode  # 替换为实际的 API 代码
    areaCode: 320104  # 天气数据的默认区域代码
```

### 5. 构建和运行

```bash
# 构建项目
mvn clean compile

# 运行应用程序
mvn spring-boot:run

# 或构建 JAR 并运行
mvn clean package
java -jar target/cloudplatform-srv-0.0.1-SNAPSHOT.jar
```

应用程序将在 `http://localhost:8081` 启动

## 🔧 配置说明

### 环境变量

您可以使用环境变量覆盖配置：

```bash
export SPRING_DATASOURCE_URL=***************************************
export SPRING_DATASOURCE_USERNAME=your_username
export SPRING_DATASOURCE_PASSWORD=your_password
export SPRING_DATA_REDIS_HOST=your-redis-host
export WEATHER_API_APPCODE=your_weather_api_code
```

### 应用程序属性

`application.yml` 中的关键配置选项：

- **服务器端口**: `server.port`（默认：8081）
- **数据库**: `spring.datasource.*`
- **Redis**: `spring.data.redis.*`
- **日志**: `logging.level.*`
- **天气 API**: `weather.api.*`

## 📚 API 文档

### Swagger UI

访问交互式 API 文档：
```
http://localhost:8081/swagger-ui/index.html
```

### 主要 API 端点

#### 环境数据
- `GET /api/environmental/average/latest` - 获取平均环境数据
- `GET /api/environmental/trend/hourly` - 获取每小时环境趋势
- `GET /api/environmental/alarm/count` - 获取环境报警统计

#### 磁性报警系统
- `GET /api/magnetic/alarm/totalStats` - 获取磁性报警总统计
- `GET /api/magnetic/alarm/counts` - 按时间获取磁性报警计数
- `GET /api/magnetic/alarm/countsByLocation` - 按位置获取报警计数

#### 天气数据
- `GET /api/weather/current/{areaCode}` - 获取当前天气数据
- `GET /api/weather/triggerWeatherUpdate` - 手动触发天气更新

#### 系统监控
- `GET /api/monitoring/locations/status` - 获取所有位置监控状态
- `GET /api/system/currentTime` - 获取当前系统时间

#### API 路径规范

**重要说明**: 所有 API 端点都使用 `/api` 作为统一前缀，这是在控制器类级别通过 `@RequestMapping("/api")` 注解配置的。

**前端集成注意事项**:
- 前端应用的 `VITE_API_BASE_URL` 应配置为不包含 `/api` 的基础域名
- 例如: `https://cloudplatform.cloudmagnet.lab` 而不是 `https://cloudplatform.cloudmagnet.lab/api`
- 前端 API 调用代码中已包含 `/api` 路径前缀

**CORS 配置**:
- 已配置允许前端域名的跨域请求
- 支持常用的 HTTP 方法和请求头
- 生产环境需要根据实际前端域名调整 CORS 配置

## 🏗️ 项目结构

```
src/main/java/lab/cloudmagnet/cloudplatform_srv/
├── CloudplatformSrvApplication.java    # 主应用程序类
├── config/                             # 配置类
│   └── CorsConfig.java                # CORS 配置
├── controller/                         # REST 控制器
│   ├── EnvironmentalController.java   # 环境数据 API
│   ├── MagneticController.java        # 磁性报警 API
│   ├── MonitoringController.java      # 监控 API
│   ├── SystemController.java          # 系统 API
│   └── WeatherController.java         # 天气 API
├── dto/                               # 数据传输对象
├── entity/                            # 数据库实体
├── mapper/                            # MyBatis 映射器
├── response/                          # 响应包装类
├── schedule/                          # 定时任务
├── service/                           # 服务接口
├── service/impl/                      # 服务实现
└── util/                             # 工具类
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=EnvironmentalControllerTest

# 运行测试并生成覆盖率报告
mvn test jacoco:report
```

### 测试结构

```
src/test/java/lab/cloudmagnet/cloudplatform_srv/
├── controller/          # 控制器测试
├── service/            # 服务测试
└── integration/        # 集成测试
```

## 🚀 部署

### 生产构建

```bash
# 创建生产 JAR
mvn clean package -Pprod

# JAR 将在 target/ 目录中创建
java -jar target/cloudplatform-srv-0.0.1-SNAPSHOT.jar
```

### Docker 部署

```dockerfile
FROM openjdk:21-jre-slim

WORKDIR /app
COPY target/cloudplatform-srv-0.0.1-SNAPSHOT.jar app.jar

EXPOSE 8081

CMD ["java", "-jar", "app.jar"]
```

### Docker Compose

项目包含用于 ZLMediaKit 集成的 `docker-compose.yml`：

```bash
# 启动 ZLMediaKit 视频流服务
docker-compose up -d
```

## 🔍 监控和日志

### 应用程序日志

日志在 `application.yml` 中配置：

```yaml
logging:
  level:
    root: info
    lab.cloudmagnet.cloudplatform_srv: debug
    com.baomidou: debug
```

### 健康检查

Spring Boot Actuator 端点（如果启用）：
- `/actuator/health` - 应用程序健康状态
- `/actuator/metrics` - 应用程序指标
- `/actuator/info` - 应用程序信息

## 🛠️ 开发

### 开发环境设置

1. **IDE 设置**: 在 IntelliJ IDEA 或 Eclipse 中导入为 Maven 项目
2. **代码风格**: 遵循 Java 编码规范
3. **数据库**: 如需要可使用 H2 进行开发
4. **热重载**: 使用 Spring Boot DevTools 进行开发

### 添加新功能

1. 在 `entity/` 包中创建实体类
2. 在 `mapper/` 包中创建映射器接口
3. 在 `service/` 和 `service/impl/` 中实现服务
4. 在 `controller/` 包中创建控制器
5. 在 `dto/` 包中添加 DTO
6. 为新功能编写测试

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 MySQL 是否运行
   - 验证 `application.yml` 中的连接参数
   - 确保数据库存在

2. **Redis 连接失败**
   - 检查 Redis 服务器是否运行
   - 验证 Redis 配置
   - 检查防火墙设置

3. **天气 API 不工作**
   - 验证配置中的 API 密钥
   - 检查网络连接
   - 查看 API 速率限制

4. **端口已被使用**
   - 在 `application.yml` 中更改端口
   - 终止使用该端口的进程: `lsof -ti:8081 | xargs kill`

5. **前端 API 调用失败**
   - 检查前端的 `VITE_API_BASE_URL` 配置是否正确
   - 确认前端基础 URL 不包含重复的 `/api` 路径
   - 验证 CORS 配置是否允许前端域名
   - 检查网络连接和防火墙设置

6. **API 路径问题**
   - 所有 API 端点都以 `/api` 开头
   - 前端请求应该是: `baseURL + /api/endpoint`
   - 避免在基础 URL 中包含 `/api` 导致路径重复

### 日志位置

- 应用程序日志: 控制台输出或配置的日志文件
- 访问日志: Spring Boot 嵌入式 Tomcat 日志
- 数据库日志: MySQL 错误日志

## 👥 作者

- **Chen Yu** - *初始工作* - [chen_yu](https://gitlab.cloudmagnet.lab/cm_project/cloud_platform/cy)

## 🆘 技术支持

如需支持和咨询：

- 在 GitLab 仓库中创建 issue
- 联系开发团队
- 查看 API 文档 `/swagger-ui/index.html`

## 📈 项目状态

本项目正在积极维护和持续开发中。根据需求和反馈，定期添加新功能和改进。
