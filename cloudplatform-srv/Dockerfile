# CloudPlatform-srv 生产级优化 Dockerfile
# 基于 OpenJDK 21 的多阶段构建，专为 Spring Boot 3.4.5 应用优化
# 支持 MyBatis-Plus 3.5.7、Redis、MySQL 8.0+ 等组件
#
# 构建命令示例：
# docker build -t cloudmagnet/cloudplatform-srv:latest .
# docker build --build-arg MAVEN_OPTS="-Xmx2048m" -t cloudmagnet/cloudplatform-srv:latest .

# ============================================================================
# 第一阶段：Maven 构建阶段
# ============================================================================
FROM maven:3.9-eclipse-temurin-21-alpine AS builder

# 设置构建阶段的标签信息
LABEL stage="builder" \
      description="CloudPlatform-srv Maven 构建阶段" \
      maven.version="3.9.6" \
      java.version="21"

# 设置工作目录
WORKDIR /app

# 安装构建时需要的系统工具（最小化安装）
# 跳过 git 安装以避免网络问题
# RUN apk add --no-cache ca-certificates

# 设置构建参数，允许在构建时自定义
ARG MAVEN_OPTS="-Dmaven.repo.local=/root/.m2/repository -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport"
ARG SKIP_TESTS=true

# 设置 Maven 配置以优化构建性能
ENV MAVEN_OPTS=${MAVEN_OPTS}

# 创建 Maven 本地仓库目录
RUN mkdir -p /root/.m2/repository

# 复制 Maven 配置文件（利用 Docker 层缓存优化）
# 先复制 pom.xml，这样依赖下载可以被缓存
COPY pom.xml .

# 下载项目依赖（这一层会被缓存，除非 pom.xml 发生变化）
# dependency:resolve-sources: 下载源码，便于调试
# dependency:go-offline: 预下载所有依赖到本地仓库
RUN mvn dependency:go-offline dependency:resolve-sources -B --no-transfer-progress \
    && mvn dependency:list -B --no-transfer-progress

# 复制源代码（放在依赖下载之后，避免代码变更导致重新下载依赖）
COPY src ./src

# 构建应用程序
# clean: 清理之前的构建结果
# package: 打包应用程序
# -DskipTests: 根据参数决定是否跳过测试
# -B: 批处理模式，减少交互式输出
# --no-transfer-progress: 不显示传输进度，减少日志输出
# -Dmaven.compile.fork=true: 使用独立进程编译，提高性能
RUN if [ "$SKIP_TESTS" = "true" ]; then \
        mvn clean package -DskipTests -B --no-transfer-progress -Dmaven.compile.fork=true; \
    else \
        mvn clean package -B --no-transfer-progress -Dmaven.compile.fork=true; \
    fi

# 验证构建结果并提取 JAR 文件信息
RUN ls -la target/ && \
    test -f target/cloudplatform-srv-*.jar && \
    echo "构建成功，JAR 文件信息：" && \
    ls -lh target/cloudplatform-srv-*.jar

# ============================================================================
# 第二阶段：运行时阶段
# ============================================================================
FROM amazoncorretto:21-alpine AS runtime

# 设置镜像元数据和标签
LABEL maintainer="CloudMagnet Lab <<EMAIL>>" \
      description="CloudPlatform-srv 后端服务 - Spring Boot 3.4.5 + Java 21" \
      version="0.0.1-SNAPSHOT" \
      java.version="21" \
      spring.boot.version="3.4.5" \
      mybatis.plus.version="3.5.7" \
      build.date="2024-06-18" \
      project.name="cloudplatform-srv" \
      project.group="lab.cloudmagnet" \
      project.artifact="cloudplatform-srv" \
      license="Proprietary" \
      vendor="CloudMagnet Lab"

# 设置运行时参数
ARG APP_USER=cloudplatform
ARG APP_UID=1001
ARG APP_GID=1001

# 设置时区为上海时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建非 root 用户以提高安全性
# 使用固定的 UID/GID 确保跨容器一致性，避免权限问题
# Alpine Linux 使用 addgroup 和 adduser 命令
RUN addgroup -g ${APP_GID} -S ${APP_USER} && \
    adduser -u ${APP_UID} -D -S -G ${APP_USER} -h /app -s /sbin/nologin ${APP_USER}

# 安装运行时必要的系统工具（最小化安装）
# curl: 用于健康检查
# ca-certificates: SSL/TLS 证书
# tzdata: 时区数据
# dumb-init: 进程管理，处理僵尸进程
RUN apk add --no-cache \
    curl \
    ca-certificates \
    tzdata \
    dumb-init

# 设置工作目录
WORKDIR /app

# 创建必要的目录结构
# logs: 应用日志目录
# config: 外部配置文件目录
# temp: 临时文件目录
# heapdumps: 堆转储文件目录
RUN mkdir -p /app/logs /app/config /app/temp /app/heapdumps && \
    chown -R ${APP_USER}:${APP_USER} /app && \
    chmod -R 755 /app

# 从构建阶段复制 JAR 文件
# 使用通配符匹配 JAR 文件名，避免版本变更导致的问题
COPY --from=builder --chown=${APP_USER}:${APP_USER} /app/target/cloudplatform-srv-*.jar /app/app.jar

# 验证 JAR 文件完整性和基本信息
RUN ls -lh /app/app.jar && \
    echo "JAR 文件大小: $(du -h /app/app.jar | cut -f1)" && \
    echo "JAR 文件验证完成"

# ============================================================================
# 运行时配置
# ============================================================================

# 切换到非 root 用户（安全最佳实践）
USER ${APP_USER}

# 暴露 Spring Boot 应用端口
EXPOSE 8081

# 设置 JVM 参数以优化容器环境下的性能
# 基础 JVM 参数：
# -server: 使用服务器模式 JVM
# -Djava.security.egd: 使用非阻塞随机数生成器，提高启动速度
# -Dfile.encoding: 设置文件编码为 UTF-8
# -Duser.timezone: 设置时区
#
# 内存管理参数：
# -Xms: 初始堆内存大小
# -Xmx: 最大堆内存大小
# -XX:MaxRAMPercentage: JVM 可使用的最大内存百分比（容器感知）
# -XX:InitialRAMPercentage: JVM 初始内存百分比
#
# 垃圾收集参数：
# -XX:+UseG1GC: 使用 G1 垃圾收集器，适合低延迟应用
# -XX:MaxGCPauseMillis: 设置 GC 最大暂停时间
# -XX:G1HeapRegionSize: G1 堆区域大小
#
# 容器支持参数：
# -XX:+UseContainerSupport: 启用容器感知，自动检测容器资源限制
#
# 错误处理参数：
# -XX:+ExitOnOutOfMemoryError: 内存溢出时退出 JVM
# -XX:+HeapDumpOnOutOfMemoryError: 内存溢出时生成堆转储
# -XX:HeapDumpPath: 堆转储文件路径
#
# 诊断和监控参数（Java 21 兼容）：
# -Xlog:gc: 使用新的日志格式替代已废弃的 -XX:+PrintGCDetails
ENV JAVA_OPTS="-server \
    -Djava.security.egd=file:/dev/./urandom \
    -Dfile.encoding=UTF-8 \
    -Duser.timezone=Asia/Shanghai \
    -Xms512m \
    -Xmx1024m \
    -XX:+UseG1GC \
    -XX:MaxGCPauseMillis=200 \
    -XX:G1HeapRegionSize=16m \
    -XX:+UseContainerSupport \
    -XX:MaxRAMPercentage=75.0 \
    -XX:InitialRAMPercentage=50.0 \
    -XX:+ExitOnOutOfMemoryError \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=/app/heapdumps/ \
    -Xlog:gc:/app/logs/gc.log:time,tags"

# 设置 Spring Boot 应用的环境变量
ENV SERVER_PORT=8081 \
    SPRING_PROFILES_ACTIVE=docker \
    LOGGING_LEVEL_ROOT=INFO \
    LOGGING_LEVEL_LAB_CLOUDMAGNET=DEBUG \
    LOGGING_FILE_NAME=/app/logs/cloudplatform-srv.log \
    MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics,prometheus \
    MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when_authorized \
    SPRING_JPA_SHOW_SQL=false \
    SPRING_JPA_HIBERNATE_DDL_AUTO=none

# 健康检查配置
# --interval: 健康检查间隔时间（30秒）
# --timeout: 单次健康检查超时时间（10秒）
# --start-period: 容器启动后等待时间（90秒），给应用足够的启动时间
# --retries: 连续失败次数后标记为不健康（3次）
# 检查 Spring Boot Actuator 的健康端点
HEALTHCHECK --interval=30s \
            --timeout=10s \
            --start-period=90s \
            --retries=3 \
    CMD curl -f http://localhost:8081/actuator/health || exit 1

# 设置容器启动时的信号处理
# SIGTERM: 优雅关闭信号，Spring Boot 会处理这个信号进行优雅关闭
STOPSIGNAL SIGTERM

# ============================================================================
# 启动命令配置
# ============================================================================

# 启动命令
# 使用 dumb-init 作为 PID 1 进程，正确处理信号和僵尸进程
# 使用 exec 形式确保 Java 进程能够接收到系统信号
# $JAVA_OPTS 允许在运行时覆盖 JVM 参数
ENTRYPOINT ["dumb-init", "--"]
CMD ["sh", "-c", "exec java $JAVA_OPTS -jar app.jar"]

# ============================================================================
# 构建和运行最佳实践说明
# ============================================================================

# 构建命令示例：
# 基础构建：
# docker build -t cloudmagnet/cloudplatform-srv:latest .
#
# 自定义 Maven 参数构建：
# docker build --build-arg MAVEN_OPTS="-Xmx2048m" -t cloudmagnet/cloudplatform-srv:latest .
#
# 包含测试的构建：
# docker build --build-arg SKIP_TESTS=false -t cloudmagnet/cloudplatform-srv:latest .
#
# 多平台构建：
# docker buildx build --platform linux/amd64,linux/arm64 -t cloudmagnet/cloudplatform-srv:latest .

# 运行命令示例：
# 基础运行：
# docker run -d --name cloudplatform-srv -p 8081:8081 cloudmagnet/cloudplatform-srv:latest
#
# 生产环境运行（推荐）：
# docker run -d \
#   --name cloudplatform-srv \
#   --restart unless-stopped \
#   -p 8081:8081 \
#   -e SPRING_PROFILES_ACTIVE=prod \
#   -e SPRING_DATASOURCE_URL=*********************************** \
#   -e SPRING_DATASOURCE_USERNAME=cloudplatform \
#   -e SPRING_DATASOURCE_PASSWORD=your_password \
#   -e SPRING_DATA_REDIS_HOST=redis \
#   -e SPRING_DATA_REDIS_PORT=6379 \
#   -e JAVA_OPTS="-Xmx2048m -XX:MaxRAMPercentage=80.0" \
#   -v /app/logs:/app/logs \
#   -v /app/config:/app/config \
#   --memory=2g \
#   --cpus=2 \
#   cloudmagnet/cloudplatform-srv:latest
#
# 开发环境运行：
# docker run -d \
#   --name cloudplatform-srv-dev \
#   -p 8081:8081 \
#   -e SPRING_PROFILES_ACTIVE=dev \
#   -e LOGGING_LEVEL_LAB_CLOUDMAGNET=DEBUG \
#   -v $(pwd)/logs:/app/logs \
#   cloudmagnet/cloudplatform-srv:latest

# 环境变量说明：
# 必需的环境变量：
# - SPRING_DATASOURCE_URL: 数据库连接 URL
# - SPRING_DATASOURCE_USERNAME: 数据库用户名
# - SPRING_DATASOURCE_PASSWORD: 数据库密码
# - SPRING_DATA_REDIS_HOST: Redis 主机地址
#
# 可选的环境变量：
# - SPRING_PROFILES_ACTIVE: Spring 配置文件（dev/test/prod/docker）
# - JAVA_OPTS: 自定义 JVM 参数
# - SERVER_PORT: 服务端口（默认 8081）
# - LOGGING_LEVEL_ROOT: 根日志级别（默认 INFO）
# - WEATHER_API_APPCODE: 天气 API 应用代码
# - WEATHER_API_AREACODE: 天气 API 区域代码

# 数据卷挂载建议：
# - /app/logs: 应用日志目录
# - /app/config: 外部配置文件目录
# - /app/heapdumps: 堆转储文件目录

# 资源限制建议：
# - 内存: 最少 1GB，推荐 2GB 或更多
# - CPU: 最少 1 核，推荐 2 核或更多
# - 磁盘: 最少 10GB，用于日志和临时文件

# 监控和健康检查：
# - 健康检查端点: http://localhost:8081/actuator/health
# - 指标端点: http://localhost:8081/actuator/metrics
# - 应用信息: http://localhost:8081/actuator/info
# - API 文档: http://localhost:8081/swagger-ui/index.html
