<?xml version="1.0" encoding="UTF-8"?>
<!-- Maven 项目对象模型 (POM) 文件 -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<!-- 父项目：引入 Spring Boot 的父级依赖，管理共同配置和依赖版本 -->
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.5</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>

	<!-- 项目基本信息 -->
	<groupId>lab.cloudmagnet</groupId>
	<artifactId>cloudplatform-srv</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>cloudplatform-srv</name>
	<description>Demo project for Spring Boot</description>

	<url/>
	<licenses>
		<license/>
	</licenses>
	<developers>
		<developer/>
	</developers>
	<scm>
		<connection/>
		<developerConnection/>
		<tag/>
		<url/>
	</scm>

	<!-- 项目属性：定义项目相关的配置，如 Java 版本 -->
	<properties>
		<java.version>21</java.version>
	</properties>

	<!-- 项目依赖：定义项目运行和编译所需的各种库 -->
	<dependencies>
		<!-- Spring Boot 核心依赖：提供基础功能 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>

		<!-- Spring Boot 测试依赖：用于编写单元测试和集成测试 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- Spring Boot Web 依赖：构建 Web 应用程序，包含 Tomcat 和 Spring MVC -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId> 
		</dependency>
		
		<!-- MyBatis-Plus Spring Boot Starter：简化 MyBatis-Plus 与 Spring Boot 集成 -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-spring-boot3-starter</artifactId>
			<version>3.5.7</version>
		</dependency>

		<!-- Lombok：简化 Java Bean 开发，自动生成 getter/setter 等 -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- Spring Boot 日志依赖：提供日志功能 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-logging</artifactId>
		</dependency>

		<!-- MySQL 连接器：用于连接 MySQL 数据库 -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>8.0.33</version>
		</dependency>

		<!-- Spring Boot 配置处理器：用于生成配置元数据 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- Spring Boot DevTools：提供开发时的热部署等便利功能 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>

		<!-- Spring Boot Validation 依赖：支持 JSR 303 Bean Validation -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

		<!-- Springdoc OpenAPI UI Starter：自动生成 Swagger/OpenAPI 文档界面 -->
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.8.8</version>
		</dependency>

		<!-- Spring Boot Data Redis 依赖：集成 Redis -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

	</dependencies>

	<!-- 项目构建配置 -->
	<build>
		<plugins>
			<!-- Spring Boot Maven 插件：打包 Spring Boot 应用为可执行 JAR -->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
