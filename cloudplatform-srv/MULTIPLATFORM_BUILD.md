# CloudPlatform-srv 多平台构建指南

## 概述

`build-multiplatform.sh` 是一个简化的多平台构建脚本，专门用于构建支持不同 CPU 架构的 CloudPlatform-srv Docker 镜像。

## 功能特性

### 🏗️ 核心功能
- ✅ 支持 AMD64 (x86_64) 和 ARM64 (Apple Silicon) 平台
- ✅ 基于 Docker Buildx 的多平台构建
- ✅ 自动平台检测
- ✅ 简洁的命令行界面

### 🎯 设计目标
- **简化**: 避免复杂功能，专注于多平台构建
- **高效**: 利用 Docker Buildx 的缓存和并行构建
- **兼容**: 支持 macOS M1/M2 和 Linux 系统

## 快速开始

### 基础用法

```bash
# 构建当前平台镜像（自动检测）
./build-multiplatform.sh

# 构建 ARM64 平台镜像
./build-multiplatform.sh --platform arm64

# 构建 AMD64 平台镜像
./build-multiplatform.sh --platform amd64

# 构建所有平台镜像
./build-multiplatform.sh --platform all
```

### 高级用法

```bash
# 构建并推送到仓库
./build-multiplatform.sh --platform all --push

# 详细模式构建
./build-multiplatform.sh --platform arm64 --verbose

# 查看帮助信息
./build-multiplatform.sh --help
```

## 命令参数

| 参数 | 描述 | 可选值 |
|------|------|--------|
| `--platform` | 指定构建平台 | `amd64`, `arm64`, `all` |
| `--push` | 构建后推送镜像 | - |
| `--verbose` | 显示详细输出 | - |
| `--help` | 显示帮助信息 | - |

## 平台支持

### 支持的平台
- **linux/amd64**: Intel/AMD x86_64 架构
- **linux/arm64**: ARM64 架构（Apple Silicon M1/M2）

### 平台检测
脚本会自动检测当前系统架构：
- macOS M1/M2: 自动选择 `linux/arm64`
- Intel Mac/Linux: 自动选择 `linux/amd64`

## 构建结果

### 镜像标签
构建完成后会生成以下标签：
- `cloudmagnet/cloudplatform-srv:latest`
- `cloudmagnet/cloudplatform-srv:{version}`

### 版本号规则
1. **Git 标签**: 如果当前提交有标签，使用标签作为版本
2. **pom.xml + Git**: 使用 pom.xml 版本 + Git 提交哈希
3. **时间戳**: 如果以上都不可用，使用时间戳

## 使用场景

### 开发环境
```bash
# 在 Apple Silicon Mac 上开发
./build-multiplatform.sh --platform arm64

# 在 Intel Mac 上开发
./build-multiplatform.sh --platform amd64
```

### 生产部署
```bash
# 构建多平台镜像并推送
./build-multiplatform.sh --platform all --push
```

### CI/CD 集成
```bash
# 在 CI 环境中构建多平台镜像
./build-multiplatform.sh --platform all --push --verbose
```

## 运行示例

### 构建成功输出
```
=== CloudPlatform-srv 多平台构建 ===
Docker Buildx 版本: v0.24.0
当前系统架构: arm64

[INFO] 检查构建环境...
[SUCCESS] 环境检查通过
[INFO] 设置多平台构建器...
[SUCCESS] 构建器设置完成
[INFO] 开始构建多平台镜像...
项目: cloudplatform-srv
版本: 3.4.5-60205ad
平台: linux/arm64
推送: 否

[SUCCESS] 镜像构建成功!

=== 构建完成 ===
✓ 项目: cloudplatform-srv
✓ 版本: 3.4.5-60205ad
✓ 镜像: cloudmagnet/cloudplatform-srv:latest
✓ 平台: linux/arm64

运行命令示例:
  docker run -d --name cloudplatform-srv -p 8081:8081 cloudmagnet/cloudplatform-srv:latest
```

### 运行容器
```bash
# 启动容器
docker run -d \
  --name cloudplatform-srv \
  -p 8081:8081 \
  -e SPRING_PROFILES_ACTIVE=docker \
  cloudmagnet/cloudplatform-srv:latest

# 查看日志
docker logs cloudplatform-srv

# 健康检查
curl http://localhost:8081/actuator/health
```

## 故障排除

### 常见问题

#### Docker Buildx 不可用
```bash
# 检查 Docker 版本
docker version

# 启用 Buildx（如果需要）
docker buildx install
```

#### 构建器创建失败
```bash
# 手动创建构建器
docker buildx create --name cloudplatform-multiplatform --use

# 检查构建器状态
docker buildx ls
```

#### 多平台构建失败
```bash
# 检查平台支持
docker buildx inspect --bootstrap

# 清理并重新创建构建器
docker buildx rm cloudplatform-multiplatform
./build-multiplatform.sh --platform arm64
```

### 调试技巧

1. **使用详细模式**: `--verbose` 查看完整构建输出
2. **检查构建器**: `docker buildx ls` 查看可用构建器
3. **验证镜像**: `docker buildx imagetools inspect <image>` 查看镜像平台信息

## 性能优化

### 构建缓存
- Docker Buildx 会自动利用构建缓存
- 多平台构建可以并行执行
- 第二次构建会显著加快

### 最佳实践
1. **首次构建**: 使用单平台构建测试
2. **生产构建**: 使用多平台构建确保兼容性
3. **CI/CD**: 配置缓存策略提高构建速度

## 技术细节

### Docker Buildx
- 使用 `docker-container` 驱动
- 支持跨平台模拟构建
- 自动管理构建器生命周期

### 基础镜像
- 构建阶段: `maven:3.9-eclipse-temurin-21-alpine`
- 运行阶段: `eclipse-temurin:21-jre-alpine`
- 支持多架构的官方镜像

### 构建优化
- 跳过测试以加快构建速度
- 使用多阶段构建减少镜像大小
- 利用 Alpine Linux 最小化基础镜像