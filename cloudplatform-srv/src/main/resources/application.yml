# 数据库连接配置 (以 MySQL 为例)
spring:
  application:
    name: cloudplatform-srv
  datasource:
    url: ******************************************************************************************************************************************************************
    username: root
    password: xiaoye71
    driver-class-name: com.mysql.cj.jdbc.Driver
  # Redis 配置
  data:
    redis:
      host: localhost # Redis 服务器地址，如果不是本机，请修改
      port: 6379     # Redis 端口，如果不是默认端口，请修改
      # password: your_password # 如果 Redis 设置了密码，请取消注释并填写密码

# MyBatis-Plus 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 开启驼峰命名自动映射

# 服务器端口配置
server:
  port: 8081 # 应用运行端口，您可以修改为其他端口

# 日志配置 (基础示例)
logging:
  level:
    root: info # 默认日志级别
    lab.cloudmagnet.cloudplatform_srv: debug # 指定您项目包的日志级别，方便开发调试
    com.baomidou: debug # MyBatis-Plus 的日志级别

weather:
  api:
    appcode: 61dd02402f3141169a02d743f746fc2c # <<< 替换为实际 APPCODE
    areaCode: 320104 # redis存储的区域代码，默认为秦淮区