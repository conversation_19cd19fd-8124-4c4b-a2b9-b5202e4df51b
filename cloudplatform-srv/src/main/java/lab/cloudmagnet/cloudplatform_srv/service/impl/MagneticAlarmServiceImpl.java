package lab.cloudmagnet.cloudplatform_srv.service.impl;

import lab.cloudmagnet.cloudplatform_srv.dto.MagneticAlarmTotalStatsDTO;
import lab.cloudmagnet.cloudplatform_srv.dto.MagneticAlarmCountDTO;
import lab.cloudmagnet.cloudplatform_srv.dto.TotalAlarmCountDTO;
import lab.cloudmagnet.cloudplatform_srv.service.MagneticAlarmService;
import lab.cloudmagnet.cloudplatform_srv.mapper.EqpEquipmentMapper;
import lab.cloudmagnet.cloudplatform_srv.mapper.EqpEventMapper;
import lab.cloudmagnet.cloudplatform_srv.mapper.EqpLocationMapper;
import lab.cloudmagnet.cloudplatform_srv.entity.EqpEquipment;
import lab.cloudmagnet.cloudplatform_srv.entity.EqpEvent;
import lab.cloudmagnet.cloudplatform_srv.entity.EqpLocation;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Optional;
import java.util.ArrayList;
import lab.cloudmagnet.cloudplatform_srv.dto.LocationAlarmCountDTO;

@Service
public class MagneticAlarmServiceImpl implements MagneticAlarmService {

    @Autowired
    private EqpEquipmentMapper eqpEquipmentMapper;

    @Autowired
    private EqpEventMapper eqpEventMapper;

    @Autowired
    private EqpLocationMapper eqpLocationMapper;

    @Override
    public MagneticAlarmTotalStatsDTO getMagneticAlarmStats(String timeSpanType, List<String> locationIds) {
        LocalDateTime[] timeRange = calculateTimeRange(timeSpanType);
        LocalDateTime startTime = timeRange[0];
        LocalDateTime endTime = timeRange[1];

        // 1. 查询 eqp_equipment 获取相关设备ID
        QueryWrapper<EqpEquipment> equipmentWrapper = new QueryWrapper<>();
        equipmentWrapper.in("SBLX", Arrays.asList("1", "10", "11", "12", "2", "20", "21"));
        equipmentWrapper.eq("SBZT", "0");
        equipmentWrapper.eq("YXBJ", "0");
        
        // 如果传递了点位ID列表，添加点位过滤条件
        if (locationIds != null && !locationIds.isEmpty()) {
            List<Integer> locationIdInts = locationIds.stream()
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            equipmentWrapper.in("DWBH", locationIdInts);
        }
        
        List<Integer> magneticDeviceIds = eqpEquipmentMapper.selectList(equipmentWrapper)
                .stream()
                .map(EqpEquipment::getSbbh)
                .collect(Collectors.toList());

        if (magneticDeviceIds.isEmpty()) {
            return new MagneticAlarmTotalStatsDTO(); // 未找到铁磁设备
        }

        // 2. 查询 eqp_event 统计这些设备在时间范围内的 SJJB 事件数量
        QueryWrapper<EqpEvent> eventWrapper = new QueryWrapper<>();
        eventWrapper.eq("SJLX", 3); // 事件类型 3-报警事件
        eventWrapper.in("SBBH", magneticDeviceIds);
        // 对 KSSJ 字段使用 >= startTime 和 <= endTime 进行日期范围查询
        eventWrapper.between("KSSJ", Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()),
                Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()));
        eventWrapper.select("SJJB", "COUNT(*) as alarmCount");
        eventWrapper.groupBy("SJJB");

        // 执行查询并获取每个 SJJB 的计数
        List<Map<String, Object>> eventCounts = eqpEventMapper.selectMaps(eventWrapper);

        // 3. 聚合每个 SJJB (0, 1, 2, 3) 的计数
        Map<Integer, Long> statsMap = eventCounts.stream()
                .filter(map -> map.get("SJJB") != null && map.get("alarmCount") != null)
                .collect(Collectors.toMap(
                        map -> (Integer) map.get("SJJB"),
                        map -> Optional.ofNullable(map.get("alarmCount"))
                                .map(Object::toString)
                                .map(Long::valueOf)
                                .orElse(0L),
                        (existingValue, newValue) -> existingValue));

        MagneticAlarmTotalStatsDTO stats = new MagneticAlarmTotalStatsDTO();
        stats.setSafeCount(statsMap.getOrDefault(0, 0L));
        stats.setLowRiskCount(statsMap.getOrDefault(1, 0L));
        stats.setMediumRiskCount(statsMap.getOrDefault(2, 0L));
        stats.setHighRiskCount(statsMap.getOrDefault(3, 0L));

        return stats;
    }

    @Override
    public MagneticAlarmCountDTO getMagneticAlarmCount(String timeSpanType, List<String> locationIds) {
        if (timeSpanType == null || timeSpanType.trim().isEmpty()) {
            throw new IllegalArgumentException("时间范围类型 (timeSpanType) 不能为空");
        }

        LocalDateTime[] timeRange = calculateTimeRange(timeSpanType);
        LocalDateTime startTime = timeRange[0];
        LocalDateTime endTime = timeRange[1];

        // 1. 查询 eqp_equipment 获取相关设备ID
        QueryWrapper<EqpEquipment> equipmentWrapper = new QueryWrapper<>();
        equipmentWrapper.in("SBLX", Arrays.asList("1", "10", "11", "12", "2", "20", "21"));
        equipmentWrapper.eq("SBZT", "0");
        equipmentWrapper.eq("YXBJ", "0");

        if (locationIds != null && !locationIds.isEmpty()) {
            List<Integer> locationIdInts = locationIds.stream()
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            equipmentWrapper.in("DWBH", locationIdInts); // 假设SBBH是设备编号，与点位ID对应
        }

        List<Integer> magneticDeviceIds = eqpEquipmentMapper.selectList(equipmentWrapper)
                .stream()
                .map(EqpEquipment::getSbbh)
                .collect(Collectors.toList());

        MagneticAlarmCountDTO dto = new MagneticAlarmCountDTO();
        if (magneticDeviceIds.isEmpty()) {
            dto.setAlarmCount(0L); // 未找到符合条件的铁磁设备
            return dto;
        }

        // 2. 查询 eqp_event 统计这些设备在时间范围内的 SJJB 事件数量
        QueryWrapper<EqpEvent> eventWrapper = new QueryWrapper<>();
        eventWrapper.eq("SJLX", 3); // 事件类型 3-报警事件
        eventWrapper.in("SBBH", magneticDeviceIds);
        // 对 KSSJ 字段使用 >= startTime 和 <= endTime 进行日期范围查询
        eventWrapper.between("KSSJ", Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()),
                Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()));

        Long alarmCount = eqpEventMapper.selectCount(eventWrapper);

        dto.setAlarmCount(alarmCount);
        return dto;
    }

    @Override
    public TotalAlarmCountDTO getTotalAlarmTotalCount(String timeSpanType, List<String> locationIds) {
        if (timeSpanType == null || timeSpanType.trim().isEmpty()) {
            throw new IllegalArgumentException("时间范围类型 (timeSpanType) 不能为空");
        }

        LocalDateTime[] timeRange = calculateTimeRange(timeSpanType);
        LocalDateTime startTime = timeRange[0];
        LocalDateTime endTime = timeRange[1];

        // 1. 查询 eqp_equipment 获取相关设备ID (铁磁、氧浓度、温湿度传感器)
        QueryWrapper<EqpEquipment> equipmentWrapper = new QueryWrapper<>();
        equipmentWrapper.in("SBLX", Arrays.asList("1", "10", "11", "12", "2", "20", "21", "A1", "A3")); // 添加氧浓度和温湿度传感器的SBLX
        equipmentWrapper.eq("SBZT", "0"); // 正常状态
        equipmentWrapper.eq("YXBJ", "0"); // 有效标记

        if (locationIds != null && !locationIds.isEmpty()) {
            List<Integer> locationIdInts = locationIds.stream()
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            equipmentWrapper.in("DWBH", locationIdInts); // 按点位过滤
        }

        List<Integer> relevantDeviceIds = eqpEquipmentMapper.selectList(equipmentWrapper)
                .stream()
                .map(EqpEquipment::getSbbh)
                .collect(Collectors.toList());

        if (relevantDeviceIds.isEmpty()) {
            return new TotalAlarmCountDTO(0L); // 未找到符合条件的设备，返回0
        }

        // 2. 查询 eqp_event 统计这些设备在时间范围内的 SJJB 事件数量
        QueryWrapper<EqpEvent> eventWrapper = new QueryWrapper<>();
        eventWrapper.eq("SJLX", 3); // 事件类型 3-报警事件
        eventWrapper.in("SBBH", relevantDeviceIds); // 过滤相关设备ID
        // 对 KSSJ 字段使用 >= startTime 和 <= endTime 进行日期范围查询
        eventWrapper.between("KSSJ", Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()),
                Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()));

        Long alarmCount = eqpEventMapper.selectCount(eventWrapper);

        return new TotalAlarmCountDTO(alarmCount);
    }

    @Override
    public List<LocationAlarmCountDTO> getLocationAlarmCounts(String timeSpanType) {
        if (timeSpanType == null || timeSpanType.trim().isEmpty()) {
            throw new IllegalArgumentException("时间范围类型 (timeSpanType) 不能为空");
        }

        LocalDateTime[] timeRange = calculateTimeRange(timeSpanType);
        LocalDateTime startTime = timeRange[0];
        LocalDateTime endTime = timeRange[1];

        // 1. 查询符合条件的设备，获取设备ID和点位ID
        QueryWrapper<EqpEquipment> equipmentWrapper = new QueryWrapper<>();
        equipmentWrapper.in("SBLX", Arrays.asList("1", "10", "11", "12", "2", "20", "21", "A1", "A3"));
        equipmentWrapper.eq("SBZT", "0");
        equipmentWrapper.eq("YXBJ", "0");
        equipmentWrapper.select("SBBH", "DWBH");
        List<EqpEquipment> relevantEquipments = eqpEquipmentMapper.selectList(equipmentWrapper);

        if (relevantEquipments.isEmpty()) {
            return List.of(); // 未找到符合条件的设备，返回空列表
        }

        // 将设备按点位编号分组，并收集所有涉及的点位ID
        Map<Integer, List<Integer>> locationDeviceMap = relevantEquipments.stream()
                .filter(eq -> eq.getDwbh() != null && eq.getSbbh() != null)
                .collect(Collectors.groupingBy(EqpEquipment::getDwbh,
                        Collectors.mapping(EqpEquipment::getSbbh, Collectors.toList())));

        // 2. 查询 eqp_location 获取有效点位名称
        List<Integer> potentialLocationIds = new ArrayList<>(locationDeviceMap.keySet());
        if (potentialLocationIds.isEmpty()) {
            return List.of(); // 没有涉及的点位，返回空列表
        }

        QueryWrapper<EqpLocation> locationWrapper = new QueryWrapper<>();
        locationWrapper.in("DWBH", potentialLocationIds);
        locationWrapper.select("DWBH", "DWMC");
        Map<Integer, String> validLocationNameMap = eqpLocationMapper.selectList(locationWrapper).stream()
                .filter(loc -> loc.getDwbh() != null && loc.getDwmc() != null)
                .collect(Collectors.toMap(EqpLocation::getDwbh, EqpLocation::getDwmc));

        if (validLocationNameMap.isEmpty()) {
            return List.of(); // 没有找到有效的点位，返回空列表
        }

        // 过滤设备列表，只保留属于有效点位的设备，并收集有效设备ID
        List<Integer> validDeviceIds = relevantEquipments.stream()
                .filter(eq -> eq.getDwbh() != null && validLocationNameMap.containsKey(eq.getDwbh()))
                .map(EqpEquipment::getSbbh)
                .collect(Collectors.toList());

        if (validDeviceIds.isEmpty()) {
            return List.of(); // 没有有效设备，返回空列表
        }

        // 有效点位到设备ID列表的映射，只包含有效点位和其下的有效设备ID
        Map<Integer, List<Integer>> validLocationDeviceMap = relevantEquipments.stream()
                .filter(eq -> eq.getDwbh() != null && validLocationNameMap.containsKey(eq.getDwbh()))
                .collect(Collectors.groupingBy(EqpEquipment::getDwbh,
                        Collectors.mapping(EqpEquipment::getSbbh, Collectors.toList())));

        // 3. 一次性查询 eqp_event，统计有效设备的报警事件
        QueryWrapper<EqpEvent> eventWrapper = new QueryWrapper<>();
        eventWrapper.eq("SJLX", 3); // 事件类型 3-报警事件
        eventWrapper.in("SBBH", validDeviceIds); // 过滤有效设备ID
        eventWrapper.between("KSSJ", Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()),
                Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()));
        eventWrapper.select("SBBH"); // 只选择设备ID，用于后续分组

        List<EqpEvent> validEvents = eqpEventMapper.selectList(eventWrapper);

        // 在内存中按设备ID统计报警事件数量
        Map<Integer, Long> deviceAlarmCountMap = validEvents.stream()
                .collect(Collectors.groupingBy(EqpEvent::getSbbh, Collectors.counting()));

        List<LocationAlarmCountDTO> resultList = new ArrayList<>();

        // 遍历所有有效点位，构建结果DTO
        for (Map.Entry<Integer, String> locationEntry : validLocationNameMap.entrySet()) {
            Integer locationId = locationEntry.getKey();
            String locationName = locationEntry.getValue();

            // 获取该点位下的所有设备ID
            List<Integer> deviceIdsAtLocation = validLocationDeviceMap.get(locationId);

            Long totalAlarmCountAtLocation = 0L;
            if (deviceIdsAtLocation != null) {
                // 累加该点位下所有设备的报警次数
                totalAlarmCountAtLocation = deviceIdsAtLocation.stream()
                        .mapToLong(deviceId -> deviceAlarmCountMap.getOrDefault(deviceId, 0L))
                        .sum();
            }

            // 添加到结果列表，即使报警次数为0
            resultList.add(new LocationAlarmCountDTO(totalAlarmCountAtLocation, locationName));
        }

        return resultList;
    }

    private LocalDateTime[] calculateTimeRange(String timeSpanType) {
        LocalDate now = LocalDate.now();
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;

        switch (timeSpanType.toUpperCase()) {
            case "YEAR":
                startTime = now.with(TemporalAdjusters.firstDayOfYear()).atStartOfDay();
                endTime = now.with(TemporalAdjusters.lastDayOfYear()).atTime(LocalTime.MAX);
                break;
            case "QUARTER":
                int currentMonth = now.getMonthValue();
                int startMonthOfQuarter = (currentMonth - 1) / 3 * 3 + 1;
                startTime = LocalDate.of(now.getYear(), startMonthOfQuarter, 1).atStartOfDay();
                endTime = startTime.plusMonths(3).minusDays(1).toLocalDate().atTime(LocalTime.MAX);
                break;
            case "MONTH":
                startTime = now.with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
                endTime = now.with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX);
                break;
            case "DAY":
                startTime = now.atStartOfDay();
                endTime = now.atTime(LocalTime.MAX);
                break;
            default:
                // 默认按天统计，或根据需要抛出异常
                startTime = now.atStartOfDay();
                endTime = now.atTime(LocalTime.MAX);
        }

        return new LocalDateTime[] { startTime, endTime };
    }
}