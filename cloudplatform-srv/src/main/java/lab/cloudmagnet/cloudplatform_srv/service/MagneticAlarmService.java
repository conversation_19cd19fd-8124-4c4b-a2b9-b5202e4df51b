package lab.cloudmagnet.cloudplatform_srv.service;

import lab.cloudmagnet.cloudplatform_srv.dto.MagneticAlarmTotalStatsDTO;

import java.util.List;

import lab.cloudmagnet.cloudplatform_srv.dto.MagneticAlarmCountDTO;
import lab.cloudmagnet.cloudplatform_srv.dto.TotalAlarmCountDTO;
import lab.cloudmagnet.cloudplatform_srv.dto.LocationAlarmCountDTO;

public interface MagneticAlarmService {
    /**
     * 获取铁磁设备报警的总数量统计
     *
     * @param timeSpanType 统计的时间范围类型（例如："day", "week", "month", "year"）
     * @param locationIds 可选的点位ID列表
     * @return 包含报警总数量统计数据的DTO
     */
    MagneticAlarmTotalStatsDTO getMagneticAlarmStats(String timeSpanType, List<String> locationIds);
    
    /**
     * 获取铁磁设备报警次数统计
     *
     * @param timeSpanType 统计的时间范围类型（例如："day", "week", "month", "year"）
     * @param locationIds 可选的点位ID列表
     * @return 包含报警次数统计数据的DTO
     */
    MagneticAlarmCountDTO getMagneticAlarmCount(String timeSpanType, List<String> locationIds);

    /**
     * 获取铁磁设备、氧浓度、温湿度传感器报警总次数统计
     *
     * @param timeSpanType 统计的时间范围类型（例如："day", "week", "month", "year"）
     * @param locationIds 可选的点位ID列表
     * @return 包含总报警次数统计数据的DTO
     */
    TotalAlarmCountDTO getTotalAlarmTotalCount(String timeSpanType, List<String> locationIds);

    /**
     * 获取每一个有效点位的铁磁设备、氧浓度、温湿度传感器在指定时间范围内的报警次数
     *
     * @param timeSpanType 统计的时间范围类型（例如："day", "week", "month", "year"）
     * @return 包含每个点位报警次数统计数据的List
     */
    List<LocationAlarmCountDTO> getLocationAlarmCounts(String timeSpanType);
} 