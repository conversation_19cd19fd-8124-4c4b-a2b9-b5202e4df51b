package lab.cloudmagnet.cloudplatform_srv.service.impl;

import lab.cloudmagnet.cloudplatform_srv.dto.AverageEnvironmentalDataDTO;
import lab.cloudmagnet.cloudplatform_srv.dto.EnvironmentalAlarmCountDTO;
import lab.cloudmagnet.cloudplatform_srv.entity.EqpEquipment;
import lab.cloudmagnet.cloudplatform_srv.entity.WostResult;
import lab.cloudmagnet.cloudplatform_srv.entity.EqpEvent;
import lab.cloudmagnet.cloudplatform_srv.mapper.EqpEquipmentMapper;
import lab.cloudmagnet.cloudplatform_srv.mapper.WostResultMapper;
import lab.cloudmagnet.cloudplatform_srv.mapper.EqpEventMapper;
import lab.cloudmagnet.cloudplatform_srv.service.EnvironmentalDataService;
import lab.cloudmagnet.cloudplatform_srv.dto.EnvironmentalTrendDTO;
import lab.cloudmagnet.cloudplatform_srv.dto.HourlyEnvironmentalData;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.Optional;
import java.util.stream.Collectors;
import java.time.ZoneId;
import lab.cloudmagnet.cloudplatform_srv.mapper.WostResultHisMapper;
import lab.cloudmagnet.cloudplatform_srv.entity.WostResultHis;

@Service
public class EnvironmentalDataServiceImpl implements EnvironmentalDataService {

    @Autowired
    private WostResultMapper wostResultMapper;

    @Autowired
    private EqpEquipmentMapper eqpEquipmentMapper;

    @Autowired
    private EqpEventMapper eqpEventMapper;

    @Autowired
    private WostResultHisMapper wostResultHisMapper;

    @Override
    public AverageEnvironmentalDataDTO getAverageLatestEnvironmentalData() {
        // 获取符合条件的设备列表
        QueryWrapper<EqpEquipment> equipmentWrapper = new QueryWrapper<>();
        equipmentWrapper.eq("SBZT", "0"); // 设备状态为0-未连接
        equipmentWrapper.eq("YXBJ", "0"); // 有效标记为0-启用
        equipmentWrapper.in("SBLX", "A1", "A3"); // 设备类型为A1或A3
        List<EqpEquipment> equipmentList = eqpEquipmentMapper.selectList(equipmentWrapper);

        List<Double> oxygenConcentrations = new ArrayList<>();
        List<Double> humidities = new ArrayList<>();
        List<Double> temperatures = new ArrayList<>();

        for (EqpEquipment equipment : equipmentList) {
            Integer equipmentId = equipment.getSbbh();

            // 获取最新的氧气浓度数据 (QTDW = 1)
            QueryWrapper<WostResult> oxygenWrapper = new QueryWrapper<>();
            oxygenWrapper.eq("SBBH", equipmentId);
            oxygenWrapper.eq("QTDW", 1); // 氧气浓度
            oxygenWrapper.orderByDesc("JCSJ", "HMZ").last("LIMIT 1");
            WostResult latestOxygenData = wostResultMapper.selectOne(oxygenWrapper);

            if (latestOxygenData != null && latestOxygenData.getQtld() != null) {
                oxygenConcentrations.add(latestOxygenData.getQtld().doubleValue() / 100.0); // 氧气浓度除以100
                // 暂不处理单位，假设所有设备的单位一致或取第一个设备的单位
            }

            // 获取最新的环境湿度数据 (QTDW = 9)
            QueryWrapper<WostResult> humidityWrapper = new QueryWrapper<>();
            humidityWrapper.eq("SBBH", equipmentId);
            humidityWrapper.eq("QTDW", 9); // 湿度
            humidityWrapper.orderByDesc("JCSJ", "HMZ").last("LIMIT 1");
            WostResult latestHumidityData = wostResultMapper.selectOne(humidityWrapper);

            if (latestHumidityData != null && latestHumidityData.getQtld() != null) {
                try {
                    humidities.add(latestHumidityData.getQtld().doubleValue() / 10.0); // 湿度除以10
                } catch (NumberFormatException e) {
                    // 处理转换错误，忽略此设备的湿度数据
                }
                // 暂不处理单位
            }

            // 获取最新的环境温度数据 (QTDW = 8)
            QueryWrapper<WostResult> temperatureWrapper = new QueryWrapper<>();
            temperatureWrapper.eq("SBBH", equipmentId);
            temperatureWrapper.eq("QTDW", 8); // 温度
            temperatureWrapper.orderByDesc("JCSJ", "HMZ").last("LIMIT 1");
            WostResult latestTemperatureData = wostResultMapper.selectOne(temperatureWrapper);

            if (latestTemperatureData != null && latestTemperatureData.getQtld() != null) {
                try {
                    temperatures.add(latestTemperatureData.getQtld().doubleValue() / 10.0); // 温度除以10
                } catch (NumberFormatException e) {
                    // 处理转换错误，忽略此设备的温度数据
                }
                // 暂不处理单位
            }
        }

        // 计算平均值
        AverageEnvironmentalDataDTO averageDTO = new AverageEnvironmentalDataDTO();
        averageDTO.setAverageOxygenConcentration(calculateAverage(oxygenConcentrations));
        averageDTO.setAverageHumidity(calculateAverage(humidities));
        averageDTO.setAverageTemperature(calculateAverage(temperatures));

        // 确定合适的单位并设置到DTO中
        averageDTO.setOxygenUnit("%");
        averageDTO.setHumidityUnit("%RH");
        averageDTO.setTemperatureUnit("℃");

        return averageDTO;
    }

    @Override
    public EnvironmentalTrendDTO getHourlyEnvironmentalTrend() {
        EnvironmentalTrendDTO trendDTO = new EnvironmentalTrendDTO();
        List<HourlyEnvironmentalData> hourlyDataList = new ArrayList<>();

        // 获取符合条件的设备列表 (同getAverageLatestEnvironmentalData方法)
        QueryWrapper<EqpEquipment> equipmentWrapper = new QueryWrapper<>();
        equipmentWrapper.eq("SBZT", "0"); // 设备状态为0-未连接
        equipmentWrapper.eq("YXBJ", "0"); // 有效标记为0-启用
        equipmentWrapper.in("SBLX", "A1", "A3"); // 设备类型为A1或A3
        List<EqpEquipment> equipmentList = eqpEquipmentMapper.selectList(equipmentWrapper);

        if (equipmentList.isEmpty()) {
            trendDTO.setHourlyData(hourlyDataList); // 没有符合条件的设备，返回空列表
            return trendDTO;
        }

        List<Integer> validEquipmentIds = equipmentList.stream()
                .map(EqpEquipment::getSbbh)
                .collect(Collectors.toList());

        // 获取当天开始时间
        LocalDateTime startOfDay = LocalDateTime.now().truncatedTo(ChronoUnit.DAYS);
        // 获取当前整点时间
        LocalDateTime currentRealTimeHour = LocalDateTime.now().truncatedTo(ChronoUnit.HOURS);

        // 遍历从当天0点到当前整点的小时
        for (LocalDateTime currentHourToProcess = startOfDay; !currentHourToProcess
                .isAfter(currentRealTimeHour); currentHourToProcess = currentHourToProcess
                        .plusHours(1)) {
            int hour = currentHourToProcess.getHour();
            HourlyEnvironmentalData hourlyData = new HourlyEnvironmentalData();
            hourlyData.setHour(hour);

            LocalDateTime startOfQueryHour = currentHourToProcess;
            LocalDateTime endOfQueryHour = currentHourToProcess.plusHours(1);

            List<WostResult> resultsToProcess;

            // 判断是查询实时表还是历史表
            // 如果当前处理的小时 (currentHourToProcess) 早于真实的当前整点 (currentRealTimeHour)
            // 或者如果当前处理的小时等于真实的当前整点，但是我们希望包含当前整点之前到上一整点的数据，也视为历史（或近期）查询，
            // 但更准确的应该是：如果 currentHourToProcess < currentRealTimeHour，则为历史。
            // 如果 currentHourToProcess == currentRealTimeHour，则为当前最新小时的数据，查实时表。

            boolean isHistoricalQuery = currentHourToProcess.isBefore(currentRealTimeHour);

            if (isHistoricalQuery) {
                QueryWrapper<WostResultHis> hourlyWrapperHis = new QueryWrapper<>();
                hourlyWrapperHis.in("SBBH", validEquipmentIds);
                hourlyWrapperHis.in("QTDW", 8, 9); // 8-温度, 9-湿度
                hourlyWrapperHis.between("JCSJ", startOfQueryHour, endOfQueryHour);
                hourlyWrapperHis.orderByAsc("JCSJ", "HMZ");
                List<WostResultHis> hourlyResultsHis = wostResultHisMapper.selectList(hourlyWrapperHis);

                resultsToProcess = hourlyResultsHis.stream()
                        .map(his -> {
                            WostResult current = new WostResult();
                            current.setSbbh(his.getSbbh());
                            current.setQtdw(his.getQtdw());
                            current.setQtld(his.getQtld());
                            current.setJcsj(his.getJcsj());
                            current.setHmz(his.getHmz());
                            // 复制其他公共字段，如果WostResult和WostResultHis有更多共同且需要的字段
                            // 例如: current.setFdbs(his.getFdbs()); current.setGjzt(his.getGjzt());
                            // current.setJbdl(his.getJbdl()); current.setJbdh(his.getJbdh());
                            // current.setRksj(his.getRksj()); current.setTdh(his.getTdh());
                            return current;
                        })
                        .collect(Collectors.toList());

            } else { // 查询实时表 (wost_result)
                QueryWrapper<WostResult> hourlyWrapper = new QueryWrapper<>();
                hourlyWrapper.in("SBBH", validEquipmentIds);
                hourlyWrapper.in("QTDW", 8, 9); // 8-温度, 9-湿度
                hourlyWrapper.between("JCSJ", startOfQueryHour, endOfQueryHour);
                hourlyWrapper.orderByAsc("JCSJ", "HMZ");
                resultsToProcess = wostResultMapper.selectList(hourlyWrapper);
            }

            if (!resultsToProcess.isEmpty()) {
                Optional<WostResult> closestTemperature = findClosestData(resultsToProcess, currentHourToProcess, 8);
                Optional<WostResult> closestHumidity = findClosestData(resultsToProcess, currentHourToProcess, 9);

                closestTemperature.ifPresent(data -> {
                    if (data.getQtld() != null) {
                        hourlyData.setTemperature(data.getQtld().doubleValue() / 10.0);
                    }
                });

                closestHumidity.ifPresent(data -> {
                    if (data.getQtld() != null) {
                        hourlyData.setHumidity(data.getQtld().doubleValue() / 10.0);
                    }
                });
            }

            hourlyDataList.add(hourlyData);
        }

        trendDTO.setHourlyData(hourlyDataList);
        return trendDTO;
    }

    // 辅助方法：计算平均值
    private Double calculateAverage(List<Double> values) {
        if (values == null || values.isEmpty()) {
            return null;
        }
        double sum = values.stream().mapToDouble(Double::doubleValue).sum();
        return sum / values.size();
    }

    /**
     * 辅助方法：在给定的结果列表中查找距离目标时间最近的指定QTDW的数据
     * 
     * @param results    数据结果列表
     * @param targetTime 目标时间（整点）
     * @param qtdw       目标QTDW (8为温度, 9为湿度)
     * @return 距离目标时间最近的数据，Optional包装
     */
    private Optional<WostResult> findClosestData(List<WostResult> results, LocalDateTime targetTime, Integer qtdw) {
        return results.stream()
                .filter(result -> result.getQtdw() != null && result.getQtdw().equals(qtdw))
                .min(Comparator.comparingLong(result -> {
                    // 将JCSJ (Date类型) 转换为 LocalDateTime
                    LocalDateTime dataTime = result.getJcsj().toInstant().atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                    // 如果HMZ存在且有效，可以使用它来提高精度
                    if (result.getHmz() != null && result.getHmz() >= 0 && result.getHmz() < 1000) {
                        dataTime = dataTime.plusNanos(result.getHmz() * 1_000_000L);
                    }
                    return Math.abs(ChronoUnit.MILLIS.between(targetTime, dataTime));
                }));
    }

    @Override
    public EnvironmentalAlarmCountDTO getEnvironmentalAlarmCount(String timeSpanType, String[] locationIds) {
        if (timeSpanType == null || timeSpanType.trim().isEmpty()) {
            throw new IllegalArgumentException("时间范围类型 (timeSpanType) 不能为空");
        }

        LocalDateTime[] timeRange = calculateTimeRange(timeSpanType); // 复用时间计算逻辑
        LocalDateTime startTime = timeRange[0];
        LocalDateTime endTime = timeRange[1];

        QueryWrapper<EqpEquipment> equipmentWrapper = new QueryWrapper<>();
        // 环境传感器设备类型
        equipmentWrapper.in("SBLX", "A", "A1", "A2", "A3", "A4");
        equipmentWrapper.eq("SBZT", "0");
        equipmentWrapper.eq("YXBJ", "0");

        if (locationIds != null && locationIds.length > 0) {
            try {
                List<Integer> locationIdInts = Arrays.stream(locationIds)
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                equipmentWrapper.in("DWBH", locationIdInts);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("无效的点位ID格式，点位ID必须为数字");
            }
        }

        List<Integer> environmentalDeviceIds = eqpEquipmentMapper.selectList(equipmentWrapper)
                .stream()
                .map(EqpEquipment::getSbbh)
                .collect(Collectors.toList());

        EnvironmentalAlarmCountDTO dto = new EnvironmentalAlarmCountDTO();
        if (environmentalDeviceIds.isEmpty()) {
            dto.setAlarmCount(0L);
            return dto;
        }

        QueryWrapper<EqpEvent> eventWrapper = new QueryWrapper<>();

        eventWrapper.eq("SJLX", 3);
        eventWrapper.in("SBBH", environmentalDeviceIds);
        eventWrapper.between("KSSJ", Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()),
                Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()));

        Long alarmCount = eqpEventMapper.selectCount(eventWrapper);
        dto.setAlarmCount(alarmCount);
        return dto;
    }

    // 从 MagneticAlarmServiceImpl 复制并粘贴 calculateTimeRange 方法
    private LocalDateTime[] calculateTimeRange(String timeSpanType) {
        LocalDate now = LocalDate.now();
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;

        switch (timeSpanType.toUpperCase()) {
            case "YEAR":
                startTime = now.with(TemporalAdjusters.firstDayOfYear()).atStartOfDay();
                endTime = now.with(TemporalAdjusters.lastDayOfYear()).atTime(LocalTime.MAX);
                break;
            case "QUARTER":
                int currentMonth = now.getMonthValue();
                int startMonthOfQuarter = (currentMonth - 1) / 3 * 3 + 1;
                startTime = LocalDate.of(now.getYear(), startMonthOfQuarter, 1).atStartOfDay();
                endTime = startTime.plusMonths(3).minusDays(1).toLocalDate().atTime(LocalTime.MAX);
                break;
            case "MONTH":
                startTime = now.with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
                endTime = now.with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX);
                break;
            case "DAY":
                startTime = now.atStartOfDay();
                endTime = now.atTime(LocalTime.MAX);
                break;
            default:
                // 默认按天统计，或根据需要抛出异常
                startTime = now.atStartOfDay();
                endTime = now.atTime(LocalTime.MAX);
        }

        return new LocalDateTime[] { startTime, endTime };
    }
}