package lab.cloudmagnet.cloudplatform_srv.service;

import lab.cloudmagnet.cloudplatform_srv.dto.AverageEnvironmentalDataDTO;
import lab.cloudmagnet.cloudplatform_srv.dto.EnvironmentalTrendDTO;
import lab.cloudmagnet.cloudplatform_srv.dto.EnvironmentalAlarmCountDTO;

public interface EnvironmentalDataService {

    /**
     * 获取所有点位实时的平均氧气浓度和平均环境温度和平均环境湿度
     * 
     * @return 平均环境数据DTO
     */
    AverageEnvironmentalDataDTO getAverageLatestEnvironmentalData();

    /**
     * 获取当天每小时温湿度变化趋势
     * 
     * @return 环境趋势DTO
     */
    EnvironmentalTrendDTO getHourlyEnvironmentalTrend();

    /**
     * 获取环境传感器设备报警次数统计
     *
     * @param timeSpanType 统计的时间范围类型（例如："day", "week", "month", "year"）
     * @param locationIds 可选的点位ID列表
     * @return 包含报警次数统计数据的DTO
     */
    EnvironmentalAlarmCountDTO getEnvironmentalAlarmCount(String timeSpanType, String[] locationIds);
}