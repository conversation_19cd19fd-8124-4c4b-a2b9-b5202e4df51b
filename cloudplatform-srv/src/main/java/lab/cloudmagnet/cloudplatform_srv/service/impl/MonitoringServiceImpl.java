package lab.cloudmagnet.cloudplatform_srv.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lab.cloudmagnet.cloudplatform_srv.dto.LocationMonitoringStatusDTO;
import lab.cloudmagnet.cloudplatform_srv.entity.EqpEquipment;
import lab.cloudmagnet.cloudplatform_srv.entity.EqpLocation;
import lab.cloudmagnet.cloudplatform_srv.mapper.EqpEquipmentMapper;
import lab.cloudmagnet.cloudplatform_srv.mapper.EqpLocationMapper;
import lab.cloudmagnet.cloudplatform_srv.service.MonitoringService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import java.io.StringReader;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

// RTSP检查所需的导入
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URI;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Service
@Slf4j
public class MonitoringServiceImpl implements MonitoringService {

    private final EqpLocationMapper eqpLocationMapper;
    private final EqpEquipmentMapper eqpEquipmentMapper;

    // 定义连接和读取超时时间（毫秒）- 优化超时参数
    private static final int CONNECT_TIMEOUT = 1000; // 1秒连接超时
    private static final int READ_TIMEOUT = 1000;    // 1秒读取超时
    private static final int FUTURE_GET_TIMEOUT = 2000; // 单个摄像头检查任务的future.get()超时
    private static final int OVERALL_TIMEOUT = 15000; // 整体操作超时时间

    public MonitoringServiceImpl(EqpLocationMapper eqpLocationMapper, EqpEquipmentMapper eqpEquipmentMapper) {
        this.eqpLocationMapper = eqpLocationMapper;
        this.eqpEquipmentMapper = eqpEquipmentMapper;
    }

    private static class CameraStatusInfo {
        String rtspUrl;
        boolean isOnline;
        String error; // 用于记录错误信息

        public CameraStatusInfo(String rtspUrl, boolean isOnline, String error) {
            this.rtspUrl = rtspUrl;
            this.isOnline = isOnline;
            this.error = error;
        }
    }

    @Override
    public List<LocationMonitoringStatusDTO> getLocationMonitoringStatus() {
        List<EqpLocation> locations = eqpLocationMapper.selectList(null); // 获取所有点位
        List<LocationMonitoringStatusDTO> dtos = new ArrayList<>();
    
        if (locations == null || locations.isEmpty()) {
            return dtos; // 如果没有点位，返回空列表
        }
    
        // 优化线程池配置 - 使用可用处理器数量的2倍作为线程数
        int numberOfThreads = Math.min(Runtime.getRuntime().availableProcessors() * 2, locations.size());
        ExecutorService executorService = Executors.newFixedThreadPool(numberOfThreads);
    
        try {
            // 使用CompletableFuture替代Future，实现真正的异步处理
            List<CompletableFuture<LocationMonitoringStatusDTO>> futures = new ArrayList<>();
    
            for (EqpLocation location : locations) {
                CompletableFuture<LocationMonitoringStatusDTO> future = CompletableFuture.supplyAsync(
                    () -> processSingleLocation(location), executorService);
                futures.add(future);
            }
    
            // 等待所有Future完成，不会被单个慢任务阻塞
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));
    
            // 设置总体超时
            try {
                allFutures.get(OVERALL_TIMEOUT, TimeUnit.MILLISECONDS);
            } catch (TimeoutException e) {
                log.warn("部分点位处理任务超时");
            } catch (InterruptedException e) {
                log.warn("等待点位处理任务被中断", e);
                Thread.currentThread().interrupt(); // 重新设置中断标志
            } catch (java.util.concurrent.ExecutionException e) {
                log.error("点位处理任务执行异常", e);
            }
    
            // 收集结果（即使部分失败也能获取成功的部分）
            dtos = futures.stream()
                .filter(f -> f.isDone() && !f.isCompletedExceptionally())
                .map(f -> {
                    try {
                        return f.get();
                    } catch (Exception e) {
                        log.error("获取点位监控状态结果异常: {}", e.getMessage(), e);
                        return null;
                    }
                })
                .filter(dto -> dto != null)
                .collect(Collectors.toList());
    
            return dtos;
        } finally {
            // 优雅关闭线程池
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    private LocationMonitoringStatusDTO processSingleLocation(EqpLocation location) {
        LocationMonitoringStatusDTO dto = new LocationMonitoringStatusDTO();
        dto.setLocationId(location.getDwbh());
        dto.setLocationName(location.getDwmc());
        List<String> rtspUrlsForDto = new ArrayList<>();

        QueryWrapper<EqpEquipment> equipmentQueryWrapper = new QueryWrapper<>();
        equipmentQueryWrapper.eq("DWBH", location.getDwbh())
                             .eq("SBLX", "02");
        List<EqpEquipment> cameras = eqpEquipmentMapper.selectList(equipmentQueryWrapper);

        if (cameras == null || cameras.isEmpty()) {
            dto.setMonitoringStatus("无设备");
            dto.setRtspUrls(rtspUrlsForDto); // 空列表
            return dto;
        }

        // 优化线程池配置 - 增加线程数
        int numCamerasForPool = Math.min(cameras.size(), 10); // 增加到最多10个线程
        ExecutorService cameraCheckExecutor = Executors.newFixedThreadPool(numCamerasForPool > 0 ? numCamerasForPool : 1);

        try {
            // 使用CompletableFuture替代Future
            List<CompletableFuture<CameraStatusInfo>> cameraFutures = new ArrayList<>();

            for (EqpEquipment camera : cameras) {
                String rtspUrl = extractRtspUrlFromLjcs(camera.getLjcs());
                if (rtspUrl != null && !rtspUrl.isEmpty()) {
                    rtspUrlsForDto.add(rtspUrl);
                    CompletableFuture<CameraStatusInfo> cameraFuture = CompletableFuture.supplyAsync(() -> {
                        boolean online = isCameraOnline(rtspUrl);
                        return new CameraStatusInfo(rtspUrl, online, online ? null : "连接失败或未收到有效响应");
                    }, cameraCheckExecutor);
                    cameraFutures.add(cameraFuture);
                } else {
                    log.warn("摄像头RTSP URL为空或不存在，设备编号: {} 点位编号: {}", camera.getSbbh(), location.getDwbh());
                }
            }
            dto.setRtspUrls(rtspUrlsForDto);

            // 等待所有摄像头检查完成或超时
            CompletableFuture<Void> allCameraFutures = CompletableFuture.allOf(
                cameraFutures.toArray(new CompletableFuture[0]));

            try {
                // 设置整体超时
                allCameraFutures.get(FUTURE_GET_TIMEOUT * 2, TimeUnit.MILLISECONDS);
            } catch (TimeoutException e) {
                log.warn("部分摄像头检查任务超时，点位: {}", location.getDwbh());
            } catch (InterruptedException e) {
                log.warn("等待摄像头检查任务被中断，点位: {}", location.getDwbh(), e);
                Thread.currentThread().interrupt(); // 重新设置中断标志
            } catch (java.util.concurrent.ExecutionException e) {
                log.error("摄像头检查任务执行异常，点位: {}", location.getDwbh(), e);
            }

            // 收集结果
            long onlineCount = 0;
            long offlineCount = 0;

            for (CompletableFuture<CameraStatusInfo> future : cameraFutures) {
                if (future.isDone() && !future.isCompletedExceptionally()) {
                    try {
                        CameraStatusInfo statusInfo = future.get();
                        if (statusInfo.isOnline) {
                            onlineCount++;
                        } else {
                            offlineCount++;
                            log.warn("摄像头离线或出错: URL {}, 错误: {}", statusInfo.rtspUrl, statusInfo.error);
                        }
                    } catch (Exception e) {
                        offlineCount++;
                        log.error("获取摄像头状态出错: {}", e.getMessage(), e);
                    }
                } else {
                    offlineCount++;
                    log.warn("摄像头检查任务未正常完成");
                }
            }

            int actualCameraCountWithUrls = rtspUrlsForDto.size();

            if (actualCameraCountWithUrls == 0 && !cameras.isEmpty()) { // 数据库中有摄像头记录但没有有效URL
                dto.setMonitoringStatus("配置错误");
            } else if (actualCameraCountWithUrls == 0) {
                dto.setMonitoringStatus("无设备");
            } else if (onlineCount == actualCameraCountWithUrls) {
                dto.setMonitoringStatus("在线");
            } else if (onlineCount > 0) {
                dto.setMonitoringStatus("部分在线");
            } else {
                dto.setMonitoringStatus("离线");
            }
            return dto;
        } finally {
            // 优雅关闭线程池
            cameraCheckExecutor.shutdown();
            try {
                if (!cameraCheckExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cameraCheckExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cameraCheckExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    private String extractRtspUrlFromLjcs(String ljcs) {
        if (ljcs == null || ljcs.isEmpty()) {
            return null;
        }
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            // 禁用DTD验证以防止XXE漏洞和缺少DTD的问题
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            factory.setExpandEntityReferences(false);

            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new InputSource(new StringReader(ljcs)));
            return doc.getElementsByTagName("Url").item(0).getTextContent();
        } catch (Exception e) {
            log.error("解析LJCS XML获取RTSP URL时出错: {}", e.getMessage(), e);
            return null;
        }
    }

    private boolean isCameraOnline(String rtspUrl) {
        if (rtspUrl == null || rtspUrl.isEmpty()) {
            log.warn("RTSP URL为空或不存在，无法检查状态。");
            return false;
        }
        URI uri;
        try {
            uri = new URI(rtspUrl);
        } catch (Exception e) {
            log.error("RTSP URL语法无效: {}", rtspUrl, e);
            return false;
        }

        String host = uri.getHost();
        int port = uri.getPort();
        if (port == -1) {
            port = 554; // 默认RTSP端口
        }
        String path = uri.getPath();
        if (path == null || path.isEmpty()) {
            path = "/";
        }
        String query = uri.getQuery();
        if (query != null && !query.isEmpty()) {
            path += "?" + query;
        }


        try (Socket socket = new Socket()) {
            log.debug("尝试连接RTSP: {} 端口 {} (连接超时: {}毫秒)", host, port, CONNECT_TIMEOUT);
            socket.connect(new InetSocketAddress(host, port), CONNECT_TIMEOUT);
            log.debug("已连接到RTSP: {}:{}. 设置读取超时为 {}毫秒", host, port, READ_TIMEOUT);
            socket.setSoTimeout(READ_TIMEOUT); // 设置读取超时

            try (OutputStream outputStream = socket.getOutputStream();
                 BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {

                String request = "OPTIONS " + rtspUrl + " RTSP/1.0\r\n" + // 使用完整URL
                                 "CSeq: 1\r\n" +
                                 (uri.getUserInfo() != null ? "Authorization: Basic " + java.util.Base64.getEncoder().encodeToString(uri.getUserInfo().getBytes()) + "\r\n" : "") +
                                 "User-Agent: CloudPlatform-Srv RTSP Check\r\n" +
                                 "\r\n";

                log.debug("发送RTSP OPTIONS请求到 {}:{}: {}", host, port, request.replaceAll("\r\n", ""));
                outputStream.write(request.getBytes());
                outputStream.flush();

                StringBuilder responseBuilder = new StringBuilder();
                String line;
                long startTime = System.currentTimeMillis();
                // 读取响应时也应有超时逻辑，socket.setSoTimeout() 会在这里生效
                while ((line = reader.readLine()) != null) {
                    responseBuilder.append(line).append("");
                    if (line.isEmpty()) { // 响应头结束
                        break;
                    }
                    if (System.currentTimeMillis() - startTime > READ_TIMEOUT) { // 双重保险
                        log.warn("RTSP读取超时（手动检查）URL: {}", rtspUrl);
                        return false;
                    }
                }
                String response = responseBuilder.toString();
                String firstLineOfResponse = response.contains("\n") ? response.substring(0, response.indexOf("\n")) : response;
                log.debug("从 {}:{} 接收到RTSP响应: {}", host, port, firstLineOfResponse.replace("\r", "")); // 移除\r以获得更清晰的日志

                if (response.startsWith("RTSP/1.0 200 OK") || response.startsWith("RTSP/1.0 401 Unauthorized")) {
                    log.info("摄像头在线（200 OK或401 Unauthorized）URL: {}", rtspUrl);
                    return true;
                } else {
                    log.warn("摄像头检查失败 URL: {}. 响应: {}", rtspUrl, firstLineOfResponse.replace("\r", ""));
                    return false;
                }
            }
        } catch (java.net.SocketTimeoutException e) {
            log.warn("RTSP连接或读取超时 {}:{}. URL: {}. 错误: {}", host, port, rtspUrl, e.getMessage());
            return false;
        } catch (java.io.IOException e) {
            log.warn("RTSP IO异常 {}:{}. URL: {}. 错误: {}", host, port, rtspUrl, e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("检查RTSP时发生意外错误 {}:{}. URL: {}. 错误: {}", host, port, rtspUrl, e.getMessage(), e);
            return false;
        }
    }
}