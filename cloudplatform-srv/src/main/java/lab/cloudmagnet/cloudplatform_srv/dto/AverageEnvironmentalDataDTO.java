package lab.cloudmagnet.cloudplatform_srv.dto;

import lombok.Data;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

@Data
@Schema(description = "平均环境数据DTO")
public class AverageEnvironmentalDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    // 平均氧气浓度
    @Schema(description = "平均氧气浓度")
    private Double averageOxygenConcentration;
    @Schema(description = "氧气单位")
    private String oxygenUnit;

    // 平均环境湿度
    @Schema(description = "平均环境湿度")
    private Double averageHumidity;
    @Schema(description = "湿度单位")
    private String humidityUnit;

    // 平均环境温度
    @Schema(description = "平均环境温度")
    private Double averageTemperature;
    @Schema(description = "温度单位")
    private String temperatureUnit;
} 