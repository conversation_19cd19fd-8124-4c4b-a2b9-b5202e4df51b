package lab.cloudmagnet.cloudplatform_srv.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

@Data
@Schema(description = "每小时环境数据DTO")
public class HourlyEnvironmentalData {
    @Schema(description = "小时 (0-23)")
    private int hour; // 小时 (0-23)
    @Schema(description = "环境温度")
    private Double temperature; // 环境温度
    @Schema(description = "环境湿度")
    private Double humidity; // 环境湿度
}