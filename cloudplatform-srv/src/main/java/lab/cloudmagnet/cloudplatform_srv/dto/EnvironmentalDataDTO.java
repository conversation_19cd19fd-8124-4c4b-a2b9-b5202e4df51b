package lab.cloudmagnet.cloudplatform_srv.dto;

import lombok.Data;

import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

@Data
@Schema(description = "环境数据DTO")
public class EnvironmentalDataDTO {
    // 氧气浓度
    @Schema(description = "氧气浓度")
    private Double oxygenConcentration;
    @Schema(description = "氧气单位")
    private String oxygenUnit;

    // 环境湿度
    @Schema(description = "环境湿度")
    private Double humidity;
    @Schema(description = "湿度单位")
    private String humidityUnit;

    // 环境温度
    @Schema(description = "环境温度")
    private Double temperature;
    @Schema(description = "温度单位")
    private String temperatureUnit;

    // 数据采集时间
    @Schema(description = "数据采集时间")
    private Date timestamp;

    // 可能需要的设备信息等
    @Schema(description = "设备ID")
    private Integer equipmentId;
    @Schema(description = "设备名称")
    private String equipmentName;
} 