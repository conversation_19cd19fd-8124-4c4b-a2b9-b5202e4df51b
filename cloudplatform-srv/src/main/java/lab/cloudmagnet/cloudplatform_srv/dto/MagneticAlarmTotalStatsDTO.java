package lab.cloudmagnet.cloudplatform_srv.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

@Data
@Schema(description = "铁磁设备报警总数量统计DTO")
public class MagneticAlarmTotalStatsDTO {
    @Schema(description = "无风险（安全）报警数量")
    private Long safeCount; // 无风险（安全）
    @Schema(description = "低风险报警数量")
    private Long lowRiskCount; // 低风险
    @Schema(description = "中风险报警数量")
    private Long mediumRiskCount; // 中风险
    @Schema(description = "高风险报警数量")
    private Long highRiskCount; // 高风险
} 