package lab.cloudmagnet.cloudplatform_srv.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "点位报警次数统计DTO")
public class LocationAlarmCountDTO {
    @Schema(description = "报警次数")
    private Long alarmCount;
    @Schema(description = "点位名称")
    private String locationName;
}