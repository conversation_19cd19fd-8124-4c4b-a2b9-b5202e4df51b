package lab.cloudmagnet.cloudplatform_srv.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "当前天气和气温数据DTO")
public class CurrentWeatherDTO {

    @Schema(description = "天气描述")
    private String weather;

    @Schema(description = "气温 (℃)")
    private String temperature;
} 