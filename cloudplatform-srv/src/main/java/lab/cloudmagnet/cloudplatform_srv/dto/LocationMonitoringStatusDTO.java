package lab.cloudmagnet.cloudplatform_srv.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "点位监控状态信息DTO，包含该点位下所有摄像头的RTSP URL列表")
public class LocationMonitoringStatusDTO {

    @Schema(description = "点位ID (对应数据库中 eqp_location.DWBH)", example = "101")
    private Integer locationId;

    @Schema(description = "点位名称 (对应数据库中 eqp_location.DWMC)", example = "MR扫描室1")
    private String locationName;

    @Schema(description = "点位监控状态", example = "在线")
    private String monitoringStatus;

    @Schema(description = "该点位下所有摄像头设备的RTSP URL列表", example = "[\"rtsp://...\"]")
    private List<String> rtspUrls;
} 