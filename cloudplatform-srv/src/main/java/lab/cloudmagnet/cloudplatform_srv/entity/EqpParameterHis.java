package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备参数史表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_parameter_his")
public class EqpParameterHis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @TableId("SBBH")
    private Integer sbbh;

    /**
     * 检测项目
     */
    private Integer jcxm;

    /**
     * 检测时间
     */
    @TableId("JCSJ")
    private Date jcsj;

    /**
     * 毫秒值
     */
    private Integer hmz;

    /**
     * 检测值
     */
    private String jcz;

    /**
     * 补充标记 0-记录 1-历史
     */
    private String bcbj;

    /**
     * 入库时间
     */
    private Date rksj;

} 