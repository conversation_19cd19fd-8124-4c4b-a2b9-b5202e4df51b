package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备参数设置表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_parameter")
public class EqpParameter implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @TableId("SBBH")
    private Integer sbbh;

    /**
     * 参数编号
     */
    private Integer csbh;

    /**
     * 参数名称
     */
    private String csmc;

    /**
     * 参数说明
     */
    private String cssm;

    /**
     * 记录标记 0-不记录 1-记录
     */
    private String jlbj;

    /**
     * 设置值 空为未设置需初始值填充
     */
    private String szz;

    /**
     * 检测值
     */
    private String jcz;

    /**
     * 检测时间
     */
    private Date jcsj;

    /**
     * 毫秒值
     */
    private Integer hmz;

    /**
     * 入库时间
     */
    private Date rksj;

} 