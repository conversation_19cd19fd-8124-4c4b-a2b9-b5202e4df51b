package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 铁磁四代设备监测结果表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("mag4_result")
public class Mag4Result implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @TableId("SBBH")
    private Integer sbbh;

    /**
     * A探头1的磁场值
     */
    private Integer ma1;

    /**
     * A探头2的磁场值
     */
    private Integer ma2;

    /**
     * A探头3的磁场值
     */
    private Integer ma3;

    /**
     * A探头4的磁场值
     */
    private Integer ma4;

    /**
     * A探头5的磁场值
     */
    private Integer ma5;

    /**
     * B探头1的磁场值
     */
    private Integer mb1;

    /**
     * B探头2的磁场值
     */
    private Integer mb2;

    /**
     * B探头3的磁场值
     */
    private Integer mb3;

    /**
     * B探头4的磁场值
     */
    private Integer mb4;

    /**
     * B探头5的磁场值
     */
    private Integer mb5;

    /**
     * A柱铁磁干扰值
     */
    private Integer idxa;

    /**
     * B柱铁磁干扰值
     */
    private Integer idxb;

    /**
     * A柱灯颜色
     */
    private String cola;

    /**
     * B柱灯颜色
     */
    private String colb;

    /**
     * A柱灯位置
     */
    private Integer posa;

    /**
     * B柱灯位置
     */
    private Integer posb;

    /**
     * 1:代表开门，如果为0代表关门
     */
    private Integer door;

    /**
     * 代表闯入窗口延时进行到了800，如果为0表示闯入结束
     */
    private Integer ckyc;

    /**
     * 通道防护工作模式
     */
    private Integer a2s;

    /**
     * 检测时间
     */
    private Date jcsj;

    /**
     * 毫秒值
     */
    private Integer hmz;

    /**
     * 入库时间
     */
    private Date rksj;

} 