package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 云磁点位职守表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_location_post")
public class EqpLocationPost implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录序号
     */
    @TableId("JLXH")
    private Integer jlxh;

    /**
     * 点位编号
     */
    private Integer dwbh;

    /**
     * 人员编号
     */
    private Integer rybh;

    /**
     * 职守状态 0-职守 1-离开
     */
    private String zsst;

    /**
     * 开始时间
     */
    private Date zssj;

    /**
     * 结束时间
     */
    private Date jssj;

} 