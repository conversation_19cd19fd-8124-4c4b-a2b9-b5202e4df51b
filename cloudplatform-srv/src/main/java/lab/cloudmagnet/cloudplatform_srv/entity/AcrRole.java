package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色定义表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("acr_role")
public class AcrRole implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * JSBM
     */
    @TableId("JSBM")
    private Integer jsbm;

    private String jsmc;

    private String jsms;

    private String kjxz;

    private Date gxsj;

    private Date rksj;

} 