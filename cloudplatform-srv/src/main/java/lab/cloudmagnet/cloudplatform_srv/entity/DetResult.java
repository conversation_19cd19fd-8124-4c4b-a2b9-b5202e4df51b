package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * AI智能识别大物体结果表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("det_result")
public class DetResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @TableId("SBBH")
    private Long sbbh;

    /**
     * 检测序列
     */
    private Long jcxl;

    /**
     * 检测结果 0-未发现 1-发现目标物体
     */
    private Boolean jcjg;

    /**
     * 检测时间
     */
    private Date jcsj;

    /**
     * 毫秒值
     */
    private Integer hmz;

    /**
     * 入库时间
     */
    private Date rksj;

} 