package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * 接口访问统计表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("int_stat")
public class IntStat implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("JKBH")
    private String jkbh;

    private String yhmc;

    private String sjbj;

    private Integer fwcs;

    private Integer clcs;

} 