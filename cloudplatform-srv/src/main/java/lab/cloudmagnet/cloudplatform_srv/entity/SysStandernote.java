package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 标准字典表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("sys_standernote")
public class SysStandernote implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ZDLX
     */
    @TableId("ZDLX")
    private String zdlx;

    /**
     * DMZ
     */
    private String dmz;

    private String dmms;

    private String dmsm;

    private String fjd;

    private String zt;

    private Date gxsj;

    private Date rksj;

} 