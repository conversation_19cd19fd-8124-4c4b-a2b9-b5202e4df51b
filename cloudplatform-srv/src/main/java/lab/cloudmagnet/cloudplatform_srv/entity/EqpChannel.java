package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 通讯通道表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_channel")
public class EqpChannel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Integer fwbh;

    private Integer tdbh;

    private String tdmc;

    private Integer dwbh;

    private String zkbj;

    private String txzt;

    private Date gxsj;

} 