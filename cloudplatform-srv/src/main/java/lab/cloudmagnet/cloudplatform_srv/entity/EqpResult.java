package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备监测结果表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_result")
public class EqpResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @TableId("SBBH")
    private Integer sbbh;

    /**
     * 检测项目
     */
    private Integer jcxm;

    /**
     * 项目名称
     */
    private String xmmc;

    /**
     * 项目说明
     */
    private String xmsm;

    /**
     * 记录标记 0-不记录 1-记录
     */
    private String jlbj;

    /**
     * 检测值
     */
    private String jcz;

    /**
     * 检测时间
     */
    private Date jcsj;

    /**
     * 毫秒值
     */
    private Integer hmz;

    /**
     * 入库时间
     */
    private Date rksj;

} 