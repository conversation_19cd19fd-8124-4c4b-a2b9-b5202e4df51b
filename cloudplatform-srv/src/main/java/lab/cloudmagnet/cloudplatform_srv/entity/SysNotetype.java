package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 字典类型定义表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("sys_notetype")
public class SysNotetype implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ZDLX
     */
    @TableId("ZDLX")
    private String zdlx;

    private String zdms;

    private String zdsm;

    private Date gxsj;

    private Date rksj;

} 