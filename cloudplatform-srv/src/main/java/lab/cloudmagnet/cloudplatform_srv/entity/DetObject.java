package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * det_object
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("det_object")
public class DetObject implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * det_object id
     */
    @TableId("id")
    private Long id;

    private Long detInfoId;

    /**
     * class_name id
     */
    private Long classId;

    /**
     * 摄像机画面中的坐标
     */
    private String coordinate;

} 