package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作日志
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("sys_operatelog")
public class SysOperatelog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("CZSJ")
    private Date czsj;

    private Integer czry;

    private String czlx;

    private String sjzy;

    private String fzxx;

    private String czjg;

} 