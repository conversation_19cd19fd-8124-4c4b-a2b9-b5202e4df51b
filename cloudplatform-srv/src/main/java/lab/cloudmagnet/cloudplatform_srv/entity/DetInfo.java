package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * det_info
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("det_info")
public class DetInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * det_info id
     */
    @TableId("id")
    private Long id;

    /**
     * 摄像头id
     */
    private Long cameraId;

    private Boolean haveSomething;

    /**
     * 图片
     */
    private byte[] pic;

    /**
     * 检测时间
     */
    private Date detTime;

} 