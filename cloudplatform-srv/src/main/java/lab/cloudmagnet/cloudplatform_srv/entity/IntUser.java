package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 访问用户备案表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("int_user")
public class IntUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("YHMC")
    private String yhmc;

    private String sydw;

    private String fwxl;

    private String dzxz;

    private Date gxsj;

    private Date rksj;

    private String bz;

} 