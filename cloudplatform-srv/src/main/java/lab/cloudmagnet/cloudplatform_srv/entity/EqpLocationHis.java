package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 云磁点位历史表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_location_his")
public class EqpLocationHis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 点位编号
     */
    @TableId("DWBH")
    private Integer dwbh;

    /**
     * 点位名称
     */
    private String dwmc;

    /**
     * 点位位置
     */
    private String dwwz;

    /**
     * 管理部门
     */
    private String glbm;

    /**
     * 通讯服务地址
     */
    private String txfw;

    /**
     * 通讯服务端口 语音服务端口号
     */
    private Integer txdk;

    /**
     * 更新时间
     */
    private Date gxsj;

    /**
     * 入库时间
     */
    private Date rksj;

    /**
     * 删除时间
     */
    private Date scsj;

} 