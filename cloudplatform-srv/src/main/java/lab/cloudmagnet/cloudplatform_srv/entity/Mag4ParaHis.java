package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 铁磁四代设备参数监测历史表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("mag4_para_his")
public class Mag4ParaHis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @TableId("SBBH")
    private Integer sbbh;

    /**
     * 设备型号
     */
    private String sbxh;

    /**
     * 设备角色
     */
    private String sbjs;

    /**
     * 红灯阈值B
     */
    private Integer yzra;

    /**
     * 红灯阈值B
     */
    private Integer yzrb;

    /**
     * 闯入窗口控制 毫秒
     */
    private Integer ckkz;

    /**
     * 报警延迟时间 毫秒
     */
    private Integer bjyc;

    /**
     * 中等铁磁物质报警阈值
     */
    private Integer yzmi;

    /**
     * 大型铁磁物质报警阈值
     */
    private Integer yzla;

    /**
     * 检测时间
     */
    @TableId("JCSJ")
    private Date jcsj;

    /**
     * 毫秒值
     */
    @TableId("HMZ")
    private Integer hmz;

    /**
     * 入库时间
     */
    private Date rksj;

} 