package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统任务执行时间记录表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_location_point")
public class EqpLocationPoint implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * DWBH
     */
    @TableId("DWBH")
    private Integer dwbh;

    /**
     * RWMC
     */
    private String rwmc;

    private Date zxsj;

    private Date scsj;

} 