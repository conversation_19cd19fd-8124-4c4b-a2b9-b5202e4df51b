package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 接口授权历史表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("int_accredit_his")
public class IntAccreditHis implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("JKBH")
    private String jkbh;

    private String yhmc;

    private Date sqsj;

    private Integer rxzl;

    private Date jcsj;

} 