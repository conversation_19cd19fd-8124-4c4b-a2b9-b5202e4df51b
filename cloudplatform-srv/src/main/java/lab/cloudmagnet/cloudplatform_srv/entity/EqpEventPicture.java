package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备警报图片表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_event_picture")
public class EqpEventPicture implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 像机编号
     */
    @TableId("SBBH")
    private Integer sbbh;

    /**
     * 检测时间
     */
    private Date jcsj;

    /**
     * 毫秒值
     */
    private Integer hmz;

    /**
     * 图片
     */
    private byte[] tp;

    /**
     * 入库时间
     */
    private Date rksj;

} 