package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 铁磁设备参数监测历史表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("mag_para_his")
public class MagParaHis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @TableId("SBBH")
    private Integer sbbh;

    /**
     * 设备角色
     */
    private String sbjs;

    /**
     * 黄灯报警阈值
     */
    private Integer ydyz;

    /**
     * 红灯报警阈值
     */
    private Integer rdyz;

    /**
     * 剩余电池电量
     */
    private Integer dcdl;

    /**
     * 闯入窗口控制 毫秒
     */
    private Integer ckkz;

    /**
     * 报警延迟时间 毫秒
     */
    private Integer bjyc;

    /**
     * 预留
     */
    private Integer ylzd;

    /**
     * 语音功放音量调节
     */
    private Integer yygf;

    /**
     * 蜂鸣器报警音量调节
     */
    private Integer bjyl;

    /**
     * LED指示灯亮度调节
     */
    private Integer zsdl;

    /**
     * 设备算法选择
     */
    private Integer sbsf;

    /**
     * 语音报警语种选择
     */
    private Integer yyxz;

    /**
     * 检测时间
     */
    @TableId("JCSJ")
    private Date jcsj;

    /**
     * 毫秒值
     */
    @TableId("HMZ")
    private Integer hmz;

    /**
     * 入库时间
     */
    private Date rksj;

} 