package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 云磁点位服务情况表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_location_log")
public class EqpLocationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 点位编号
     */
    @TableId("DWBH")
    private Integer dwbh;

    /**
     * 管理服务地址
     */
    private String glfw;

    /**
     * 管理服务端口
     */
    private Integer gldk;

    /**
     * 启动时间
     */
    private Date qdsj;

    /**
     * 心跳保持时间
     */
    private Date xtsj;

    /**
     * 冲突次数
     */
    private Integer ctcs;

    /**
     * 点位状态 0-未启动 1-已启动
     */
    private String dwzt;

} 