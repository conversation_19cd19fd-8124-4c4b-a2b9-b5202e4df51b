package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 接口定义表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("int_define")
public class IntDefine implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("JKBH")
    private String jkbh;

    private String xsmc;

    private String jksm;

    private String kjxz;

    private Date gxsj;

    private Date rksj;

} 