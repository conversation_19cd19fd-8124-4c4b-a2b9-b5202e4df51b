package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 运行日志
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("sys_runlog")
public class SysRunlog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * RZXL
     */
    @TableId("RZXL")
    private Integer rzxl;

    /**
     * YXSJ
     */
    private Date yxsj;

    /**
     * XXLB
     */
    private String xxlb;

    /**
     * XXLR
     */
    private String xxlr;

    /**
     * FZXX
     */
    private String fzxx;

    /**
     * KHDZ
     */
    private String khdz;

} 