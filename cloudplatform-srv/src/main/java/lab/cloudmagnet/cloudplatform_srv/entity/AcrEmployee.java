package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户信息表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("acr_employee")
public class AcrEmployee implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 人员编号
     */
    @TableId("RYBH")
    private Integer rybh;

    /**
     * 人员姓名
     */
    private String ryxm;

    /**
     * 性别 1-男 2-女
     */
    private String xb;

    /**
     * 职务
     */
    private String zw;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 工作编号
     */
    private String gzbh;

    /**
     * 部门编码
     */
    private String bmbm;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 内部排序
     */
    private Integer nbpx;

    /**
     * 登陆用户
     */
    private String dlyh;

    /**
     * 登陆密码
     */
    private String dlmm;

    /**
     * 人员状态
     */
    private String ryzt;

    /**
     * 更新时间
     */
    private Date gxsj;

    /**
     * 入库时间
     */
    private Date rksj;

} 