package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 报表输出表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("rep_item")
public class RepItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("BBXH")
    private String bbxh;

    private Integer scbh;

    private String xsmc;

    private String scms;

    private String sjly;

    private Integer sjks;

    private Integer sjjs;

    private Integer myhs;

    private Integer mhls;

    private String hjbj;

    private String hbbj;

    private Date gxsj;

    private Date rksj;

} 