package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备警报事件表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_event")
public class EqpEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 事件编号
     */
    @TableId("SJXH")
    private Integer sjxh;

    /**
     * 点位编号
     */
    private Integer dwbh;

    /**
     * 设备编号
     */
    private Integer sbbh;

    /**
     * 检测设备
     */
    private Integer jcsb;

    /**
     * 事件类型 1-预警事件 2-一般事件 3-报警事件
     */
    private Integer sjlx;

    /**
     * 事件级别 1-低风险事件 2-中风险事件 3-高风险事件
     */
    private Integer sjjb;

    /**
     * 开始时间
     */
    private Date kssj;

    /**
     * 毫秒值
     */
    private Integer kshm;

    /**
     * 结束时间
     */
    private Date jssj;

    /**
     * 毫秒值
     */
    private Integer jshm;

    /**
     * 处理标记 0-未处理 1-有效事件 2-无效事件 3-作废事件
     */
    private String clbj;

    /**
     * 处理结果
     */
    private String cljg;

    /**
     * 处理人员
     */
    private Integer clry;

    /**
     * 处理时间
     */
    private Date clsj;

    /**
     * 归档标记
     */
    private String gdbj;

    /**
     * 归档人员
     */
    private Integer gdry;

    /**
     * 归档时间
     */
    private Date gdsj;

    /**
     * 入库时间
     */
    private Date rksj;

} 