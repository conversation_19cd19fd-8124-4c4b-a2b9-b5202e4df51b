package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 授权信息表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("acr_accredit")
public class AcrAccredit implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private String dxxh;

    private String ztxh;

    private String sqlb;

    private Date rksj;

} 