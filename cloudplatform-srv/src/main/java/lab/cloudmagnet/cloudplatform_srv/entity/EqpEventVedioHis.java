package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备警报视频表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_event_vedio_his")
public class EqpEventVedioHis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @TableId("SBBH")
    private Integer sbbh;

    /**
     * 开始时间
     */
    private Date kssj;

    /**
     * 毫秒值
     */
    @TableId("KSHM")
    private Integer kshm;

    /**
     * 结束时间
     */
    private Date jssj;

    /**
     * 毫秒值
     */
    private Integer jshm;

    /**
     * 视频地址
     */
    private String spdz;

    /**
     * 入库时间
     */
    private Date rksj;

    /**
     * 清理类型
     */
    private String qllx;

    /**
     * 清理时间
     */
    private Date qlsj;

} 