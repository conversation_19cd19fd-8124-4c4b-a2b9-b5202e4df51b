package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备类型备案表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_type")
public class EqpType implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("SBLX")
    private Integer sblx;

    private String lxmc;

    /**
     * 连接方式 0-被动连接 1-网络连接 2-COM连接
     */
    private String ljfs;

    private String csxh;

    private String glcs;

    private String sbcs;

    private String ljcs;

    private String xsmb;

    private Date gxsj;

    private Date rksj;

} 