package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 铁磁设备监测结果表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("mag_result")
public class MagResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @TableId("SBBH")
    private Integer sbbh;

    /**
     * 探头1的磁场值
     */
    private Integer ccz1;

    /**
     * 探头2的磁场值
     */
    private Integer ccz2;

    /**
     * 探头3的磁场值
     */
    private Integer ccz3;

    /**
     * 探头4的磁场值
     */
    private Integer ccz4;

    /**
     * 探头5的磁场值
     */
    private Integer ccz5;

    /**
     * 铁磁质信号强度
     */
    private Integer xhqd;

    /**
     * 指示灯状况
     */
    private Integer zsdz;

    /**
     * 状态值
     */
    private Integer jczt;

    /**
     * 门开关状态
     */
    private Integer door;

    /**
     * 报警状态
     */
    private Integer jbbz;

    /**
     * 进入状态
     */
    private Integer ryjc;

    /**
     * 检测时间
     */
    private Date jcsj;

    /**
     * 毫秒值
     */
    private Integer hmz;

    /**
     * 入库时间
     */
    private Date rksj;

} 