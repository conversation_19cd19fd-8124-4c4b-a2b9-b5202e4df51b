package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 报表定义表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("rep_define")
public class RepDefine implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("BBXH")
    private String bbxh;

    private String bbmc;

    private String sjly;

    private Integer btks;

    private Integer btjs;

    private String wjmc;

    private String wjlx;

    private String wjmb;

    private Date gxsj;

    private Date rksj;

} 