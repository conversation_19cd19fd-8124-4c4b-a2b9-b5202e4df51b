package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 功能定义表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("acr_function")
public class AcrFunction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 功能编码
     */
    @TableId("GNBM")
    private String gnbm;

    private String gnmc;

    private String gnms;

    private String gnlx;

    private String gldz;

    private String kjxz;

    private Date rksj;

} 