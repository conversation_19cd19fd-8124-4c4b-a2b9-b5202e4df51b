package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 部门信息表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("acr_department")
public class AcrDepartment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门编码
     */
    @TableId
    private String bmbm;

    private String bmmc;

    private String yjdz;

    private String lxdh;

    private String lxr;

    private Date gxsj;

    private Date rksj;

} 