package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 接口访问日志表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("int_log")
public class IntLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("RZXH")
    private Integer rzxh;

    @TableId("JKBH")
    private String jkbh;

    @TableId("YHMC")
    private String yhmc;

    private String qqtj;

    private String qqdz;

    @TableId("QQSJ")
    private Date qqsj;

    private String fhjg;

    private String fhbj;

    private Date fhsj;

} 