package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 接口输出表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("int_item")
public class IntItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("JKBH")
    private String jkbh;

    private Integer scbh;

    private String xsmc;

    private String scms;

    private String sjly;

    private String jdmc;

    private Date gxsj;

    private Date rksj;

} 