package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 云磁设备历史表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_equipment_his")
public class EqpEquipmentHis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @TableId("SBBH")
    private Integer sbbh;

    /**
     * 点位编号
     */
    private Integer dwbh;

    /**
     * 设备名称
     */
    private String sbmc;

    /**
     * 设备序列号
     */
    private String sbxlh;

    /**
     * 安装位置
     */
    private String azwz;

    /**
     * 设备类型
     */
    private String sblx;

    /**
     * 生产厂商
     */
    private String sccs;

    /**
     * 设备品牌
     */
    private String sbpp;

    /**
     * 设备型号
     */
    private String sbxh;

    /**
     * 合格证书
     */
    private String hgzs;

    /**
     * 检修日期
     */
    private Date jxrq;

    /**
     * 责任人
     */
    private String zrr;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 管理设备
     */
    private Integer glsb;

    /**
     * 管理参数
     */
    private String glcs;

    /**
     * 设备参数
     */
    private String sbcs;

    /**
     * 连接参数
     */
    private String ljcs;

    /**
     * 设备状态 0-未连接 1-服务中 2-警报中
     */
    private String sbzt;

    /**
     * 有效标记 0-启用 1-临时 2-停用
     */
    private String yxbj;

    /**
     * 连接方式 0-被动连接 1-主动SOCKET 2-主动COM
     */
    private String ljfs;

    /**
     * 内部排序
     */
    private Integer nbpx;

    /**
     * 更新时间
     */
    private Date gxsj;

    /**
     * 入库时间
     */
    private Date rksj;

    /**
     * 删除时间
     */
    private Date scsj;

} 