package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 通讯服务情况表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("eqp_communication_log")
public class EqpCommunicationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * FWBH
     */
    @TableId("FWBH")
    private Integer fwbh;

    private String glfw;

    private Integer gldk;

    private Date qdsj;

    private Date xtsj;

    private Integer ctcs;

    private String fwzt;

} 