package lab.cloudmagnet.cloudplatform_srv.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 氧气浓度监测结果表
 * <AUTHOR> @since 2024-05-20
 */
@Data
@TableName("wost_result")
public class WostResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @TableId("SBBH")
    private Integer sbbh;

    /**
     * TDH
     */
    private Integer tdh;

    /**
     * 气体浓度
     */
    private Integer qtld;

    /**
     * 告警状态
     */
    private Integer gjzt;

    /**
     * 浓度值放大倍数
     */
    private Integer fdbs;

    /**
     * 气体单位
     */
    private Integer qtdw;

    /**
     * 气体低报点
     */
    private Integer jbdl;

    /**
     * 气体高报点
     */
    private Integer jbdh;

    /**
     * 检测时间
     */
    private Date jcsj;

    /**
     * 毫秒值
     */
    private Integer hmz;

    /**
     * 入库时间
     */
    private Date rksj;

} 