package lab.cloudmagnet.cloudplatform_srv.schedule;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Component
public class WeatherUpdateTask {

    private static final Logger logger = LoggerFactory.getLogger(WeatherUpdateTask.class);

    @Value("${weather.api.appcode}")
    private String appcode;

    @Value("${weather.api.areaCode:320104}") // 使用默认值以防未配置
    private String areaCode;

    @Autowired
    private StringRedisTemplate redisTemplate;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    private static final String THIRD_PARTY_API_HOST = "https://ali-weather.showapi.com";
    private static final String THIRD_PARTY_API_PATH = "/hour24";
    private static final int MAX_RETRIES = 3;
    private static final long CACHE_TTL_SECONDS = TimeUnit.MINUTES.toSeconds(750); // 12小时30分钟
    private static final String REDIS_KEY_PREFIX = "weather:forecast:";

    // 每12小时执行一次
    // @Scheduled(fixedRate = 1000 * 60 * 60 * 12)
    public void updateWeatherData() {
        logger.info("天气数据更新任务开始，区域代码: {}", areaCode);

        String weatherDataJson = null;
        int retryCount = 0;

        while (retryCount < MAX_RETRIES) {
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.set("Authorization", "APPCODE " + appcode);

                UriComponentsBuilder builder = UriComponentsBuilder
                        .fromUriString(THIRD_PARTY_API_HOST + THIRD_PARTY_API_PATH)
                        .queryParam("areaCode", areaCode);

                HttpEntity<String> entity = new HttpEntity<>(headers);

                logger.debug("调用第三方天气API: {}", builder.toUriString());
                ResponseEntity<String> response = restTemplate.exchange(
                        builder.toUriString(),
                        HttpMethod.GET,
                        entity,
                        String.class);

                if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                    weatherDataJson = response.getBody();
                    logger.info("成功从第三方API获取天气数据。");
                    break; // 成功，退出重试循环
                } else {
                    logger.warn("第三方API调用失败，状态码: {}", response.getStatusCode());
                }

            } catch (Exception e) {
                logger.error("调用第三方天气API出错 (尝试 {}/{}}): {}", retryCount + 1, MAX_RETRIES, e.getMessage());
            }
            retryCount++;
            // 可选：重试前添加短暂延迟
            // try { Thread.sleep(1000); } catch (InterruptedException ie) {
            // Thread.currentThread().interrupt(); }
        }

        if (weatherDataJson != null) {
            try {
                JsonNode root = objectMapper.readTree(weatherDataJson);
                JsonNode hourList = root.path("showapi_res_body").path("hourList");

                if (hourList.isArray()) {
                    String redisKey = REDIS_KEY_PREFIX + areaCode;
                    // 将 hourList 节点转回 JSON 字符串存储
                    String hourListJsonString = objectMapper.writeValueAsString(hourList);
                    redisTemplate.opsForValue().set(redisKey, hourListJsonString, CACHE_TTL_SECONDS, TimeUnit.SECONDS);
                    logger.info("成功将 hourList 数据存储到 Redis，键: {}，TTL: {} 秒。", redisKey, CACHE_TTL_SECONDS);
                } else {
                    logger.error("解析API响应中的 hourList 失败：hourList 不是一个数组。");
                }
            } catch (Exception e) {
                logger.error("解析API响应或存储数据到 Redis 时出错: {}", e.getMessage());
            }
        } else {
            logger.error("在 {} 次重试后未能获取天气数据。", MAX_RETRIES);
        }

        logger.info("天气数据更新任务完成，区域代码: {}", areaCode);
    }
}