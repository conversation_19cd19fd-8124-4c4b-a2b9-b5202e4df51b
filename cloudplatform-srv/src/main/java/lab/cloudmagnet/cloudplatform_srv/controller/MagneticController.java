package lab.cloudmagnet.cloudplatform_srv.controller;

import lab.cloudmagnet.cloudplatform_srv.dto.MagneticAlarmTotalStatsDTO;
import lab.cloudmagnet.cloudplatform_srv.dto.MagneticAlarmCountDTO;
import lab.cloudmagnet.cloudplatform_srv.dto.TotalAlarmCountDTO;
import lab.cloudmagnet.cloudplatform_srv.service.MagneticAlarmService;
import lab.cloudmagnet.cloudplatform_srv.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lab.cloudmagnet.cloudplatform_srv.dto.LocationAlarmCountDTO;

@Tag(name = "铁磁警报统计", description = "获取铁磁设备报警数据")
@RestController
@RequestMapping("/api/magnetic")
public class MagneticController {

    @Autowired
    private MagneticAlarmService magneticAlarmService;

    /**
     * 获取铁磁设备报警的总数量统计
     *
     * @param timeSpanType 统计的时间范围类型（例如："day", "week", "month", "year"）
     * @param locationIds 可选的点位ID列表
     * @return 包含报警总数量统计数据的DTO
     */
    @Operation(summary = "获取铁磁设备报警总数量统计", description = "### 接口功能详细说明：\n此接口用于获取铁磁设备在指定时间范围内的报警统计数据，并按风险等级进行细分。\n统计的时间范围通过 `timeSpanType` 参数控制，统计的点位范围通过 `locationIds` 参数控制。\n\n### 请求参数：\n-   `timeSpanType` (String): 必需。指定统计的时间范围类型。可能的取值包括但不限于：\n    -   \"day\": 统计当天的报警数量\n    -   \"week\": 统计当周的报警数量\n    -   \"month\": 统计当月的报警数量\n    -   \"year\": 统计当年的报警数量\n    \n    具体的支持范围类型取决于后端实现。\n-   `locationIds` (List<String>): 可选。指定要统计的点位ID列表。\n    -   不传参数时：统计所有点位的数据\n    -   传递点位列表时：统计列表中指定点位的数据总和\n    -   例如：locationIds=[1,3] 统计点位1和点位3的数据总和\n\n### 返回数据：\n接口返回一个标准化的响应结构 `ApiResponse<MagneticAlarmTotalStatsDTO>`。\n其中，`MagneticAlarmTotalStatsDTO` 包含以下字段：\n-   `safeCount` (Long): 指定时间范围内，状态为无风险（安全）的报警数量。\n-   `lowRiskCount` (Long): 指定时间范围内，状态为低风险的报警数量。\n-   `mediumRiskCount` (Long): 指定时间范围内，状态为中风险的报警数量。\n-   `highRiskCount` (Long): 指定时间范围内，状态为高风险的报警数量。\n")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功获取指定时间范围内的铁磁设备按风险等级划分的报警统计数据。返回包含各风险等级数量的DTO。", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MagneticAlarmTotalStatsDTO.class)))
    })
    @GetMapping("/alarm/stats")
    public ResponseEntity<ApiResponse<MagneticAlarmTotalStatsDTO>> getMagneticAlarmStats(
            @Parameter(description = "统计的时间范围类型，如: day, week, month, year", required = true) @RequestParam String timeSpanType,
            @Parameter(description = "统计的点位ID列表，可选") @RequestParam(required = false) List<String> locationIds) {
        MagneticAlarmTotalStatsDTO stats = magneticAlarmService.getMagneticAlarmStats(timeSpanType, locationIds);
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 获取铁磁设备报警次数统计
     *
     * @param timeSpanType 统计的时间范围类型（例如："day", "week", "month", "year"）
     * @param locationIds 可选的点位ID列表
     * @return 包含报警次数统计数据的DTO
     */
    @Operation(summary = "获取铁磁设备报警次数统计", description = "### 接口功能详细说明：\n此接口用于获取铁磁设备在指定时间范围内和指定点位（若指定）的报警次数。\n统计的时间范围通过 `timeSpanType` 参数控制，统计的点位范围通过 `locationIds` 参数控制。\n\n### 请求参数：\n-   `timeSpanType` (String): 必需。指定统计的时间范围类型。可能的取值包括但不限于：\n    -   \"day\": 统计当天的报警数量\n    -   \"week\": 统计当周的报警数量\n    -   \"month\": 统计当月的报警数量\n    -   \"year\": 统计当年的报警数量\n-   `locationIds` (String[]): 可选。指定统计的点位ID列表。若不指定，则统计所有点位。\n\n### 返回数据：\n接口返回一个标准化的响应结构 `ApiResponse<MagneticAlarmCountDTO>`。\n其中，`MagneticAlarmCountDTO` 包含以下字段：\n-   `alarmCount` (Long): 指定时间范围和点位范围内的报警次数。")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功获取指定时间范围和点位范围内的铁磁设备报警次数统计数据。", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MagneticAlarmCountDTO.class)))
    })
    @GetMapping("/alarm/count")
    public ResponseEntity<ApiResponse<MagneticAlarmCountDTO>> getMagneticAlarmCount(
            @Parameter(description = "统计的时间范围类型，如: day, week, month, year", required = true) @RequestParam String timeSpanType,
            @Parameter(description = "统计的点位ID列表，可选") @RequestParam(required = false) List<String> locationIds) {
        MagneticAlarmCountDTO count = magneticAlarmService.getMagneticAlarmCount(timeSpanType, locationIds);
        return ResponseEntity.ok(ApiResponse.success(count));
    }

    /**
     * 获取铁磁设备、氧浓度、温湿度传感器报警总次数统计
     *
     * @param timeSpanType 统计的时间范围类型（例如："day", "week", "month", "year"）
     * @param locationIds 可选的点位ID列表
     * @return 包含总报警次数统计数据的DTO
     */
    @Operation(summary = "获取铁磁设备、氧浓度、温湿度传感器报警总次数统计", description = "### 接口功能详细说明：\n此接口用于获取铁磁设备、氧浓度、温湿度传感器在指定时间范围内的报警总次数。\n统计的时间范围通过 `timeSpanType` 参数控制，统计的点位范围通过 `locationIds` 参数控制。\n\n### 请求参数：\n-   `timeSpanType` (String): 必需。指定统计的时间范围类型。可能的取值与 /api/magnetic/alarm/count 接口相同。\n-   `locationIds` (String[]): 可选。指定统计的点位ID列表。若不指定，则统计所有点位。\n\n### 返回数据：\n接口返回一个标准化的响应结构 `ApiResponse<TotalAlarmCountDTO>`。\n其中，`TotalAlarmCountDTO` 包含以下字段：\n-   `alarmCount` (Long): 指定时间范围和点位范围内的总报警次数。")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功获取指定时间范围和点位范围内的总报警次数统计数据。", content = @Content(mediaType = "application/json", schema = @Schema(implementation = TotalAlarmCountDTO.class)))
    })
    @GetMapping("/alarm/totalCount")
    public ResponseEntity<ApiResponse<TotalAlarmCountDTO>> getTotalAlarmTotalCount(
            @Parameter(description = "统计的时间范围类型，如: day, week, month, year", required = true) @RequestParam String timeSpanType,
            @Parameter(description = "统计的点位ID列表，可选") @RequestParam(required = false) List<String> locationIds) {
        TotalAlarmCountDTO totalCount = magneticAlarmService.getTotalAlarmTotalCount(timeSpanType, locationIds);
        return ResponseEntity.ok(ApiResponse.success(totalCount));
    }

    /**
     * 获取每一个有效点位的铁磁设备、氧浓度、温湿度传感器在指定时间范围内的报警次数
     *
     * @param timeSpanType 统计的时间范围类型（例如："day", "week", "month", "year"）
     * @return 包含每个点位报警次数统计数据的List
     */
    @Operation(summary = "获取各点位设备报警次数统计", description = "### 接口功能详细说明：\n此接口用于获取每一个有效点位的铁磁设备、氧浓度、温湿度传感器在指定时间范围内的报警次数。\n统计的时间范围通过 `timeSpanType` 参数控制。\n\n### 请求参数：\n-   `timeSpanType` (String): 必需。指定统计的时间范围类型。可能的取值包括但不限于：\n    -   \"day\": 统计当天的报警数量\n    -   \"week\": 统计当周的报警数量\n    -   \"month\": 统计当月的报警数量\n    -   \"year\": 统计当年的报警数量\n    \n    具体的支持范围类型取决于后端实现。\n\n### 返回数据：\n接口返回一个标准化的响应结构 `ApiResponse<List<LocationAlarmCountDTO>>`。\n其中，`LocationAlarmCountDTO` 包含以下字段：\n-   `alarmCount` (Long): 指定时间范围和该点位下的设备报警次数。\n-   `locationName` (String): 点位名称。")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功获取指定时间范围内各点位设备报警次数统计数据。", content = @Content(mediaType = "application/json", schema = @Schema(implementation = LocationAlarmCountDTO.class)))
    })
    @GetMapping("/alarm/countsByLocation")
    public ResponseEntity<ApiResponse<List<LocationAlarmCountDTO>>> getLocationAlarmCounts(
            @Parameter(description = "统计的时间范围类型，如: day, week, month, year", required = true) @RequestParam String timeSpanType) {
        List<LocationAlarmCountDTO> counts = magneticAlarmService.getLocationAlarmCounts(timeSpanType);
        return ResponseEntity.ok(ApiResponse.success(counts));
    }
}