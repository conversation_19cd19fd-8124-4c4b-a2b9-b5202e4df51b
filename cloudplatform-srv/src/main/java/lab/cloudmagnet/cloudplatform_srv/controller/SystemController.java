package lab.cloudmagnet.cloudplatform_srv.controller;

import lab.cloudmagnet.cloudplatform_srv.dto.CurrentTimeDTO;
import lab.cloudmagnet.cloudplatform_srv.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/api/system")
@Tag(name = "系统接口", description = "提供系统相关的服务接口")
public class SystemController {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ISO_INSTANT.withZone(ZoneOffset.UTC);

    /**
     * 获取服务器当前时间
     *
     * @return 包含服务器当前时间的标准化响应
     */
    @Operation(summary = "获取服务器当前时间", description = "### 接口功能详细说明：\n" +
            "此接口用于获取服务器当前的UTC时间。\n\n" +
            "### 请求参数：\n" +
            "无需提供任何请求参数。\n\n" +
            "### 返回数据：\n" +
            "接口返回一个标准化的响应结构 `ApiResponse<CurrentTimeDTO>`。\n" +
            "其中，`CurrentTimeDTO` 包含以下字段：\n" +
            "- `currentTime` (String): 服务器当前时间，格式为 ISO 8601 (例如: `2023-10-27T10:00:00Z`)。")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功获取服务器当前时间。\n" +
                    "返回的 `data` 字段包含一个 `CurrentTimeDTO` 对象，其中 `currentTime` 字段为 ISO 8601 格式的 UTC 时间。", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ApiResponse.class)))
    })
    @GetMapping("/currentTime")
    public ResponseEntity<ApiResponse<CurrentTimeDTO>> getCurrentTime() {
        String currentTimeIso = formatter.format(Instant.now());
        CurrentTimeDTO currentTimeDTO = new CurrentTimeDTO();
        currentTimeDTO.setCurrentTime(currentTimeIso);

        // 假设 ApiResponse 静态方法用于成功响应
        return ResponseEntity.ok(ApiResponse.success(currentTimeDTO));
    }
}
