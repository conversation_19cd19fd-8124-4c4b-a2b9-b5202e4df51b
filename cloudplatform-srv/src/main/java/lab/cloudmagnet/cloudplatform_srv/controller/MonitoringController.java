package lab.cloudmagnet.cloudplatform_srv.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lab.cloudmagnet.cloudplatform_srv.dto.LocationMonitoringStatusDTO;
import lab.cloudmagnet.cloudplatform_srv.response.ApiResponse;
import lab.cloudmagnet.cloudplatform_srv.service.MonitoringService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "监控服务接口", description = "提供设备监控状态相关的服务")
@RestController
@RequestMapping("/api/monitoring")
public class MonitoringController {

    @Autowired
    private MonitoringService monitoringService; // 取消注释并注入

    @Operation(
        summary = "获取所有点位的监控状态列表",
        description = "### 接口功能详细说明：\n此接口用于获取系统中所有注册点位及其关联的摄像头设备（类型SBXH=\"02\"）的综合监控状态。\n\n" +
                      "**数据来源/筛选条件：**\n" +
                      "- 点位信息: 从 `eqp_location` 表获取所有点位。\n" +
                      "- 摄像头设备: 从 `eqp_equipment` 表筛选 `DWBH` 对应各点位且 `SBXH` 为 \"02\" 的设备。\n\n" +
                      "**数据处理：**\n" +
                      "1. **RTSP URL提取**: 从每个摄像头设备的 `LJCS` 字段（XML格式：`<Connect><Url>...</Url></Connect>`）中解析提取RTSP URL。\n" +
                      "2. **摄像头状态检查**: 对每个提取到的RTSP URL，通过发送标准的RTSP `OPTIONS` 请求来检查摄像头的网络可达性和RTSP服务活跃性。请求超时设置为5秒。\n" +
                      "3. **点位监控状态判断**: 根据点位下所有摄像头的检查结果确定该点位的整体监控状态：\n" +
                      "    - **在线**: 该点位下的所有摄像头RTSP服务均正常响应。\n" +
                      "    - **部分在线**: 该点位下有部分摄像头RTSP服务正常，部分异常（无法连接、超时或RTSP服务未响应200 OK）。\n" +
                      "    - **离线**: 该点位下的所有摄像头RTSP服务均异常。\n" +
                      "    - **无设备**: 该点位下未关联任何摄像头设备（`SBXH`=\"02\")。\n" +
                      "    - **配置错误**: 该点位下有关联的摄像头设备，但未能从其`LJCS`字段中成功提取出有效的RTSP URL。\n" +
                      "    - **状态未知**: 出现预期之外的情况导致无法判断状态 (理论上不应出现)。\n\n" +
                      "### 请求参数：\n无需提供任何请求参数。\n\n" +
                      "### 返回数据：\n接口返回一个标准化的响应结构 `ApiResponse<List<LocationMonitoringStatusDTO>>`。\n" +
                      "其中，`List<LocationMonitoringStatusDTO>` 是一个列表，每个 `LocationMonitoringStatusDTO` 对象包含以下字段：\n" +
                      "- `locationId` (Integer): 点位ID (对应数据库中 `eqp_location.DWBH`)。\n" +
                      "- `locationName` (String): 点位名称 (对应数据库中 `eqp_location.DWMC`)。\n" +
                      "- `monitoringStatus` (String): 点位监控状态 (可能的值：\"在线\", \"部分在线\", \"离线\", \"无设备\", \"配置错误\", \"状态未知\")。\n" +
                      "- `rtspUrls` (List<String>): 该点位下所有摄像头设备（`SBXH`=\"02\"）的原始RTSP URL列表。如果点位无摄像头或摄像头无有效LJCS配置，则列表可能为空。"
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "成功获取所有点位的监控状态列表。列表可能为空（如果没有配置任何点位）。监控状态字段准确反映了各点位下摄像头的可访问性和服务状态。",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = lab.cloudmagnet.cloudplatform_srv.response.ApiResponse.class) // ApiResponse<List<LocationMonitoringStatusDTO>>
            )
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "服务器内部错误，例如数据库连接失败或RTSP检查过程中发生意外异常。",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = lab.cloudmagnet.cloudplatform_srv.response.ApiResponse.class)
            )
        )
    })
    @GetMapping("/location-status")
    public ResponseEntity<ApiResponse<List<LocationMonitoringStatusDTO>>> getLocationMonitoringStatus() {
        List<LocationMonitoringStatusDTO> statusList = monitoringService.getLocationMonitoringStatus();
        return ResponseEntity.ok(ApiResponse.success(statusList));
        // 暂时返回空数据，待Service层实现 // 注释掉这行
        // return ResponseEntity.ok(ApiResponse.success(null));
    }
} 