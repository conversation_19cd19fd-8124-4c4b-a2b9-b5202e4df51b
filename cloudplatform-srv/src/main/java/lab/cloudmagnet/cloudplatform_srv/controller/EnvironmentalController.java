package lab.cloudmagnet.cloudplatform_srv.controller;

import lab.cloudmagnet.cloudplatform_srv.dto.AverageEnvironmentalDataDTO;
import lab.cloudmagnet.cloudplatform_srv.dto.EnvironmentalTrendDTO;
import lab.cloudmagnet.cloudplatform_srv.dto.EnvironmentalAlarmCountDTO;
import lab.cloudmagnet.cloudplatform_srv.service.EnvironmentalDataService;
import lab.cloudmagnet.cloudplatform_srv.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.Parameter;

@RestController
@RequestMapping("/api/environmental")
@Tag(name = "环境数据接口", description = "提供环境数据的查询服务")
public class EnvironmentalController {

    @Autowired
    private EnvironmentalDataService environmentalDataService;

    /**
     * 获取所有点位实时的平均氧气浓度和平均环境温度和平均环境湿度
     *
     * @return 平均环境数据DTO
     */
    @Operation(summary = "获取实时平均环境数据", description = "### 接口功能详细说明：\n此接口用于获取所有符合特定条件的设备点位的实时平均环境数据，包括平均氧气浓度、平均环境温度和平均环境湿度。\n\n符合条件的设备通常指：\n- 设备状态正常（例如，状态码为 '0'）。\n- 有效标记为启用状态（例如，标记为 '0'）。\n- 设备类型属于特定的环境监测类型（例如，类型为 'A1' 或 'A3'）。\n\n接口会查询每个符合条件的设备的最新一条环境数据记录，并计算这些最新记录中相应指标（氧气浓度、温度、湿度）的平均值。\n氧气浓度数据会进行 '/ 100.0' 的缩放处理，温度和湿度数据会进行 '/ 10.0' 的缩放处理，以转换为实际单位值。\n\n### 请求参数：\n无需提供任何请求参数。这是一个 GET 请求，所有必要的数据筛选逻辑在后端实现。\n\n### 返回数据：\n接口返回一个标准化的响应结构 `ApiResponse<AverageEnvironmentalDataDTO>`。\n其中，`AverageEnvironmentalDataDTO` 包含以下字段：\n- `averageOxygenConcentration` (Double): 所有符合条件设备的最新平均氧气浓度值（单位：%%）。如果在符合条件的设备中未找到有效的氧气浓度数据，则可能为null。\n- `averageTemperature` (Double): 所有符合条件设备的最新平均环境温度值（单位：℃）。如果在符合条件的设备中未找到有效的温度数据，则可能为null。\n- `averageHumidity` (Double): 所有符合条件设备的最新平均环境湿度值（单位：%%RH）。如果在符合条件的设备中未找到有效的湿度数据，则可能为null。\n- `oxygenUnit` (String): 氧气浓度的单位，固定为 \"%%\".\n- `humidityUnit` (String): 湿度的单位，固定为 \"%%RH\".\n- `temperatureUnit` (String): 温度的单位，固定为 \"℃\".")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功获取所有符合条件设备点位的实时平均氧气浓度、环境温度和湿度数据。返回包含计算出的平均值和单位。如果某些指标无有效数据，对应平均值字段可能为null。", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AverageEnvironmentalDataDTO.class)))
    })
    @GetMapping("/average/latest")
    public ResponseEntity<ApiResponse<AverageEnvironmentalDataDTO>> getAverageLatestEnvironmentalData() {
        AverageEnvironmentalDataDTO averageLatestData = environmentalDataService.getAverageLatestEnvironmentalData();
        return ResponseEntity.ok(ApiResponse.success(averageLatestData));
    }

    /**
     * 获取当天每小时温湿度变化趋势
     *
     * @return 环境趋势DTO
     */
    @Operation(summary = "获取当天每小时温湿度趋势", description = "### 接口功能详细说明：\n此接口用于查询并展示当天（从0点开始）至当前最近的整点时刻（例如，如果当前时间是8点44分，则查询到8点）的环境温度和湿度变化趋势。数据来源于符合特定条件的设备点位。\n\n符合条件的设备通常指：\n- 设备状态正常（例如，状态码为 '0'）。\n- 有效标记为启用状态（例如，标记为 '0'）。\n- 设备类型属于特定的环境监测类型（例如，类型为 'A1' 或 'A3'）。\n\n接口会查询每个符合条件的设备在当天每个整点时刻（0点, 1点, ..., 当前整点）距离该整点时间最近的一条有效温湿度记录作为该小时的数据代表，并汇聚呈现。\n氧气浓度（虽然此接口主要关注温湿度）数据如果包含，通常会进行 '/ 100.0' 的缩放处理，温度和湿度数据会进行 '/ 10.0' 的缩放处理，以转换为实际单位值（尽管本接口侧重温湿度，但为保持描述一致性提及）。\n\n### 请求参数：\n无需提供任何请求参数。这是一个 GET 请求，所有必要的数据筛选和时间逻辑在后端实现。\n\n### 返回数据：\n接口返回一个标准化的响应结构 `ApiResponse<EnvironmentalTrendDTO>`。\n其中，`EnvironmentalTrendDTO` 包含一个 `hourlyData` 列表，该列表由 `HourlyEnvironmentalData` 对象组成。\n每个 `HourlyEnvironmentalData` 对象代表一个特定小时的数据，包含以下字段：\n- `hour` (int): 小时数，范围从0到23。\n- `temperature` (Double): 该小时对应的环境温度值（单位：℃）。如果在该小时或附近未找到有效温度数据，则可能为null。\n- `humidity` (Double): 该小时对应的环境湿度值（单位：%%RH）。如果在该小时或附近未找到有效湿度数据，则可能为null。")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功获取当天每小时温湿度变化趋势数据。返回包含当天0点到当前整点之间，每个小时的温湿度数据列表。对于每个小时，返回距离该整点时间最近的有效数据点。如果某小时无数据或附近无数据，对应温湿度字段可能为null。", content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvironmentalTrendDTO.class)))
    })
    @GetMapping("/trend/hourly")
    public ResponseEntity<ApiResponse<EnvironmentalTrendDTO>> getHourlyEnvironmentalTrend() {
        EnvironmentalTrendDTO trendData = environmentalDataService.getHourlyEnvironmentalTrend();
        return ResponseEntity.ok(ApiResponse.success(trendData));
    }

    /**
     * 获取环境传感器设备报警次数统计
     *
     * @param timeSpanType 统计的时间范围类型（例如："day", "week", "month", "year"）
     * @param locationIds  可选的点位ID列表
     * @return 包含报警次数统计数据的DTO
     */
    @Operation(summary = "获取环境传感器设备报警次数统计", description = "### 接口功能详细说明：\n此接口用于获取氧浓度和温湿度传感器设备在指定时间范围内和指定点位（若指定）的报警次数。\n统计的时间范围通过 `timeSpanType` 参数控制，统计的点位范围通过 `locationIds` 参数控制。\n\n### 请求参数：\n- `timeSpanType` (String): 必需。指定统计的时间范围类型。可能的取值包括但不限于：\n    - \"day\": 统计当天的报警数量\n    - \"week\": 统计当周的报警数量\n    - \"month\": 统计当月的报警数量\n    - \"year\": 统计当年的报警数量\n- `locationIds` (String[]): 可选。指定统计的点位ID列表。若不指定，则统计所有点位。\n\n### 返回数据：\n接口返回一个标准化的响应结构 `ApiResponse<EnvironmentalAlarmCountDTO>`。\n其中，`EnvironmentalAlarmCountDTO` 包含以下字段：\n- `alarmCount` (Long): 指定时间范围和点位范围内的报警次数。")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功获取指定时间范围和点位范围内的环境传感器设备报警次数统计数据。", content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvironmentalAlarmCountDTO.class)))
    })
    @GetMapping("/alarm/count")
    public ResponseEntity<ApiResponse<EnvironmentalAlarmCountDTO>> getEnvironmentalAlarmCount(
            @Parameter(description = "统计的时间范围类型，如: day, week, month, year", required = true) @RequestParam String timeSpanType,
            @Parameter(description = "统计的点位ID列表，可选") @RequestParam(required = false) String[] locationIds) {
        EnvironmentalAlarmCountDTO count = environmentalDataService.getEnvironmentalAlarmCount(timeSpanType,
                locationIds);
        return ResponseEntity.ok(ApiResponse.success(count));
    }
}