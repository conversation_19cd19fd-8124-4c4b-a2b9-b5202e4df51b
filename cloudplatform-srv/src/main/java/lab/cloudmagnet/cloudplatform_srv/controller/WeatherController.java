package lab.cloudmagnet.cloudplatform_srv.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lab.cloudmagnet.cloudplatform_srv.dto.CurrentWeatherDTO;
import lab.cloudmagnet.cloudplatform_srv.response.ApiResponse;
import lab.cloudmagnet.cloudplatform_srv.schedule.WeatherUpdateTask;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Optional;

@Tag(name = "天气数据接口", description = "获取天气和气温数据")
@RestController
@RequestMapping("/api/weather")
public class WeatherController {

    @Value("${weather.api.appcode}")
    private String appcode;

    @Value("${weather.api.areaCode:320104}") // 注入默认 areaCode
    private String defaultAreaCode;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private WeatherUpdateTask weatherUpdateTask; // 注入定时任务 Bean

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final String REDIS_KEY_PREFIX = "weather:forecast:";

    /**
     * 获取指定城市或城市代码的天气和气温数据
     *
     * @param city     城市名称
     * @param areaCode 城市代码
     * @return 天气数据（原始字符串）
     */
    @Operation(summary = "获取当前天气 (直接调用第三方API)", description = "根据城市名称或城市代码直接调用第三方API获取当前天气和气温等数据，用于测试")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功获取天气数据", content = @Content(mediaType = "application/json",
                    // 注意：这里假设第三方API直接返回JSON字符串，如果返回结构复杂，可能需要定义一个Schema
                    schema = @Schema(implementation = String.class))),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "获取天气数据失败")
    })
    @GetMapping("/current")
    public ResponseEntity<String> getCurrentWeather(
            @Parameter(description = "要查询天气的城市名称", required = false, example = "秦淮") @RequestParam(value = "city", required = false) String city,
            @Parameter(description = "要查询天气的城市代码", required = false, example = "320104") @RequestParam(value = "areaCode", required = false) String areaCode) {

        // 验证 city 或 areaCode 至少提供一个，但不能同时提供
        if ((city == null || city.isEmpty()) && (areaCode == null || areaCode.isEmpty())) {
            return ResponseEntity.badRequest().body("city或areaCode必须提供一个.");
        }
        // 只能提供 city 或 areaCode 中的一个
        if ((city != null && !city.isEmpty()) && (areaCode != null && !areaCode.isEmpty())) {
            return ResponseEntity.badRequest().body("city或areaCode只能提供一个.");
        }

        String host = "https://ali-weather.showapi.com"; // 固定 host
        String path = "/hour24"; // 固定 path

        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "APPCODE " + appcode);

        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(host + path);

        if (city != null && !city.isEmpty()) {
            builder.queryParam("area", city);
        } else {
            builder.queryParam("areaCode", areaCode);
        }

        HttpEntity<String> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    builder.toUriString(),
                    HttpMethod.GET,
                    entity,
                    String.class);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body("获取天气数据出错: " + e.getMessage());
        }
    }

    /**
     * 从缓存获取指定城市或城市代码的当前天气和气温数据
     *
     * @param areaCode 城市代码
     * @return 只包含天气和气温的DTO
     */
    @Operation(summary = "获取当前天气 (从缓存)", description = "### 接口功能详细说明：\n" +
            "此接口用于根据城市代码从Redis缓存中获取该区域当前时间最接近的小时天气预测数据。\n" +
            "数据来源于后台定时任务周期性从第三方API获取并存入Redis的24小时预测数据。\n\n" +
            "**时间匹配规则：**\n" +
            "- 如果当前请求时间的分钟部分在 0 到 30 之间，返回当前小时的预测数据。\n" +
            "- 如果当前请求时间的分钟部分在 31 到 59 之间，返回下一小时的预测数据。\n\n" +
            "**数据来源：**\n" +
            "Redis缓存，Key为 `weather:forecast:<areaCode>`。\n\n" +
            "### 请求参数：\n" +
            "- `areaCode` (String): 可选。需要查询天气的区域代码。如果未提供，则使用后台配置的默认区域代码。\n\n" +
            "### 返回数据：\n" +
            "接口返回一个标准化的响应结构 `ApiResponse<CurrentWeatherDTO>`。\n" +
            "其中，`CurrentWeatherDTO` 包含以下字段：\n" +
            "- `weather` (String): 天气描述。\n" +
            "- `temperature` (String): 气温（单位：℃）。\n" +
            "如果缓存数据暂不可用（未命中、过期或定时任务失败），`data` 字段将包含一个空的 `CurrentWeatherDTO` 对象，`message` 字段为 '天气数据暂不可用'。")
    @ApiResponses(value = {
            // 这里的 implementation 通常指向 ApiResponse
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功获取天气数据或缓存数据暂不可用。\n"
                    +
                    "- 如果缓存中存在匹配当前时间的数据，返回具体的天气和气温。\n" +
                    "- 如果缓存未命中、数据过期或定时任务失败，返回一个空的 `CurrentWeatherDTO` (`weather` 和 `temperature` 为空字符串)，并在 `message` 中说明数据暂不可用。", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ApiResponse.class))),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误，例如提供了无效的 areaCode 格式。"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "缓存中未找到该区域天气数据（此情况已在 200 响应中通过 message 和空 data 处理）。"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "处理缓存数据或解析时出错。")
    })
    @GetMapping("/currentFromCache")
    public ResponseEntity<ApiResponse<CurrentWeatherDTO>> getCurrentWeatherFromCache(
            @Parameter(description = "要查询天气的城市代码", required = false, example = "320104") @RequestParam(value = "areaCode", required = false) String areaCode) {

        // 如果请求未提供 areaCode 或为空，使用默认值
        String effectiveAreaCode = (areaCode == null || areaCode.isEmpty()) ? this.defaultAreaCode : areaCode;

        String redisKey = REDIS_KEY_PREFIX + effectiveAreaCode;
        String hourListJsonString = redisTemplate.opsForValue().get(redisKey);

        if (hourListJsonString != null) {
            try {
                JsonNode hourList = objectMapper.readTree(hourListJsonString);

                if (hourList.isArray()) {
                    LocalDateTime now = LocalDateTime.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHH");

                    // 根据当前分钟确定目标小时 (0-30取当前小时，31-59取下一小时)
                    LocalDateTime targetTime = now.getMinute() <= 30 ? now : now.plusHours(1);
                    String targetTimeStr = targetTime.format(formatter) + "00"; // 补上分钟秒钟

                    Optional<JsonNode> matchedHourData = Optional.empty();
                    for (JsonNode hourData : hourList) {
                        if (hourData.has("time") && hourData.get("time").asText().equals(targetTimeStr)) {
                            matchedHourData = Optional.of(hourData);
                            break;
                        }
                    }

                    if (matchedHourData.isPresent()) {
                        JsonNode hourData = matchedHourData.get();
                        String weather = hourData.has("weather") ? hourData.get("weather").asText() : "未知天气";
                        String temperature = hourData.has("temperature") ? hourData.get("temperature").asText()
                                : "未知气温";
                        CurrentWeatherDTO currentWeather = new CurrentWeatherDTO(weather, temperature);
                        return ResponseEntity.ok(ApiResponse.success("成功获取天气数据", currentWeather));
                    } else {
                        // 未找到匹配的时间条目，可能是数据已过期或格式问题
                        // 返回一个基础结构或空数据，让前端保持当前显示
                        return ResponseEntity.ok(ApiResponse.success("天气数据暂不可用", new CurrentWeatherDTO("", "")));
                    }

                } else {
                    // Redis中的数据格式不正确
                    return ResponseEntity.status(500).body(ApiResponse.error("缓存数据格式错误."));
                }

            } catch (Exception e) {
                // 数据解析或处理出错
                e.printStackTrace();
                return ResponseEntity.status(500).body(ApiResponse.error("处理缓存数据出错: " + e.getMessage()));
            }
        } else {
            // Redis中没有该区域的天气数据
            // 返回一个基础结构或空数据，让前端保持当前显示
            return ResponseEntity.ok(ApiResponse.success("天气数据暂不可用", new CurrentWeatherDTO("", "")));
        }
    }

    // 临时接口，用于手动触发天气数据更新定时任务
    @Operation(summary = "手动触发天气数据更新", description = "手动触发 WeatherUpdateTask 定时任务，用于测试")
    @GetMapping("/triggerWeatherUpdate")
    public ResponseEntity<String> triggerWeatherUpdateTask() {
        weatherUpdateTask.updateWeatherData();
        return ResponseEntity.ok("天气数据更新任务已触发。");
    }
}
