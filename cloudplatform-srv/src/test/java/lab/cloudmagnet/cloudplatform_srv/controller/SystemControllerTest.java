package lab.cloudmagnet.cloudplatform_srv.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import lab.cloudmagnet.cloudplatform_srv.dto.CurrentTimeDTO;
import lab.cloudmagnet.cloudplatform_srv.response.ApiResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.options;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SystemController 单元测试
 * 测试系统相关的 API 端点
 */
@WebMvcTest(SystemController.class)
class SystemControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 测试获取当前系统时间接口
     */
    @Test
    void testGetCurrentTime() throws Exception {
        // 执行请求
        MvcResult result = mockMvc.perform(get("/api/system/currentTime")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.statusCode").value(200))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.currentTime").exists())
                .andReturn();

        // 解析响应
        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<CurrentTimeDTO> response = objectMapper.readValue(
            responseContent, 
            objectMapper.getTypeFactory().constructParametricType(
                ApiResponse.class, 
                CurrentTimeDTO.class
            )
        );

        // 验证响应数据
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getData());
        assertNotNull(response.getData().getCurrentTime());

        // 验证时间格式
        String timeString = response.getData().getCurrentTime();
        assertDoesNotThrow(() -> {
            LocalDateTime.parse(timeString, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }, "时间格式应该是 yyyy-MM-dd HH:mm:ss");

        // 验证时间的合理性（应该是当前时间附近）
        LocalDateTime responseTime = LocalDateTime.parse(timeString, 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime now = LocalDateTime.now();
        
        // 允许1分钟的误差
        assertTrue(responseTime.isAfter(now.minusMinutes(1)) && 
                  responseTime.isBefore(now.plusMinutes(1)),
                  "返回的时间应该接近当前时间");
    }

    /**
     * 测试系统时间接口的响应格式
     */
    @Test
    void testCurrentTimeResponseFormat() throws Exception {
        mockMvc.perform(get("/api/system/currentTime"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").isBoolean())
                .andExpect(jsonPath("$.statusCode").isNumber())
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.currentTime").isString());
    }

    /**
     * 测试错误的请求路径
     */
    @Test
    void testInvalidEndpoint() throws Exception {
        mockMvc.perform(get("/api/system/invalidEndpoint"))
                .andExpect(status().isNotFound());
    }

    /**
     * 测试 CORS 预检请求
     */
    @Test
    void testCorsPreflightRequest() throws Exception {
        mockMvc.perform(options("/api/system/currentTime")
                .header("Origin", "http://localhost:3000")
                .header("Access-Control-Request-Method", "GET")
                .header("Access-Control-Request-Headers", "Content-Type"))
                .andExpect(status().isOk())
                .andExpect(header().exists("Access-Control-Allow-Origin"))
                .andExpect(header().exists("Access-Control-Allow-Methods"));
    }
}

/**
 * 测试运行说明：
 * 
 * 1. 运行单个测试类：
 *    mvn test -Dtest=SystemControllerTest
 * 
 * 2. 运行特定测试方法：
 *    mvn test -Dtest=SystemControllerTest#testGetCurrentTime
 * 
 * 3. 生成测试报告：
 *    mvn test jacoco:report
 * 
 * 4. 查看测试覆盖率：
 *    打开 target/site/jacoco/index.html
 */
