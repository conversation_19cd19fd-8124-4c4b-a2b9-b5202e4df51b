#!/bin/bash

# CloudPlatform Server 构建脚本
# 用法: ./build.sh --platform <platform> --version <version>
# 示例: ./build.sh --platform linux/amd64 --version 1.0.2

set -e

# 默认值
PLATFORM=""
VERSION=""
IMAGE_NAME="cloudmagnet/cloudplatform-srv"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --platform|-p)
            PLATFORM="$2"
            shift 2
            ;;
        --version|-v)
            VERSION="$2"
            shift 2
            ;;
        --help|-h)
            echo "用法: $0 --platform <platform> --version <version>"
            echo "示例: $0 --platform linux/amd64 --version 1.0.2"
            echo ""
            echo "参数:"
            echo "  --platform, -p    目标平台 (如: linux/amd64, linux/arm64)"
            echo "  --version, -v     版本号"
            echo "  --help, -h        显示帮助信息"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 检查必需参数
if [[ -z "$PLATFORM" ]]; then
    echo "错误: 必须指定平台 (--platform)"
    echo "使用 --help 查看帮助信息"
    exit 1
fi

if [[ -z "$VERSION" ]]; then
    echo "错误: 必须指定版本 (--version)"
    echo "使用 --help 查看帮助信息"
    exit 1
fi

echo "=== CloudPlatform Server 构建 ==="
echo "平台: $PLATFORM"
echo "版本: $VERSION"
echo "镜像: $IMAGE_NAME"
echo ""

# 构建 Docker 镜像
echo "开始构建..."
docker buildx build \
    --platform "$PLATFORM" \
    --tag "$IMAGE_NAME:$VERSION" \
    --tag "$IMAGE_NAME:latest" \
    --build-arg SKIP_TESTS=true \
    --load \
    .

echo ""
echo "✅ 构建完成!"
echo "镜像标签:"
echo "  - $IMAGE_NAME:$VERSION"
echo "  - $IMAGE_NAME:latest"
echo ""

# 导出镜像为 tar 包
TAR_FILE="../cloudplatform-srv-$VERSION.tar"
echo "📦 正在导出镜像为 tar 包..."
docker save -o "$TAR_FILE" "$IMAGE_NAME:$VERSION"

if [ $? -eq 0 ]; then
    echo "✅ 镜像已导出为: $TAR_FILE"
    echo "文件大小: $(du -h "$TAR_FILE" | cut -f1)"
else
    echo "❌ 导出镜像失败"
    exit 1
fi

echo ""
echo "运行示例:"
echo "  docker run -d --name cloudplatform-srv --platform $PLATFORM -p 8081:8081 $IMAGE_NAME:$VERSION"
echo ""
echo "导入镜像示例:"
echo "  docker load -i $TAR_FILE"
